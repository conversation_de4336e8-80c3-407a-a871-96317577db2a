---
sort: 1
---
# Intro

KindleEar是一个开源免费网络应用，可以部署在大多数支持Python的托管平台，包括但不限于Google Cloud、Heroku、VPS、Ubuntu、树莓派等，主要功能是自动定期通过RSS收集网络文章然后制作成图文并茂的电子书推送至你的Kindle或其他电子阅读设备。    
Kindle修改和提取了著名电子书管理软件Calibre的epub/mobi生成模块，除了直接输入RSS链接地址即可推送外，还直接支持Calibre的Recipe格式（抓取各种网站信息的Python脚本），已经内置一千多个Recipe，涵盖多种语种，除此之外，您也可以编写自己的Recipe然后在管理页面上传到KindleEar。       
KindleEar同时提供一个Chrome/Edge浏览器扩展，可以不写代码生成任意网站的爬虫Recipe脚本，可以无门槛任意订阅不支持RSS的网站。   



[下一页](https://cdhigh.github.io/KindleEar/Chinese/config.html)
