{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Edit account") }} - KindleEar</title>
{% endblock -%}

{% block content -%}
<div class="main">
  {% if tips -%}
  <div class="notice-box">{{tips|safe}}</div>
  {% endif -%}
  <form class="pure-form pure-form-aligned" action="/account/change/{{user.name}}" method="POST">
    <fieldset>
      <legend>{{_('Edit account')}}</legend>
      <div class="pure-control-group">
        <label for="orgpwd">{{_("Old password")}}</label>
        <input type="password" name="orgpwd" class="pure-u-1 pure-u-sm-1-2" autofocus />
      </div>
      <div class="pure-control-group">
        <label for="password1">{{_("New password")}}</label>
        <input type="password" name="password1" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group">
        <label for="password2">{{_("Confirm password")}}</label>
        <input type="password" name="password2" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group">
        <label for="email">{{_("Email")}}</label>
        <input type="email" name="email" class="pure-u-1 pure-u-sm-1-2" value="{{user.cfg('email')}}" required />
      </div>
      <div class="pure-control-group">
        <label>{{_("Share key")}}</label>
        <input type="text" name="shareKey" class="pure-u-1 pure-u-sm-1-2" value="{{shareKey}}" required />
      </div>
      <input type="hidden" name="username" value="{{user.name}}" />
      <br/>
      <div style="text-align:center;">
        <button type="submit" class="pure-button pure-button-primary pure-input-rounded">{{_("Confirm Change")}}</button>
      </div>
    </fieldset>
  </form>
</div>
{% endblock -%}