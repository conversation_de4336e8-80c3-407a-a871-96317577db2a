{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Signup") }} - KindleEar</title>
{% endblock -%}

{% block header_loginfo -%}
{% endblock %}

{% block menubar -%}
{% endblock -%}

{% block content -%}
<div class="main">
  {% if tips -%}
  <div class="notice-box">{{tips|safe}}</div>
  {% endif -%}
  <form action="" method="POST" class="pure-form pure-form-aligned">
    <fieldset>
      <legend>{{_("Signup")}}</legend>
      <div class="pure-control-group">
        <label for="username">{{_("Username")}}</label>
        <input name="username" type="text"class="pure-u-1 pure-u-sm-1-2" autofocus required />
      </div>
      <div class="pure-control-group">
        <label for="password1">{{_("Password")}}</label>
        <input name="password1" type="password" class="pure-u-1 pure-u-sm-1-2" required />
      </div>
      <div class="pure-control-group">
        <label for="password2">{{_("Confirm password")}}</label>
        <input name="password2" type="password" class="pure-u-1 pure-u-sm-1-2" required />
      </div>
      <div class="pure-control-group">
        <label for="email">{{_("Email")}}</label>
        <input name="email" type="email" class="pure-u-1 pure-u-sm-1-2" required />
      </div>
      {% if inviteNeed %}
      <div class="pure-control-group">
        <label for="invite_code">{{_("Invitation code")}}</label>
        <input name="invite_code" type="text" class="pure-u-1 pure-u-sm-1-2" required />
      </div>
      {% endif %}
      <div class="pure-control-group" style="text-align:center;">
        <button type="submit" class="pure-button pure-button-primary pure-input-rounded">{{_("Signup")}}</button>
      </div>
    </fieldset>
  </form>
</div>
{% endblock -%}
