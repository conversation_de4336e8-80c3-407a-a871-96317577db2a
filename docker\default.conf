server {
    listen 80 default_server;
    listen [::]:80 default_server;
    charset utf-8;
    client_max_body_size 32M;
    server_name localhost;

    # uncomment this section if https is need
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    ssl_certificate     /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;
    if ($scheme = http) {
       return 301 https://$http_host$request_uri;
    }
    
    location / {
        proxy_pass http://kindleear:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
