runtime: python310
service: default

#F1: 384MB/600MHz/automatic_scaling
#F2: 768MB/1.2GHz/automatic_scaling
#F4: 1536MB/2.4GHz/automatic_scaling
#F4_1G: 3072MB/2.4GHz/automatic_scaling
instance_class: F1
automatic_scaling:
 max_instances: 2
 min_instances: 0
 max_idle_instances: 1
 min_idle_instances: 0

app_engine_apis: true
entrypoint: gunicorn -b :$PORT -w 2 main:app

inbound_services:
- mail
- mail_bounce

env_variables:
  KE_TEMP_DIR: "/tmp"
  DATABASE_URL: "datastore"

handlers:
- url: /robots.txt
  static_files: application/static/robots.txt
  upload: application/static/robots.txt

- url: /favicon.ico
  static_files: application/static/favicon.ico
  upload: application/static/favicon.ico
  mime_type: image/x-icon

- url: /static
  static_dir: application/static

- url: /images
  static_dir: application/images

- url: /recipes
  static_dir: application/recipes

- url: /.*
  secure: always
  redirect_http_response_code: 301
  script: auto
