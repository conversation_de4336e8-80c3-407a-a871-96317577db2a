---
sort: 1
---
# Intro

KindleEar is an open-source and free web application that can be deployed on most hosting platforms that support Python, including but not limited to Google Cloud, Heroku, VPS, Ubuntu, Raspberry Pi, etc. Its main function is to automatically collect web articles via RSS at regular intervals and compile them into illustrated eBooks, which are then pushed to your Kindle or other e-reader devices.    

KindleEar has modified and extracted the epub/mobi generation module of the famous e-book management software Calibre. In addition to being able to push by simply inputting RSS feed URLs, it also directly supports Calibre's Recipe format (Python scripts that capture information from various websites). KindleEar has built-in over a thousand Recipes covering multiple languages. Besides, users can also write their own Recipes and upload them to KindleEar via the management page.    

KindleEar also provides a Chrome/Edge browser extension that allows users to generate recipes for any website without writing any code, enabling easy subscription to websites that do not support RSS feeds.     


[Next page](https://cdhigh.github.io/KindleEar/English/config.html)
