#!/usr/bin/env python
# vim:fileencoding=utf-8
# License: GPL v3 Copyright: 2019, <PERSON> <<EMAIL>>

from http.client import (responses, HTTPConnection, HTTPSConnection,
        BAD_REQUEST, FOUND, F<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTP_VERSION_NOT_SUPPORTED,
        INTERNAL_SERVER_ERROR, METHOD_NOT_ALLOWED, MOVED_PERMANENTLY,
        NOT_FOUND, NOT_IMPLEMENTED, NOT_MODIFIED, OK, PARTIAL_CONTENT,
        PRECONDITION_FAILED, REQUEST_ENTITY_TOO_LARGE, REQUEST_URI_TOO_LONG,
        REQUESTED_RANGE_NOT_SATISFIABLE, REQUEST_TIMEOUT, SEE_OTHER,
        SERVICE_UNAVAILABLE, UNAUTHORIZED, _CS_IDLE, _CS_REQ_SENT, PRECONDITION_REQUIRED, UNPROCESSABLE_ENTITY)

if False:
    responses, HTTPConnection, HTT<PERSON><PERSON>onnection, BAD_REQUEST, FOUND, FOR<PERSON><PERSON>DEN, HTTP_VERSION_NOT_SUPPORTED, INTERNAL_SERVER_ERROR, METHOD_NOT_ALLOWED
    MOVED_PERMANENTLY, NOT_FOUND, NOT_IMPLEMENTED, NOT_MODIFIED, OK, PARTIAL_CONTENT, PRECONDITION_FAILED, REQUEST_ENTITY_TOO_LARGE, REQUEST_URI_TOO_LONG
    REQUESTED_RANGE_NOT_SATISFIABLE, REQUEST_TIMEOUT, SEE_OTHER, SERVICE_UNAVAILABLE, UNAUTHORIZED, _CS_IDLE, _CS_REQ_SENT
    PRECONDITION_REQUIRED, UNPROCESSABLE_ENTITY
