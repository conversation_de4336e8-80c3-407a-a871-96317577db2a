{% extends "adv_base.html" %}
{% block titleTag -%}
<title>{{ _("Cover image") }} - KindleEar</title>
{% endblock -%}

{% block advcontent -%}
<form class="pure-form pure-form-aligned" id="uploadForm" action="{{uploadUrl}}" method="POST" enctype="multipart/form-data" onsubmit="return false;">
  <fieldset>
    <legend>{{_("Upload cover image")}}</legend>
    <p><small>{{_("Upload cover images from local with an aspect ratio of approximately 0.625.")}}</small></p>
    <div class="box-list">
      <div class="pure-control-group" style="margin-top: 10px">
        <label>{{_("Include cover")}}</label>
        <select class="pure-u-1 pure-u-sm-9-24" name="enable" id="enableCover">
          <option value="" {% if not covers.get('enable', '') %}selected="selected"{% endif %}>{{_("Disable")}}</option>
          <option value="1" {% if covers.get('enable') %}selected="selected"{% endif %}>{{_("Enable")}}</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Rule for cover")}}</label>
        <select class="pure-u-1 pure-u-sm-9-24" name="order" id="coverOrder">
          <option value="random" {% if covers.get('order', 'random') == 'random' %}selected="selected"{% endif %}>{{_("Random")}}</option>
          <option value="weekday" {% if covers.get('order') == 'weekday' %}selected="selected"{% endif %}>{{_("Weekday")}}</option>
        </select>
      </div>
      <hr style="width: 90%" />
      <div id="preview" class="imgupBox">
        <div class="imgFileUploade">
          <div class="imgAll">
            <ul>
              {% for idx in range(7) -%}
              <li>
                <div class="image-wrapper">
                  <img id="img{{idx}}" src="{{covers.get('cover' + idx|string)|safe}}" />
                  <i id="delImg{{idx}}" class="delImg" style="display:none" >X</i>
                </div>
                <div id="addUpload{{idx}}" class="top-image" onclick="ChooseCoverImage({{idx}})">
                  <img src="/static/add-upload.png" />
                </div>
                <input type="file" id="imgFile{{idx}}" name="imgFile{{idx}}" style="display:none" accept="image/gif,image/jpeg,image/jpg,image/png">
              </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div style="text-align:center;">
      <input type="button" value="{{ _('Upload/Update') }}" id="fileSubmit" class="pure-button pure-button-primary pure-input-rounded" onclick="startUploadCoversToServer('{{uploadUrl}}')" />
    </div>
    <br/>
    <div id="up_cover_progress" style="display:none;"><div id="up_cover_progress_bar"></div></div>
  </fieldset>
</form>
{% endblock -%}
{% block js -%}
<script type="text/javascript">
  var cover_images = {{jsonCovers|safe}};
  $(document).ready(function() {
    InitCoversWidgets();
  });
</script>
{% endblock -%}
