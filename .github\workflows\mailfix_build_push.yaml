name: Mailfix Docker Build and Push
permissions: read-all

on:
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Build and push Docker image
        run: |
          cd docker/postfix
          docker buildx build --push --platform=linux/amd64,linux/arm64 -t kindleear/mailfix -f Dockerfile .
