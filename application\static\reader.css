* {
  padding: 0px;
  margin: 0px;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}
html, body {
  padding: 0px;
  margin: 0px;
  height: 100%;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
body::-webkit-scrollbar {
  display: none;
  width: 0px;
  height: 0px;
  background: transparent;
}
body::-webkit-scrollbar-thumb {
  background: transparent;
  display: none;
}

.container {
  margin: 0px;
  padding: 0px;
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}
.content {
  margin: 0px;
  padding: 0px;
  overflow-y: auto;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  -webkit-overflow-scrolling: auto;
}
.embedded-iframe {
  padding: 10px 20px 10px 10px;
  margin: 0px;
  width: 100%;
  height: 100%;
  border: none;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  overflow: hidden;
  hyphens: auto;
  box-sizing: border-box;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.pos-indicator {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 15px;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
}
.corner-dict-hint {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 50px 50px 0 0;
  border-color: #291E1E transparent transparent transparent;
}
.corner-dict-hint svg {
  position: absolute;
  top: -50px;
  left: 3px;
  width: 30px;
  height: 30px;
  fill: white;
}

.navbar {
  position: fixed;
  left: 0px;
  top: 0px;
  margin: 0px;
  padding: 5px 10px 5px 5px;
  background-color: white;
  overflow-x: hidden;
  border: 1px solid #ccc;
  box-shadow: 1px 0px 3px #ccc;
  cursor: pointer;
}

/* small screen */
@media (max-width: 599px) {
  ::-webkit-scrollbar {
    display: none;
    width: 0px;
    height: 0px;
    background: transparent;
  }
  ::-webkit-scrollbar-thumb {
    background: transparent;
    display: none;
  }
  .navbar {
    display: none;
    right: 20px;
    bottom: 0px;
  }
  .container {
    margin: 0px 10px 0px 0px;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }
  .pos-indicator {
    display: block;
  }
  .nav-footer {
    height: 60px;
  }
  .nav-footer button {
    margin: 0px 3px;
    width: 30px;
    height: 30px;
  }
}

/* small screen, ink mode, pw3/voyage=1448x1072 */
@media (min-width: 600px) and (max-width: 1072px) {
  ::-webkit-scrollbar {
    display: none;
    width: 0px;
    height: 0px;
    background: transparent;
  }
  ::-webkit-scrollbar-thumb {
    background: transparent;
    display: none;
  }
  .navbar {
    display: none;
    right: 20px;
    bottom: 0px;
  }
  .container {
    margin: 0px 10px 0px 0px;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }
  .pos-indicator {
    display: block;
  }
  .nav-footer {
    height: 80px;
  }
  .nav-footer button {
    margin: 0px 10px;
    width: 50px;
    height: 50px;
  }
}
/* Desktop Styles */
@media (min-width: 1073px) {
  .navbar {
    display: block;
    width: 400px;
    height: 100%;
  }
  .container {
    width: auto;
    margin-left: 410px; /* width of .navbar */
  }

  .pos-indicator, .nav-indicator, button#closeNavBtn {
    display: none;
  }
  .nav-footer {
    height: 80px;
  }
  .nav-footer button {
    margin: 0px 10px;
    width: 40px;
    height: 40px;
  }
}

.nav-content {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  padding-bottom: 30px;
  position: relative;
  scroll-behavior: smooth;
}
.nav-indicator {
  position: absolute;
  top: 5px;
  right: 0px;
  width: 10px;
  height: 30px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
}
.nav-date {
  padding: 10px 5px;
  cursor: pointer;
  font-weight: bold;
  word-break: nowrap;
  white-space: nowrap;
  border: 1px solid #aaa;
  background-color: #eee;
}
.nav-date:hover {
  background-color: #e8e8e8;
}
.nav-date span {
  padding: 0px;
  vertical-align: middle;
}
.nav-book {
  cursor: pointer;
  font-weight: bold;
  word-break: nowrap;
  white-space: nowrap;
  display: none;
}
.nav-book-title {
  cursor: pointer;
  border-bottom: 1px dotted #ccc;
  padding: 10px;
  background-color: #f8f8f8;
}
.nav-book-title:hover {
  background-color: #e8e8e8;
}
.nav-book-chk {
  margin-right: 10px;
}
.nav-article {
  display: none;
}
.nav-title {
  font-weight: normal;
  padding: 10px 10px 10px 10px;
  cursor: pointer;
  border-bottom: 1px dotted #ccc;
  position: relative;
  width: 100%;
  display: table;
  background-color: white;
}
.nav-title:hover {
  background-color: #e8e8e8;
}
.nav-title > div {
  display: table-cell;
  vertical-align: middle;
}
.nav-title-icon {
  vertical-align: middle;
  margin-right: 5px;
  font-size: 0.9em;
}
.nav-title-text {
  font-size: 0.9em;
  margin-left: 10px;
}
.tree-icon {
  margin-right: 10px;
  font-size: 0.8em;
}
.nav-footer {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 99%;
  /* height: 80px; */ /* in media query */
  padding: 5px 5px 10px 5px;
  background-color: white;
  border-top: 1px solid #ccc;
  box-sizing: border-box;
  overflow: hidden;
  cursor: pointer;
}
.nav-footer::after {
  content: "";
  display: table;
  clear: both;
}
.nav-footer button {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  padding: 0px;
  border: none;
  background: none;
  /*margin: 0px 10px;
  width: 50px;
  height: 50px;*/ /* in media query */
  cursor: pointer;
}
.nav-footer .left-float {
  float: left;
}
.nav-footer .right-float {
  float: right;
}
.nav-footer svg {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  height: 100%;
}
.nav-popmenu {
  position: absolute;
  left: 0px;
  bottom: 80px; /*heihgt of nav-footer*/
  width: 400px;
  padding: 0px 10px 0px 10px;
  border: 1px solid #ccc;
  box-shadow: 1px 0px 3px #ccc;
  vertical-align: middle;
  display: none;
  background-color: white;
}
.nav-popmenu-row {
  /*height: 70px;*/ /* in media query */
  width: 100%;
  border-bottom: 1px solid #ddd;
  vertical-align: middle;
  display: table;
  cursor: pointer;
}
.nav-popmenu-item {
  display: table-cell;
  vertical-align: middle;
  text-align: left;
}
.check-icon {
  display: inline-block;
  width: 40px;
  text-align: center;
  vertical-align: middle;
  font-size: 0.9em;
}
@media (max-height: 499px) {
  .nav-popmenu-row {
    height: auto;
  }
}
@media (min-height: 500px) and (max-height: 767px) {
  .nav-popmenu-row {
    height: 60px;
  }
}
@media (min-height: 768px) {
  .nav-popmenu-row {
    height: 80px;
  }
}
.tr-result {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  margin-top: -200px;
  background-color: white;
  border: 1px solid #302e2e;
  border-radius: 20px;
  box-shadow: 0px 0px 3px #aaa;
  display: none;
  min-width: 300px;
  max-width: 80%;
  max-height: 400px;
  cursor: pointer;
  overflow: hidden;
  width: fit-content;
}
.tr-dict-name {
  position: absolute;
  top: 10px;
  left: 10px;
  max-width: 250px;
  height: 20px;
  text-align: left;
  overflow: hidden;
  color: #555;
  background-color: white;
  font-size: 0.7em;
}
.tr-dict-name select {
  margin: 0;
  padding: 0;
  outline: none;
  border: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: 0.7em;
  text-decoration: underline;
}
.tr-scrl-up-icon {
  position: absolute;
  top: 80px;
  right: 10px;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  color: #111;
  background-color: white;
  border: 1px solid #000;
  border-radius: 50%;
  font-size: 20px;
}
.tr-scrl-down-icon {
  position: absolute;
  top: 150px;
  right: 10px;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  color: #111;
  background-color: white;
  border: 1px solid #000;
  border-radius: 50%;
  font-size: 20px;
}
.tr-close-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  color: #111;
  background-color: white;
  border: 1px solid #000;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bolder;
}
.tr-word {
  margin: 10px auto 0px auto;
  padding: 25px 50px 5px 40px;
  font-weight: bold;
  text-align: center;
  border-bottom: 1px dotted #ccc;
}
.tr-result-text {
  width: 100%;
  min-height: 100px;
  max-height: 350px; /* tr-result - tr-word */
  overflow: hidden;
  padding: 0px 10px 10px 10px;
}
.tr-text-container {
  width: 100%;
  min-height: 100px;
  max-height: 350px;
  overflow-y: auto;
}
.tr-text {
  word-break: break-word;
  box-sizing: border-box;
  margin: 0px auto 10px auto;
  padding: 0px 10px 10px 10px;
  /*text-align: center;*/
  min-height: 100%;
}
