---
sort: 7
---

# Changelog

## 3.0.0
1. 支持Python 3
2. 重新设计的软件架构
3. 多平台支持，不再受限于gae平台
4. 支持不用修改的Calibre的recipe格式
5. 预置Calibre的一千多个recipe文件

## 1.26.9
1. 修改其他账号页面添加"有效期"选项。

## 1.26.8
1. "网友分享" 添加一个搜索栏。（小彩蛋：输入 "#download" 可以下载全部RSS资源）
2. 更新README和FAQ关于部署的说明。

## 1.26.7
1. 适配 kindle 固件 5.9.x (在书籍下方显示时间而不是作者)。
2. gufengmh地址变更。
3. readability 库升级为 0.8.1。

## 1.26.6
1. 添加漫画 扑飞, 古风 的支持。
2. 添加更多推送书籍的语种。

## 1.26.5
1. bugfix: 修正1.26.3引入的BUG，此BUG导致排版异常和在电脑上目录跳转到不正确的位置。

## 1.26.4
1. bugfix: 漫画书籍推送失败，1.26.2引入的bug。

## 1.26.3
1. 不在用户上传的CSS样式表里面添加命名空间。

## 1.26.2
1. 支持用户上传CSS样式表文件来定制推送到书籍样式。
2. 清理失效的书籍。

## 1.26.1
1. 共享库里新添加的源“置顶”一天。
2. bugfix: 修正不同账号无法订阅相同URL的BUG。

## 1.26
1. 添加 共享库 特性，所有KindleEar部署的应用都可以使用，并且支持将自己的订阅分享给其他网友。
2. [网友贡献] 完善CSS，添加响应式设计，适配手机浏览器。
3. [网友贡献] 添加Bookmarklet。
4. [网友贡献] 添加多一些漫画支持。
5. [网友贡献] 添加Sendgrid的邮件发送选项，可以支持发送更多的邮件。

## 1.25.5
1. skiinder<https://github.com/skiinder>实现在线连载小说的推送。
2. 导入OPML订阅列表时可选默认按全文还是摘要订阅源导入。

## 1.25.4
1. 改进手动推送功能，如果仅订阅了自定义RSS，则可以选择推送哪些订阅源。

## 1.25.3
1. 手动推送支持选择部分书籍(此功能移动到“高级设置|现在投递”)。
2. 新增移除文本链接选项。

## 1.25.2
1. 更新内置书籍“Economist”的RSS订阅源链接。
2. bugfix: 修正邮件中转模块推送失败问题。
3. bugfix: 修正保存到Instapaper功能失效问题。

## 1.25.1
1. 新特性：创建其他账号时可选账号过期期限。
2. bugfix: 修正漫画模式不压缩图片的问题。

## 1.25
1. 添加漫画模式和多本漫画书。
2. 添加对Kindle Voyage和Kindle PaperWhite3的支持。

## 1.24
1. 新特性：用户可以在GUI界面上直接上传修改封面图片，而且每个账号可单独设置不同的封面图片。
2. bugfix：修正 反“反盗链”特性 无法下载包含unicode字符的URL的图片的一个bug。

## 1.23.5
1. 新特性：可以在线抓取封面，在自定义py书籍文件中将coverfile属性设置为你自己的抓取函数即可。
2. bugfix: 修正部分源没有title属性导致推送失败的问题。

## 1.23.3
1. 增加可在文章后附加网址二维码的功能，方便用手机打开感兴趣的文章继续阅读。 
2. 增加一个调试功能：发往 <EMAIL> 的邮件将直接抓取邮件中链接的网页并转发到对应的管理员邮箱。

## 1.23.1
1. 增强错误处理，规避部分网友部署后无法建立索引的问题。
2. Bugfix: 修正书籍设置中extra_css无效的问题。
3. Bugfix: 修正部分全文RSS XML中HTML链接格式不规范导致无法解析出内容的问题。

## 1.23
1. 添加自动将超长图切割为多个小图片的功能，方便在kindle上看长图。
2. 进一步优化自动网页解码功能，减小乱码情况。
3. 修正导入含中文字符URL的OPML文件导致程序崩溃的问题。

## 1.22.3
1. 修正遇到xml文件中的非法tag（中文tag）导致推送失败的问题。

## 1.22.2
1. 邮件中转模块升级，在邮件标题中添加 !links则强制抓取文章链接，!article则强制发送文本。
2. Kindle邮件地址支持多个收件人地址，用分号分隔。

## 1.22.1
1. 升级内部一些模块。

## 1.22
1. 用AJAX技术重新实现“我的订阅”前后台，实现不重新加载页面增删订阅或RSS。

## 1.21.1
1. 增加保存到Instapaper的归档功能。

## 1.21
1. 增加保存到Pocket的归档功能。

## 1.20.28
1. 《雪球网》屏蔽了gae的IP段的网络请求，此版本通过中转器获取。

## 1.20.27
1. 邮件中转功能中引入字符串压缩，支持一封邮件内包含更多链接，以便更好的通过发送链接生成电子书。

## 1.20.26
1. 引入html内嵌base64图像技术，减小http请求数量，提升网页加速效率。

## 1.20.25
1. 增强网页解码，容忍部分字符解码错误，减小了网页乱码可能。

## 1.20.24
1. 修改选择了合并推送但仅有自定义RSS推送时无封面问题。

## 1.20.23
1. 去掉标题格式中的 “标题 日/月” 和 “标题 月/日” 格式，因为会导致推送失败。

## 1.20.22
1. 解决URL中有unicode字符时无法导出OPML文件的文件。
2. 解决微信公众号获取失败后导致其他书籍推送异常的问题。

## 1.20.21
1. 添加ETAG机制，减小需要的网络流量。

## 1.20.20
1. 修正部分XML文件获取文件编码失败问题。

## 1.20.19
1. 修正pycrypto模块加载失败问题。

## 1.20.18
1. 微信公众号突破一次防爬取 [zhu327](https://github.com/zhu327/rss)。
2. 通过邮件链接抓取的文章不添加封面。

## 1.20.17
1. 在目录中添加各个源的文章篇数。
2. 增加对书籍异常的处理，避免一本书籍的异常而影响其他书籍的推送。

## 1.20.16
1. 增加一个选项，可选择使用网页标题还是feed标题做为文章标题。

## 1.20.15
1. bugfix: weixinbase部分解码失败问题。

## 1.20.14
1. bugfix: 喷嚏图啩原RSS地址已经失效，更换为另一个地址。

## 1.20.13
1. bugfix: 修改有部分RSS将一个图片做为一篇文章（没有html包装）导致推送失败的问题。

## 1.20.12
1. 导出opml时将url转义。

## 1.20.11
1. "导入订阅列表"功能支持OPML元素Outline嵌套。

## 1.20.10
1. bugfix: 修正有部分文章无法提取正文导致推送失败的问题。

## 1.20.9
1. 增加从opml文件导入订阅列表的功能。
2. 网友seff增加特性：可选部分书籍单独推送。

## 1.20.8
1. 网友mcfloundinho增加《共识网》

## 1.20.7
1. bugfix:修正*****************************触发投递失败的问题。

## 1.20.6
1. bugfix:继续修改urlopener处理cookie的一个bug.

## 1.20.5
1. 增加网友mcfloundinho提供的《南方周末》。
2. bugfix:修改urlopener处理cookie的一个bug.

## 1.20.4
1. 更新土耳其语翻译。

## 1.20.3
1. 修改内置书籍TED渤海湾以适应其网站改版。

## 1.20.2
1. 针对使用图片延迟加载技术的网页特殊处理，可以获取部分此类网页的图片。

## 1.20.1
1. 新特性，在合并推送时将各书籍的封面拼贴起来。默认已经开启，如果你使用以前的config.py，请设置DEFAULT_COVER_BV=None，如果不喜欢此效果，可以设置DEFAULT_COVER_BV='cv_bound.jpg'
2. bugfix: 修正保存到evernote不成功的问题（1.13引入）

## 1.20
1. 增加一个简单的正文提取模块，在readability失败后启用。
2. 增强的网页解码器，综合考虑http响应头/html文件头/chardet检测结果，效率更高，乱码更少。
3. 支持需要登陆才能查看文章的网站，请参照FAQ如何使用。
4. 针对一天推送多次的需求，书籍属性‘oldest_article’大于365则使用*秒*为单位。
5. 增强的密码安全，加salt然后md5，无法通过密码词典破解，在可接受的代价范围内无法暴力破解。
（仅新账号启用，如果需要可以删掉admin然后重新登陆就会新建admin账号）
6. 整理文件夹结构，将相关库都放到lib目录下。
7. 其他一些小的可用性增强。
> 升级注意:书籍的fetcharticle()增加了一个参数，如果你定制的书籍使用到此接口，需要修改。
