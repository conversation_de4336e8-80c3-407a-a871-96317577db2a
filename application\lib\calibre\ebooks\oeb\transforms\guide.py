#!/usr/bin/env python


__license__   = 'GPL v3'
__copyright__ = '2009, <PERSON><PERSON> <<EMAIL>>'
__docformat__ = 'restructuredtext en'


class Clean:
    '''Clean up guide, leaving only known values '''

    def __call__(self, oeb, opts):
        self.oeb, self.log, self.opts = oeb, oeb.log, opts

        if 'cover' not in self.oeb.guide:
            covers = []
            for x in ('other.ms-coverimage-standard', 'coverimagestandard',
                    'other.ms-titleimage-standard', 'other.ms-titleimage',
                    'other.ms-coverimage', 'other.ms-thumbimage-standard',
                    'other.ms-thumbimage', 'thumbimagestandard'):
                if x in self.oeb.guide:
                    href = self.oeb.guide[x].href
                    try:
                        item = self.oeb.manifest.hrefs[href]
                    except KeyError:
                        continue
                    else:
                        covers.append([self.oeb.guide[x], len(item.data)])

            covers.sort(key=lambda x: x[1], reverse=True)
            if covers:
                ref = covers[0][0]
                if len(covers) > 1:
                    self.log('Choosing %s:%s as the cover'%(ref.type, ref.href))
                ref.type = 'cover'
                self.oeb.guide.refs['cover'] = ref

        if ('start' in self.oeb.guide and 'text' not in self.oeb.guide):
            # Prefer text to start as per the OPF 2.0 spec
            x = self.oeb.guide['start']
            self.oeb.guide.add('text', x.title, x.href)
            self.oeb.guide.remove('start')

        for x in list(self.oeb.guide):
            if x.lower() not in {
                    'cover', 'titlepage', 'masthead', 'toc', 'title-page',
                    'copyright-page', 'text', 'index', 'glossary',
                    'acknowledgements', 'bibliography', 'colophon',
                    'copyright-page', 'dedication', 'epigraph', 'foreword',
                    'loi', 'lot', 'notes', 'preface'}:
                item = self.oeb.guide[x]
                if item.title and item.title.lower() == 'start':
                    continue
                self.oeb.guide.remove(x)
