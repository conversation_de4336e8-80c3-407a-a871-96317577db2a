{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("User account") }} - KindleEar</title>
{% endblock -%}

{% block content -%}
<div class="main">
  {% if tips -%}
  <div class="notice-box">{{tips|safe}}</div>
  {% endif -%}
  <form class="pure-form pure-form-aligned" action="" method="POST">
    <fieldset>
      <legend>{{formTitle}}</legend>
      <div class="pure-control-group">
        <label for="username">{{ _("Username") }}</label>
        <input name="username" type="text" {% if user %}value="{{user.name}}" readonly {% else %} autofocus {% endif %} class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group">
        <label for="password1">{{ _("Password") }}</label>
        <input name="password1" type="password" class="pure-u-1 pure-u-sm-1-2" {% if userName %} autofocus {% endif %} />
      </div>
      <div class="pure-control-group">
        <label for="password2">{{ _("Confirm password") }}</label>
        <input name="password2" type="password" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group">
        <label for="email">{{_("Email")}}</label>
        <input type="email" name="email" class="pure-u-1 pure-u-sm-1-2" {% if user %}value="{{user.cfg('email')}}" {% endif %} />
      </div>
      <div class="pure-control-group">
        <label for="sm_service">{{_("Email service")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2 bool" name="sm_service">
          {% set srv = user.send_mail_service.get('service') if user else 'admin' %}
          <option value="admin" {% if srv == 'admin' %} selected="selected" {% endif %}>{{_("Same as admin")}}</option>
          <option value="independent" {% if srv != 'admin' %} selected="selected" {% endif %}>{{_("Independent")}}</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label for="expiration">{{_("Expiration")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2 bool" name="expiration">
          {% set exp = user.expiration_days if user else 0 %}
          <option value="0" {% if exp == 0 %}selected="selected"{% endif %}>{{_("Never expire")}}</option>
          <option value="7" {% if exp == 7 %}selected="selected"{% endif %}>{{_("7 Days")}}</option>
          <option value="30" {% if exp == 30 %}selected="selected"{% endif %}>{{_("1 Month")}}</option>
          <option value="90" {% if exp == 90 %}selected="selected"{% endif %}>{{_("3 Months")}}</option>
          <option value="180" {% if exp == 180 %}selected="selected"{% endif %}>{{_("6 Months")}}</option>
          <option value="365" {% if exp == 365 %}selected="selected"{% endif %}>{{_("1 Year")}}</option>
          <option value="730" {% if exp == 730 %}selected="selected"{% endif %}>{{_("2 Years")}}</option>
        </select>
      </div>
      <div class="pure-control-group" style="text-align:center;">
        <button type="submit" class="pure-button pure-button-primary pure-input-rounded">{{submitTitle}}</button>
      </div>
    </fieldset>
  </form>
</div>
{% endblock -%}