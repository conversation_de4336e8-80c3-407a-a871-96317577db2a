name: Docker Build and Push
permissions: read-all

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "The branch, tag or SHA to release from"
        required: true
        default: "master"

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'

      - name: Checkout code
        uses: actions/checkout@v2

      - name: Get version from main.py
        id: get_version
        run: |
          version=$(grep -oP "__Version__ = '\K[^']+" main.py)
          if [ -z "$version" ]; then
            echo "Error: Unable to retrieve version from main.py"
            exit 1
          else
            echo "::set-output name=version::$version"
          fi

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Build and push Docker image
        env:
          IMAGE_NAME: kindleear/kindleear
          TAG_LATEST: latest
          TAG_VERSION: ${{ steps.get_version.outputs.version }}
        run: |
          docker buildx build --push --platform=linux/amd64,linux/arm64 -t $IMAGE_NAME:$TAG_VERSION -t $IMAGE_NAME:$TAG_LATEST -f docker/Dockerfile .
