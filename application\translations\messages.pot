# Translations template for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-10 19:49-0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"

#: application/templates/admin.html:3 application/templates/base.html:53
#: application/templates/base.html:192 application/templates/settings.html:263
msgid "Account"
msgstr ""

#: application/templates/admin.html:19
msgid "Signup settings"
msgstr ""

#: application/templates/admin.html:19
#: application/templates/adv_calibre_options.html:18
#: application/templates/adv_proxy.html:18
msgid "Save"
msgstr ""

#: application/templates/admin.html:21
#: application/templates/user_account.html:31
msgid "Email service"
msgstr ""

#: application/templates/admin.html:23
#: application/templates/user_account.html:34
msgid "Same as admin"
msgstr ""

#: application/templates/admin.html:24
#: application/templates/user_account.html:35
msgid "Independent"
msgstr ""

#: application/templates/admin.html:28
msgid "Signup type"
msgstr ""

#: application/templates/admin.html:30
msgid "Public"
msgstr ""

#: application/templates/admin.html:31
msgid "One time code"
msgstr ""

#: application/templates/admin.html:32
msgid "Permanent code"
msgstr ""

#: application/templates/admin.html:36
msgid "Invitation codes"
msgstr ""

#: application/templates/admin.html:37
msgid "one code per line"
msgstr ""

#: application/templates/admin.html:43
msgid "Accounts"
msgstr ""

#: application/templates/admin.html:43
#: application/templates/adv_inboundmail.html:61
#: application/templates/my.html:34 application/view/admin.py:57
#: application/view/admin.py:64 application/view/admin.py:91
msgid "Add"
msgstr ""

#: application/templates/admin.html:54
#: application/templates/adv_archive.html:69 application/templates/home.html:19
#: application/templates/login.html:24 application/templates/logs.html:70
#: application/templates/reset_password.html:19
#: application/templates/reset_password.html:20
#: application/templates/settings.html:241 application/templates/signup.html:21
#: application/templates/user_account.html:15
msgid "Username"
msgstr ""

#: application/templates/admin.html:55
msgid "AutoSend"
msgstr ""

#: application/templates/admin.html:56
#: application/templates/change_password.html:27
#: application/templates/reset_password.html:26
#: application/templates/signup.html:33
#: application/templates/user_account.html:27
msgid "Email"
msgstr ""

#: application/templates/admin.html:57
#: application/templates/user_account.html:39
msgid "Expiration"
msgstr ""

#: application/templates/admin.html:58
msgid "Operation"
msgstr ""

#: application/templates/admin.html:65
msgid "Yes"
msgstr ""

#: application/templates/admin.html:65
msgid "No"
msgstr ""

#: application/templates/admin.html:68
msgid "Never"
msgstr ""

#: application/templates/admin.html:70
#: application/templates/adv_inboundmail.html:26
#: application/templates/settings.html:58
#: application/templates/settings.html:181
#: application/templates/user_account.html:43
msgid "7 Days"
msgstr ""

#: application/templates/admin.html:72
#: application/templates/user_account.html:44
msgid "1 Month"
msgstr ""

#: application/templates/admin.html:74
#: application/templates/user_account.html:45
msgid "3 Months"
msgstr ""

#: application/templates/admin.html:76
#: application/templates/user_account.html:46
msgid "6 Months"
msgstr ""

#: application/templates/admin.html:78
#: application/templates/user_account.html:47
msgid "1 Year"
msgstr ""

#: application/templates/admin.html:80
#: application/templates/user_account.html:48
msgid "2 Years"
msgstr ""

#: application/templates/admin.html:85
#: application/templates/change_password.html:3
#: application/templates/change_password.html:13 application/view/admin.py:129
#: application/view/admin.py:183
msgid "Edit account"
msgstr ""

#: application/templates/admin.html:89
#: application/templates/adv_inboundmail.html:54
#: application/templates/adv_uploadcss.html:31
#: application/templates/base.html:25 application/templates/webmail.html:22
msgid "Delete"
msgstr ""

#: application/templates/adv_archive.html:3
#: application/templates/adv_archive.html:14
#: application/templates/adv_base.html:57
#: application/templates/adv_base.html:61
msgid "Archive"
msgstr ""

#: application/templates/adv_archive.html:15
msgid "Append hyperlinks for archiving or sharing."
msgstr ""

#: application/templates/adv_archive.html:40
msgid "Authorized"
msgstr ""

#: application/templates/adv_archive.html:42
msgid "Authorize"
msgstr ""

#: application/templates/adv_archive.html:53
msgid "Email or Username"
msgstr ""

#: application/templates/adv_archive.html:56
#: application/templates/adv_archive.html:72 application/templates/base.html:54
#: application/templates/home.html:20 application/templates/login.html:28
#: application/templates/settings.html:245 application/templates/signup.html:25
#: application/templates/user_account.html:19
msgid "Password"
msgstr ""

#: application/templates/adv_archive.html:59
#: application/templates/adv_archive.html:75 application/templates/base.html:64
msgid "Verify"
msgstr ""

#: application/templates/adv_archive.html:78
msgid "client_id"
msgstr ""

#: application/templates/adv_archive.html:81
msgid "client_secret"
msgstr ""

#: application/templates/adv_archive.html:84
#: application/templates/settings.html:233
msgid "Host"
msgstr ""

#: application/templates/adv_archive.html:119
#: application/templates/adv_dict.html:79
#: application/templates/adv_inboundmail.html:34
#: application/templates/book_audiolator.html:113
#: application/templates/book_summarizer.html:88
#: application/templates/book_translator.html:80
#: application/templates/settings.html:269
msgid "Save settings"
msgstr ""

#: application/templates/adv_base.html:39
#: application/templates/adv_base.html:43
#: application/templates/adv_delivernow.html:8
msgid "Deliver Now"
msgstr ""

#: application/templates/adv_base.html:48
#: application/templates/adv_base.html:52
#: application/templates/adv_inboundmail.html:3
#: application/templates/adv_inboundmail.html:9
#: application/templates/adv_inboundmail.html:14
msgid "Inbound Mail"
msgstr ""

#: application/templates/adv_base.html:66
#: application/templates/adv_base.html:70 application/templates/adv_dict.html:3
#: application/templates/adv_dict.html:13 application/templates/reader.html:134
msgid "Dictionary"
msgstr ""

#: application/templates/adv_base.html:75
#: application/templates/adv_base.html:83
#: application/templates/adv_proxy.html:3
#: application/templates/adv_proxy.html:12
msgid "Proxy"
msgstr ""

#: application/templates/adv_base.html:92
#: application/templates/adv_base.html:96
#: application/templates/adv_import.html:8
msgid "Import Feeds"
msgstr ""

#: application/templates/adv_base.html:101
#: application/templates/adv_base.html:105
msgid "Cover Image"
msgstr ""

#: application/templates/adv_base.html:110
#: application/templates/adv_base.html:114
#: application/templates/adv_uploadcss.html:3
msgid "Stylesheet"
msgstr ""

#: application/templates/adv_base.html:119
#: application/templates/adv_base.html:123
#: application/templates/adv_calibre_options.html:3
#: application/templates/adv_calibre_options.html:12
msgid "Calibre Options"
msgstr ""

#: application/templates/adv_calibre_options.html:13
msgid "Set the parameters for Calibre, in JSON dictionary format."
msgstr ""

#: application/templates/adv_delivernow.html:3
msgid "Deliver now"
msgstr ""

#: application/templates/adv_delivernow.html:9
msgid "Deliver selected recipes now."
msgstr ""

#: application/templates/adv_delivernow.html:12
msgid "There are no recipes subscribed"
msgstr ""

#: application/templates/adv_delivernow.html:17
#: application/templates/base.html:101
msgid "Sep"
msgstr ""

#: application/templates/adv_delivernow.html:22
msgid "Select all"
msgstr ""

#: application/templates/adv_delivernow.html:23
msgid "Select none"
msgstr ""

#: application/templates/adv_delivernow.html:28
msgid "Deliver"
msgstr ""

#: application/templates/adv_dict.html:15
msgid "Set up dictionaries for online reading."
msgstr ""

#: application/templates/adv_dict.html:18
#: application/templates/adv_dict.html:40
#: application/templates/adv_dict.html:62
msgid "Book language"
msgstr ""

#: application/templates/adv_dict.html:27
#: application/templates/adv_dict.html:49
#: application/templates/adv_dict.html:66
#: application/templates/book_summarizer.html:23
#: application/templates/book_translator.html:26
#: application/templates/word_lookup.html:59
msgid "Engine"
msgstr ""

#: application/templates/adv_dict.html:33
#: application/templates/adv_dict.html:55
#: application/templates/adv_dict.html:72
#: application/templates/word_lookup.html:65
msgid "Database"
msgstr ""

#: application/templates/adv_dict.html:63
msgid "Other languages"
msgstr ""

#: application/templates/adv_dict.html:81
#: application/templates/word_lookup.html:3
#: application/templates/word_lookup.html:79
msgid "Word lookup"
msgstr ""

#: application/templates/adv_dict.html:86 application/view/reader.py:29
#: application/view/reader.py:86
msgid "Online reading feature has not been activated yet."
msgstr ""

#: application/templates/adv_import.html:3
#: application/templates/adv_import.html:19
msgid "Import"
msgstr ""

#: application/templates/adv_import.html:9
msgid "Import custom rss from an OPML file."
msgstr ""

#: application/templates/adv_import.html:15
msgid "Import as fulltext rss by default"
msgstr ""

#: application/templates/adv_import.html:20
msgid "Download"
msgstr ""

#: application/templates/adv_inboundmail.html:11
msgid ""
"To enable the inbound email feature, you also need to configure the "
"whitelist."
msgstr ""

#: application/templates/adv_inboundmail.html:16
#: application/templates/adv_uploadcover.html:15
#: application/templates/book_summarizer.html:19
#: application/templates/book_translator.html:22
#: application/templates/settings.html:143
msgid "Disable"
msgstr ""

#: application/templates/adv_inboundmail.html:17
msgid "Forward Only"
msgstr ""

#: application/templates/adv_inboundmail.html:18
msgid "Save Only"
msgstr ""

#: application/templates/adv_inboundmail.html:19
msgid "Save and Forward"
msgstr ""

#: application/templates/adv_inboundmail.html:23
msgid "Email Retention"
msgstr ""

#: application/templates/adv_inboundmail.html:25
#: application/templates/settings.html:52
#: application/templates/settings.html:175
msgid "1 Day"
msgstr ""

#: application/templates/adv_inboundmail.html:27
#: application/templates/settings.html:59
msgid "30 Days"
msgstr ""

#: application/templates/adv_inboundmail.html:28
#: application/templates/settings.html:51
#: application/templates/settings.html:174
msgid "No limit"
msgstr ""

#: application/templates/adv_inboundmail.html:36
msgid "Open webmail"
msgstr ""

#: application/templates/adv_inboundmail.html:44
msgid "White List"
msgstr ""

#: application/templates/adv_inboundmail.html:46
#, python-format
msgid ""
"Emails sent to %(name)sxxx@%(mailHost)s will be forwarded to your kindle "
"email."
msgstr ""

#: application/templates/adv_inboundmail.html:47
msgid "Example"
msgstr ""

#: application/templates/adv_inboundmail.html:59
msgid "Please input mail address"
msgstr ""

#: application/templates/adv_proxy.html:13
msgid "Supports"
msgstr ""

#: application/templates/adv_proxy.html:24
#: application/templates/adv_proxy.html:29
#: application/templates/book_audiolator.html:132
#: application/templates/book_summarizer.html:105
#: application/templates/book_translator.html:97
msgid "Test"
msgstr ""

#: application/templates/adv_uploadcover.html:3
msgid "Cover image"
msgstr ""

#: application/templates/adv_uploadcover.html:9
msgid "Upload cover image"
msgstr ""

#: application/templates/adv_uploadcover.html:10
msgid ""
"Upload cover images from local with an aspect ratio of approximately "
"0.625."
msgstr ""

#: application/templates/adv_uploadcover.html:13
msgid "Include cover"
msgstr ""

#: application/templates/adv_uploadcover.html:16
#: application/templates/book_summarizer.html:18
#: application/templates/book_translator.html:21
msgid "Enable"
msgstr ""

#: application/templates/adv_uploadcover.html:20
msgid "Rule for cover"
msgstr ""

#: application/templates/adv_uploadcover.html:22
msgid "Random"
msgstr ""

#: application/templates/adv_uploadcover.html:23
#: application/templates/base.html:127
msgid "Weekday"
msgstr ""

#: application/templates/adv_uploadcover.html:49
msgid "Upload/Update"
msgstr ""

#: application/templates/adv_uploadcss.html:22
msgid "Upload stylesheet"
msgstr ""

#: application/templates/adv_uploadcss.html:23
msgid "Upload a stylesheet from local (accept utf-8 only)."
msgstr ""

#: application/templates/adv_uploadcss.html:30
msgid "Upload"
msgstr ""

#: application/templates/autoback.html:3
msgid "Auto back"
msgstr ""

#: application/templates/autoback.html:28
msgid "Auto back to previous page after 5 seconds"
msgstr ""

#: application/templates/autoback.html:29
#: application/templates/tipsback.html:15
msgid "Click to back"
msgstr ""

#: application/templates/base.html:24 application/templates/reader.html:189
msgid "Confirm Deletion"
msgstr ""

#: application/templates/base.html:26
msgid "Delete (Ctrl for no confirm)"
msgstr ""

#: application/templates/base.html:27
msgid "View Source Code"
msgstr ""

#: application/templates/base.html:28
msgid "Subscribe (Deliver Separately)"
msgstr ""

#: application/templates/base.html:29
msgid "Subscribe"
msgstr ""

#: application/templates/base.html:30
msgid "Cannot add this custom rss, Error:"
msgstr ""

#: application/templates/base.html:31
msgid "Cannot delete this feed, Error:"
msgstr ""

#: application/templates/base.html:32
msgid "Fulltext"
msgstr ""

#: application/templates/base.html:33 application/templates/base.html:43
msgid "Share"
msgstr ""

#: application/templates/base.html:34 application/templates/reader.html:190
msgid "Are you sure to delete?"
msgstr ""

#: application/templates/base.html:35
msgid "Report to the server that this feed is invalid."
msgstr ""

#: application/templates/base.html:36
msgid "Are you sure to REMOVE ALL CUSTOM RSS?"
msgstr ""

#: application/templates/base.html:37
msgid "Share links, share happiness"
msgstr ""

#: application/templates/base.html:38
msgid "Category"
msgstr ""

#: application/templates/base.html:39
#: application/templates/book_audiolator.html:58
#: application/templates/book_summarizer.html:43
#: application/templates/settings.html:159
msgid "Language"
msgstr ""

#: application/templates/base.html:40
msgid ""
"Please write a category in text field if the one you wish is not in the "
"list."
msgstr ""

#: application/templates/base.html:41
msgid "Ok"
msgstr ""

#: application/templates/base.html:42
msgid "Cancel"
msgstr ""

#: application/templates/base.html:44
msgid "Language code invalid"
msgstr ""

#: application/templates/base.html:45
msgid "Thank you for sharing."
msgstr ""

#: application/templates/base.html:46 application/templates/reader.html:159
msgid "Close"
msgstr ""

#: application/templates/base.html:47
msgid "Unsubscribe"
msgstr ""

#: application/templates/base.html:48
msgid "Cannot subscribe this recipe, Error:"
msgstr ""

#: application/templates/base.html:49
msgid "Are you sure to Unsubscribe ({0})?"
msgstr ""

#: application/templates/base.html:50
msgid "Cannot unsubscribe this recipe, Error:"
msgstr ""

#: application/templates/base.html:51
msgid "The recipe is already subscribed."
msgstr ""

#: application/templates/base.html:52
msgid "Website login lnformation"
msgstr ""

#: application/templates/base.html:55
msgid "Submit"
msgstr ""

#: application/templates/base.html:56
msgid ""
"If any field is left blank, the server will clear the saved login "
"information."
msgstr ""

#: application/templates/base.html:57
msgid "Cannot set the website login information, Error:"
msgstr ""

#: application/templates/base.html:58 application/templates/my.html:58
msgid "Upload custom recipe"
msgstr ""

#: application/templates/base.html:59
msgid "Congratulations"
msgstr ""

#: application/templates/base.html:60
msgid "Thanks"
msgstr ""

#: application/templates/base.html:61
msgid ""
"Your recipe has been uploaded, and it can be found in the Library "
"section. If you dont see it, please make sure to switch to the correct "
"language."
msgstr ""

#: application/templates/base.html:62
msgid "Your recipe have been deleted."
msgstr ""

#: application/templates/base.html:63
msgid "Kindleify Selection"
msgstr ""

#: application/templates/base.html:65
msgid "Verified"
msgstr ""

#: application/templates/base.html:66 application/view/login.py:79
#: application/view/share.py:157
msgid "The username does not exist or password is wrong."
msgstr ""

#: application/templates/base.html:67
msgid "The file you chosen is not an acceptable type."
msgstr ""

#: application/templates/base.html:68
msgid "The file have been uploaded successfully."
msgstr ""

#: application/templates/base.html:69 application/templates/library.html:67
msgid "This feed has been successfully subscribed."
msgstr ""

#: application/templates/base.html:70
msgid "Thank you for your feedback, this feed will be reviewed soon."
msgstr ""

#: application/templates/base.html:71
msgid "Are you confirming to share the recipe ({0})?"
msgstr ""

#: application/templates/base.html:72
msgid "[All]"
msgstr ""

#: application/templates/base.html:73
msgid "[By Time]"
msgstr ""

#: application/templates/base.html:74
msgid "[Random]"
msgstr ""

#: application/templates/base.html:75
msgid "[Uncategoried]"
msgstr ""

#: application/templates/base.html:76
msgid "There are no links found."
msgstr ""

#: application/templates/base.html:77
msgid "Invalid report"
msgstr ""

#: application/templates/base.html:78
msgid "Are you confirming that this link is invalid or off the cloud?"
msgstr ""

#: application/templates/base.html:79
msgid "Customize delivery time"
msgstr ""

#: application/templates/base.html:80 application/templates/settings.html:72
msgid "Delivery days"
msgstr ""

#: application/templates/base.html:81 application/templates/settings.html:74
msgid "Mon"
msgstr ""

#: application/templates/base.html:82 application/templates/settings.html:76
msgid "Tue"
msgstr ""

#: application/templates/base.html:83 application/templates/settings.html:78
msgid "Wed"
msgstr ""

#: application/templates/base.html:84 application/templates/settings.html:80
msgid "Thu"
msgstr ""

#: application/templates/base.html:85 application/templates/settings.html:82
msgid "Fri"
msgstr ""

#: application/templates/base.html:86 application/templates/settings.html:84
msgid "Sat"
msgstr ""

#: application/templates/base.html:87 application/templates/settings.html:86
msgid "Sun"
msgstr ""

#: application/templates/base.html:88
msgid "Delivery times"
msgstr ""

#: application/templates/base.html:89
msgid "The customized delivery time for the recipe has been successfully saved."
msgstr ""

#: application/templates/base.html:90
msgid "The account have been deleted."
msgstr ""

#: application/templates/base.html:91 application/view/share.py:147
msgid "The username or password is empty."
msgstr ""

#: application/templates/base.html:92 application/view/admin.py:81
#: application/view/admin.py:165 application/view/admin.py:191
#: application/view/login.py:220 application/view/login.py:273
msgid "The two new passwords are dismatch."
msgstr ""

#: application/templates/base.html:93
msgid "Password changed successfully."
msgstr ""

#: application/templates/base.html:94
msgid "Account added successfully."
msgstr ""

#: application/templates/base.html:95 application/view/login.py:128
msgid "login required"
msgstr ""

#: application/templates/base.html:96
msgid "Upload cover files successfully."
msgstr ""

#: application/templates/base.html:97
msgid ""
"Total size of the files you selected exceeds 16MB. Please reduce the "
"image resolution or upload in batches."
msgstr ""

#: application/templates/base.html:98
#: application/templates/book_translator.html:3
#: application/templates/book_translator.html:17
msgid "Bilingual Translator"
msgstr ""

#: application/templates/base.html:99
#: application/templates/book_summarizer.html:3
#: application/templates/book_summarizer.html:14
msgid "AI Summarizer"
msgstr ""

#: application/templates/base.html:100
msgid "Upl"
msgstr ""

#: application/templates/base.html:102
msgid "Log"
msgstr ""

#: application/templates/base.html:103
msgid "Emb"
msgstr ""

#: application/templates/base.html:104
msgid "Tr"
msgstr ""

#: application/templates/base.html:105
msgid "Tts"
msgstr ""

#: application/templates/base.html:106
msgid "Ai"
msgstr ""

#: application/templates/base.html:107
msgid ""
"The test email has been successfully sent to the following addresses. "
"Please check your inbox or spam folder to confirm its delivery. Depending"
" on your email server, there may be a slight delay."
msgstr ""

#: application/templates/base.html:108
msgid "Processing..."
msgstr ""

#: application/templates/base.html:109
msgid "The configuration validation is correct."
msgstr ""

#: application/templates/base.html:110 application/templates/logs.html:23
#: application/templates/logs.html:72 application/templates/my.html:17
#: application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/settings.html:155
msgid "Title"
msgstr ""

#: application/templates/base.html:111
#: application/templates/book_audiolator.html:3
#: application/templates/book_audiolator.html:20
msgid "Text to Speech"
msgstr ""

#: application/templates/base.html:112
msgid "Action"
msgstr ""

#: application/templates/base.html:113
msgid "File"
msgstr ""

#: application/templates/base.html:114
msgid "Upload Only"
msgstr ""

#: application/templates/base.html:115
msgid "Send"
msgstr ""

#: application/templates/base.html:116 application/templates/logs.html:54
msgid "There is nothing here."
msgstr ""

#: application/templates/base.html:117
msgid "Please select a single item."
msgstr ""

#: application/templates/base.html:118 application/templates/reader.html:191
msgid "Please select at least one item."
msgstr ""

#: application/templates/base.html:119 application/view/admin.py:77
#: application/view/admin.py:152 application/view/admin.py:189
#: application/view/adv.py:459 application/view/extension.py:34
#: application/view/extension.py:63 application/view/inbound_email.py:470
#: application/view/inbound_email.py:478 application/view/inbound_email.py:493
#: application/view/inbound_email.py:514 application/view/login.py:216
#: application/view/login.py:245 application/view/reader.py:109
#: application/view/reader.py:126 application/view/share.py:37
msgid "Some parameters are missing or wrong."
msgstr ""

#: application/templates/base.html:120
msgid "The email has been sent."
msgstr ""

#: application/templates/base.html:121 application/templates/webmail.html:29
msgid "From"
msgstr ""

#: application/templates/base.html:122 application/templates/logs.html:25
#: application/templates/logs.html:74 application/templates/webmail.html:30
msgid "To"
msgstr ""

#: application/templates/base.html:123 application/templates/webmail.html:31
msgid "Subject"
msgstr ""

#: application/templates/base.html:124 application/templates/logs.html:22
#: application/templates/logs.html:71 application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/webmail.html:32
msgid "Time"
msgstr ""

#: application/templates/base.html:125 application/templates/logs.html:24
#: application/templates/logs.html:73 application/templates/webmail.html:33
msgid "Size"
msgstr ""

#: application/templates/base.html:126
msgid "Date type"
msgstr ""

#: application/templates/base.html:128
msgid "Date"
msgstr ""

#: application/templates/base.html:129
msgid "This setting is prioritized."
msgstr ""

#: application/templates/base.html:130
msgid "Combine multiple values with commas."
msgstr ""

#: application/templates/base.html:131
msgid "Put dictionary in dict folder"
msgstr ""

#: application/templates/base.html:156 application/templates/home.html:16
msgid "Logout"
msgstr ""

#: application/templates/base.html:158 application/templates/home.html:21
#: application/templates/login.html:3 application/templates/login.html:22
#: application/templates/login.html:33
msgid "Login"
msgstr ""

#: application/templates/base.html:160 application/templates/signup.html:3
#: application/templates/signup.html:19 application/templates/signup.html:43
msgid "Signup"
msgstr ""

#: application/templates/base.html:189 application/templates/home.html:15
#: application/templates/my.html:3
msgid "Feeds"
msgstr ""

#: application/templates/base.html:190 application/templates/settings.html:3
msgid "Settings"
msgstr ""

#: application/templates/base.html:191 application/templates/logs.html:3
msgid "Logs"
msgstr ""

#: application/templates/base.html:193
msgid "Advanced"
msgstr ""

#: application/templates/base.html:194 application/templates/library.html:3
msgid "Shared"
msgstr ""

#: application/templates/base.html:195 application/templates/reader.html:6
msgid "Reader"
msgstr ""

#: application/templates/book_audiolator.html:22
#: application/templates/book_summarizer.html:16
#: application/templates/book_translator.html:19
msgid "State"
msgstr ""

#: application/templates/book_audiolator.html:24
msgid "Send Ebook and Audio"
msgstr ""

#: application/templates/book_audiolator.html:25
msgid "Send Audio only"
msgstr ""

#: application/templates/book_audiolator.html:26
msgid "Disable TTS"
msgstr ""

#: application/templates/book_audiolator.html:30
msgid "Send Audio To"
msgstr ""

#: application/templates/book_audiolator.html:31
msgid "Empty to use Kindle_email"
msgstr ""

#: application/templates/book_audiolator.html:35
msgid "TTS Engine"
msgstr ""

#: application/templates/book_audiolator.html:41
#: application/templates/book_summarizer.html:35
#: application/templates/book_translator.html:32
msgid "Api Host"
msgstr ""

#: application/templates/book_audiolator.html:42
#: application/templates/book_summarizer.html:36
#: application/templates/book_summarizer.html:75
msgid "Leave empty to use default"
msgstr ""

#: application/templates/book_audiolator.html:46
msgid "Region"
msgstr ""

#: application/templates/book_audiolator.html:53
#: application/templates/book_summarizer.html:39
#: application/templates/book_translator.html:36
msgid "Api Key"
msgstr ""

#: application/templates/book_audiolator.html:66
msgid "Voice name"
msgstr ""

#: application/templates/book_audiolator.html:73
msgid "Voice speed"
msgstr ""

#: application/templates/book_audiolator.html:75
msgid "Extra slow"
msgstr ""

#: application/templates/book_audiolator.html:76
msgid "Slow"
msgstr ""

#: application/templates/book_audiolator.html:77
#: application/templates/book_audiolator.html:87
#: application/templates/book_audiolator.html:97
msgid "Medium"
msgstr ""

#: application/templates/book_audiolator.html:78
msgid "Fast"
msgstr ""

#: application/templates/book_audiolator.html:79
msgid "Extra fast"
msgstr ""

#: application/templates/book_audiolator.html:83
msgid "Voice pitch"
msgstr ""

#: application/templates/book_audiolator.html:85
msgid "Extra low"
msgstr ""

#: application/templates/book_audiolator.html:86
msgid "Low"
msgstr ""

#: application/templates/book_audiolator.html:88
msgid "High"
msgstr ""

#: application/templates/book_audiolator.html:89
msgid "Extra high"
msgstr ""

#: application/templates/book_audiolator.html:93
msgid "Voice volume"
msgstr ""

#: application/templates/book_audiolator.html:95
msgid "Extra soft"
msgstr ""

#: application/templates/book_audiolator.html:96
msgid "Soft"
msgstr ""

#: application/templates/book_audiolator.html:98
msgid "Loud"
msgstr ""

#: application/templates/book_audiolator.html:99
msgid "Extra loud"
msgstr ""

#: application/templates/book_audiolator.html:105
#: application/templates/book_summarizer.html:80
#: application/templates/book_translator.html:72
msgid "Apply to all subscribed recipes"
msgstr ""

#: application/templates/book_audiolator.html:110
#: application/templates/book_summarizer.html:85
#: application/templates/book_translator.html:77
msgid ""
"Note: Enabling this feature will significantly increase consumed CPU "
"instance hours."
msgstr ""

#: application/templates/book_audiolator.html:119
#: application/templates/book_summarizer.html:94
#: application/templates/book_translator.html:86
msgid "Test (Please save settings firstly)"
msgstr ""

#: application/templates/book_audiolator.html:121
#: application/templates/book_summarizer.html:96
#: application/templates/book_translator.html:88
msgid "Text"
msgstr ""

#: application/templates/book_audiolator.html:127
msgid "Your browser does not support the audio element."
msgstr ""

#: application/templates/book_summarizer.html:29
msgid "Model"
msgstr ""

#: application/templates/book_summarizer.html:45
msgid "Auto"
msgstr ""

#: application/templates/book_summarizer.html:56
msgid "Summary words"
msgstr ""

#: application/templates/book_summarizer.html:70
msgid "Summary style"
msgstr ""

#: application/templates/book_summarizer.html:74
msgid "Placeholders available:"
msgstr ""

#: application/templates/book_summarizer.html:74
msgid "Custom prompt"
msgstr ""

#: application/templates/book_summarizer.html:100
msgid "Summary"
msgstr ""

#: application/templates/book_translator.html:37
msgid "One key per line"
msgstr ""

#: application/templates/book_translator.html:40
#: application/templates/word_lookup.html:51
msgid "Source language"
msgstr ""

#: application/templates/book_translator.html:46
msgid "Target language"
msgstr ""

#: application/templates/book_translator.html:52
msgid "Translation Position"
msgstr ""

#: application/templates/book_translator.html:54
msgid "Below original"
msgstr ""

#: application/templates/book_translator.html:55
msgid "Above original"
msgstr ""

#: application/templates/book_translator.html:56
msgid "Left to original"
msgstr ""

#: application/templates/book_translator.html:57
msgid "Right to original"
msgstr ""

#: application/templates/book_translator.html:58
msgid "Translated text only"
msgstr ""

#: application/templates/book_translator.html:62
msgid "Original text style"
msgstr ""

#: application/templates/book_translator.html:66
msgid "Translated text style"
msgstr ""

#: application/templates/book_translator.html:92
msgid "Translation"
msgstr ""

#: application/templates/change_password.html:15
msgid "Old password"
msgstr ""

#: application/templates/change_password.html:19
#: application/templates/reset_password.html:31
#: application/templates/reset_password.html:32
msgid "New password"
msgstr ""

#: application/templates/change_password.html:23
#: application/templates/reset_password.html:35
#: application/templates/reset_password.html:36
#: application/templates/signup.html:29
#: application/templates/user_account.html:23
msgid "Confirm password"
msgstr ""

#: application/templates/change_password.html:31
msgid "Share key"
msgstr ""

#: application/templates/change_password.html:37
msgid "Confirm Change"
msgstr ""

#: application/templates/debug_cmd.html:3
msgid "Debug cmd"
msgstr ""

#: application/templates/home.html:3
msgid "Home"
msgstr ""

#: application/templates/home.html:10 application/templates/login.html:18
msgid "You are in DEMO mode. Logging out will delete all data."
msgstr ""

#: application/templates/home.html:12
msgid "Sharing Joyful News Every Step of the Way"
msgstr ""

#: application/templates/home.html:31
msgid "Inherited From Calibre"
msgstr ""

#: application/templates/home.html:34
#, python-format
msgid ""
"Empowered by %(calibre)s, you can easily create e-books on a Python-"
"supported online platform and seamlessly transfer them to your e-reader "
"or other reading devices."
msgstr ""

#: application/templates/home.html:42
msgid "Share Your Ideas"
msgstr ""

#: application/templates/home.html:45
#, python-format
msgid ""
"With the open-source %(kindleear)s application, you can set up your own "
"server to deliver daily news feeds to your e-reader and effortlessly "
"share the service with friends."
msgstr ""

#: application/templates/library.html:50 application/templates/my.html:61
msgid "Search"
msgstr ""

#: application/templates/login.html:38 application/view/login.py:197
#: application/view/login.py:204
msgid ""
"The website does not allow registration. You can ask the owner for an "
"account."
msgstr ""

#: application/templates/logs.html:11
msgid "Only display last 20 logs"
msgstr ""

#: application/templates/logs.html:26 application/templates/logs.html:75
msgid "Status"
msgstr ""

#: application/templates/logs.html:58
msgid "Logs of other users"
msgstr ""

#: application/templates/my.html:12 application/templates/settings.html:153
msgid "Custom RSS"
msgstr ""

#: application/templates/my.html:23
msgid "Content Embedded"
msgstr ""

#: application/templates/my.html:27
msgid "Deliver Separately"
msgstr ""

#: application/templates/my.html:46
msgid "Subscribed"
msgstr ""

#: application/templates/my.html:51
msgid "Library"
msgstr ""

#: application/templates/my.html:51
msgid "get more recipes"
msgstr ""

#: application/templates/my.html:68
msgid "Subscription to selected recipe successful."
msgstr ""

#: application/templates/my.html:71
msgid "Bookmarklet"
msgstr ""

#: application/templates/my.html:75
msgid "Send to Kindle"
msgstr ""

#: application/templates/my.html:79
msgid "Subscribe with KindleEar"
msgstr ""

#: application/templates/my.html:82
msgid "Drag and drop this link to your bookmarks"
msgstr ""

#: application/templates/my.html:86
msgid "Chrome extension"
msgstr ""

#: application/templates/my.html:87
msgid "Edge extension"
msgstr ""

#: application/templates/my.html:89
msgid "Browser extensions also available"
msgstr ""

#: application/templates/reader.html:57
msgid "Push current book"
msgstr ""

#: application/templates/reader.html:63
msgid "Push current article"
msgstr ""

#: application/templates/reader.html:71
msgid "Delete selected books"
msgstr ""

#: application/templates/reader.html:77
msgid "Allow click links"
msgstr ""

#: application/templates/reader.html:83
msgid "Top-left dict mode"
msgstr ""

#: application/templates/reader.html:89
msgid "Dark mode"
msgstr ""

#: application/templates/reader.html:95
msgid "eInk mode"
msgstr ""

#: application/templates/reader.html:101
msgid "Increase font size"
msgstr ""

#: application/templates/reader.html:107
msgid "Decrease font size"
msgstr ""

#: application/templates/reader.html:113
msgid "Visualize Touch Regions"
msgstr ""

#: application/templates/reader.html:119
msgid "Help"
msgstr ""

#: application/templates/reader.html:125
#: application/templates/reader_404.html:135
msgid "Menu"
msgstr ""

#: application/templates/reader.html:139
msgid "Collapse all"
msgstr ""

#: application/templates/reader.html:144
msgid "Expand all"
msgstr ""

#: application/templates/reader.html:149
#: application/templates/reader_404.html:140
msgid "Prev"
msgstr ""

#: application/templates/reader.html:154
#: application/templates/reader_404.html:143
msgid "Next page"
msgstr ""

#: application/templates/reader.html:192
msgid "Pushed successfully."
msgstr ""

#: application/templates/reader.html:193
msgid "There are currently no books or articles being read."
msgstr ""

#: application/templates/reset_password.html:3
#: application/templates/reset_password.html:41
msgid "Reset password"
msgstr ""

#: application/templates/settings.html:14
msgid "Your account will pause after {0}, please log in again before it expires."
msgstr ""

#: application/templates/settings.html:23
msgid "Base"
msgstr ""

#: application/templates/settings.html:25
msgid "Auto delivery"
msgstr ""

#: application/templates/settings.html:28
msgid "Recipes and custom RSS"
msgstr ""

#: application/templates/settings.html:29
msgid "Recipes only"
msgstr ""

#: application/templates/settings.html:30
msgid "Disable all"
msgstr ""

#: application/templates/settings.html:34
msgid "Kindle E-mail"
msgstr ""

#: application/templates/settings.html:35
msgid "Seperated by comma"
msgstr ""

#: application/templates/settings.html:39
msgid "Delivery mode"
msgstr ""

#: application/templates/settings.html:42
msgid "Email delivery & online reading"
msgstr ""

#: application/templates/settings.html:43
msgid "Email delivery"
msgstr ""

#: application/templates/settings.html:44
msgid "Online reading"
msgstr ""

#: application/templates/settings.html:48
msgid "Retention days for online books"
msgstr ""

#: application/templates/settings.html:48
msgid "Web shelf"
msgstr ""

#: application/templates/settings.html:53
#: application/templates/settings.html:176
msgid "2 Days"
msgstr ""

#: application/templates/settings.html:54
#: application/templates/settings.html:177
msgid "3 Days"
msgstr ""

#: application/templates/settings.html:55
#: application/templates/settings.html:178
msgid "4 Days"
msgstr ""

#: application/templates/settings.html:56
#: application/templates/settings.html:179
msgid "5 Days"
msgstr ""

#: application/templates/settings.html:57
#: application/templates/settings.html:180
msgid "6 Days"
msgstr ""

#: application/templates/settings.html:64
msgid "Time zone"
msgstr ""

#: application/templates/settings.html:89
msgid "Delivery time"
msgstr ""

#: application/templates/settings.html:97
msgid "Book type"
msgstr ""

#: application/templates/settings.html:104
msgid "Determines final file size"
msgstr ""

#: application/templates/settings.html:104
msgid "Device type"
msgstr ""

#: application/templates/settings.html:113
msgid "Title format"
msgstr ""

#: application/templates/settings.html:116
msgid "Title Only"
msgstr ""

#: application/templates/settings.html:130
msgid "Remove hyperlinks"
msgstr ""

#: application/templates/settings.html:133
msgid "Do not remove hyperlinks"
msgstr ""

#: application/templates/settings.html:134
msgid "Remove image links"
msgstr ""

#: application/templates/settings.html:135
msgid "Remove text links"
msgstr ""

#: application/templates/settings.html:136
msgid "Remove all hyperlinks"
msgstr ""

#: application/templates/settings.html:140
msgid "Navbar"
msgstr ""

#: application/templates/settings.html:144
msgid "Top Center"
msgstr ""

#: application/templates/settings.html:145
msgid "Top Left"
msgstr ""

#: application/templates/settings.html:146
msgid "Bottom Center"
msgstr ""

#: application/templates/settings.html:147
msgid "Bottom Left"
msgstr ""

#: application/templates/settings.html:159
msgid "Sets the lookup dictionary"
msgstr ""

#: application/templates/settings.html:171
msgid "Oldest article"
msgstr ""

#: application/templates/settings.html:185
msgid "Time format"
msgstr ""

#: application/templates/settings.html:198
msgid "Author format"
msgstr ""

#: application/templates/settings.html:215
msgid "Send Mail Service"
msgstr ""

#: application/templates/settings.html:217
msgid "Service"
msgstr ""

#: application/templates/settings.html:225
msgid "ApiKey"
msgstr ""

#: application/templates/settings.html:229
msgid "SecretKey"
msgstr ""

#: application/templates/settings.html:237
msgid "Port"
msgstr ""

#: application/templates/settings.html:249
msgid "Save path"
msgstr ""

#: application/templates/settings.html:257
#, python-format
msgid ""
"Important: Please activate your kindle firstly, then goto %(personal)s "
"Page and add %(sender)s to 'Approved Personal Document E-mail List'."
msgstr ""

#: application/templates/settings.html:257
msgid "Personal Document Settings"
msgstr ""

#: application/templates/settings.html:263
#, python-format
msgid ""
"You have not yet set up your email address. Please go to the %(admin)s "
"page to add your email address firstly."
msgstr ""

#: application/templates/settings.html:271
msgid "Send Test Email"
msgstr ""

#: application/templates/signup.html:38
msgid "Invitation code"
msgstr ""

#: application/templates/user_account.html:3
msgid "User account"
msgstr ""

#: application/templates/user_account.html:42
msgid "Never expire"
msgstr ""

#: application/templates/webmail.html:3
msgid "Webmail"
msgstr ""

#: application/templates/webmail.html:17
msgid "Refresh"
msgstr ""

#: application/templates/webmail.html:18
msgid "Read/Unread"
msgstr ""

#: application/templates/webmail.html:19
msgid "Reply"
msgstr ""

#: application/templates/webmail.html:20
msgid "Forward"
msgstr ""

#: application/templates/webmail.html:21
msgid "Fwd as Attach"
msgstr ""

#: application/templates/word_lookup.html:71
msgid "Word"
msgstr ""

#: application/view/admin.py:48 application/view/adv.py:437
#: application/view/adv.py:528 application/view/settings.py:67
#: application/view/translator.py:88 application/view/translator.py:172
#: application/view/translator.py:254
msgid "Settings Saved!"
msgstr ""

#: application/view/admin.py:57 application/view/admin.py:64
#: application/view/admin.py:91
msgid "Add account"
msgstr ""

#: application/view/admin.py:63 application/view/admin.py:103
#: application/view/admin.py:135
msgid "You do not have sufficient privileges."
msgstr ""

#: application/view/admin.py:79 application/view/login.py:44
#: application/view/login.py:222
msgid "The username includes unsafe chars."
msgstr ""

#: application/view/admin.py:83 application/view/login.py:224
msgid "Already exist the username."
msgstr ""

#: application/view/admin.py:88
msgid "The password includes non-ascii chars."
msgstr ""

#: application/view/admin.py:107 application/view/admin.py:132
#: application/view/admin.py:163 application/view/extension.py:32
#: application/view/extension.py:61
msgid "The username '{}' does not exist."
msgstr ""

#: application/view/admin.py:123
msgid "The password will not be changed if the fields are empties."
msgstr ""

#: application/view/admin.py:130 application/view/admin.py:184
msgid "Change"
msgstr ""

#: application/view/admin.py:181
msgid "Change success."
msgstr ""

#: application/view/admin.py:194
msgid "The old password is wrong."
msgstr ""

#: application/view/admin.py:196
msgid "Changes saved successfully."
msgstr ""

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108 application/view/adv.py:109
#: application/view/adv.py:110 application/view/adv.py:111
#: application/view/adv.py:112 application/view/adv.py:113
msgid "Append hyperlink '{}' to article"
msgstr ""

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108
msgid "Save to {}"
msgstr ""

#: application/view/adv.py:104
msgid "evernote"
msgstr ""

#: application/view/adv.py:105
msgid "wiz"
msgstr ""

#: application/view/adv.py:106
msgid "pocket"
msgstr ""

#: application/view/adv.py:107
msgid "instapaper"
msgstr ""

#: application/view/adv.py:108
msgid "wallabag"
msgstr ""

#: application/view/adv.py:109 application/view/adv.py:110
#: application/view/adv.py:111 application/view/adv.py:112
msgid "Share on {}"
msgstr ""

#: application/view/adv.py:109
msgid "weibo"
msgstr ""

#: application/view/adv.py:110
msgid "facebook"
msgstr ""

#: application/view/adv.py:112
msgid "tumblr"
msgstr ""

#: application/view/adv.py:113
msgid "Open in browser"
msgstr ""

#: application/view/adv.py:114
msgid "Append qrcode of url to article"
msgstr ""

#: application/view/adv.py:381 application/view/share.py:54
#: application/view/subscribe.py:250
msgid "Unknown command: {}"
msgstr ""

#: application/view/adv.py:439 application/view/adv.py:530
msgid "The format is invalid."
msgstr ""

#: application/view/adv.py:562
msgid "Authorization Error!<br/>{}"
msgstr ""

#: application/view/adv.py:583
msgid "Success authorized by Pocket!"
msgstr ""

#: application/view/adv.py:589
msgid ""
"Failed to request authorization of Pocket!<hr/>See details "
"below:<br/><br/>{}"
msgstr ""

#: application/view/adv.py:610
msgid "The Instapaper service encountered an error. Please try again later."
msgstr ""

#: application/view/adv.py:623
msgid "Request type [{}] unsupported"
msgstr ""

#: application/view/deliver.py:82 application/view/login.py:169
#: application/view/share.py:41
msgid "The username does not exist or the email is empty."
msgstr ""

#: application/view/deliver.py:109
msgid "The following recipes has been added to the push queue."
msgstr ""

#: application/view/deliver.py:112
msgid "There are no recipes to deliver."
msgstr ""

#: application/view/extension.py:69
msgid "The rules parameter is invalid."
msgstr ""

#: application/view/library.py:32
msgid "Cannot fetch data from {}, status: {}"
msgstr ""

#: application/view/library.py:48 application/view/subscribe.py:238
#: application/view/subscribe.py:380 application/view/subscribe.py:409
#: application/view/subscribe.py:416 application/view/translator.py:30
msgid "The recipe does not exist."
msgstr ""

#: application/view/login.py:27 application/view/login.py:76
msgid "Please use {}/{} to login at first time."
msgstr ""

#: application/view/login.py:40
msgid "Username is empty."
msgstr ""

#: application/view/login.py:42
msgid "The len of username reached the limit of 25 chars."
msgstr ""

#: application/view/login.py:80
msgid "Forgot password?"
msgstr ""

#: application/view/login.py:148 application/view/login.py:275
msgid "The token is wrong or expired."
msgstr ""

#: application/view/login.py:151
msgid "Please input the correct username and email to reset password."
msgstr ""

#: application/view/login.py:153
msgid "The email of account '{name}' is {email}."
msgstr ""

#: application/view/login.py:174
msgid "Reset password success, Please close this page and login again."
msgstr ""

#: application/view/login.py:177
msgid "The email you input is not associated with this account."
msgstr ""

#: application/view/login.py:186
msgid "The link to reset your password has been sent to your email."
msgstr ""

#: application/view/login.py:187
msgid "Please check your email inbox within 24 hours."
msgstr ""

#: application/view/login.py:218
msgid "The invitation code is invalid."
msgstr ""

#: application/view/login.py:226
msgid ""
"Failed to create an account. Please contact the administrator for "
"assistance."
msgstr ""

#: application/view/login.py:236
msgid "Successfully created account."
msgstr ""

#: application/view/login.py:247
msgid "Reset KindleEar password"
msgstr ""

#: application/view/login.py:248
msgid "This is an automated email. Please do not reply to it."
msgstr ""

#: application/view/login.py:249
msgid "You can click the following link to reset your KindleEar password."
msgstr ""

#: application/view/reader.py:88
msgid "The article is missing?"
msgstr ""

#: application/view/reader.py:190 application/view/translator.py:121
#: application/view/translator.py:205 application/view/translator.py:287
msgid "The text is empty."
msgstr ""

#: application/view/reader.py:239
msgid "No definitions found for '{}'."
msgstr ""

#: application/view/reader.py:240
msgid "Did you mean?"
msgstr ""

#: application/view/reader.py:324 application/view/reader.py:331
msgid "Failed to push: {}"
msgstr ""

#: application/view/reader.py:379
msgid "Failed to create ebook."
msgstr ""

#: application/view/settings.py:131
msgid ""
"You have not yet set up your email address. Please go to the 'Account' "
"page to add your email address firstly."
msgstr ""

#: application/view/settings.py:215
msgid "English"
msgstr ""

#: application/view/settings.py:216
msgid "Simplified Chinese"
msgstr ""

#: application/view/settings.py:217
msgid "Traditional Chinese"
msgstr ""

#: application/view/settings.py:218
msgid "French"
msgstr ""

#: application/view/settings.py:219
msgid "Spanish"
msgstr ""

#: application/view/settings.py:220
msgid "Portuguese"
msgstr ""

#: application/view/settings.py:221
msgid "German"
msgstr ""

#: application/view/settings.py:222
msgid "Italian"
msgstr ""

#: application/view/settings.py:223
msgid "Japanese"
msgstr ""

#: application/view/settings.py:224
msgid "Russian"
msgstr ""

#: application/view/settings.py:225
msgid "Turkish"
msgstr ""

#: application/view/settings.py:226
msgid "Korean"
msgstr ""

#: application/view/settings.py:227
msgid "Arabic"
msgstr ""

#: application/view/settings.py:228
msgid "Czech"
msgstr ""

#: application/view/settings.py:229
msgid "Dutch"
msgstr ""

#: application/view/settings.py:230
msgid "Greek"
msgstr ""

#: application/view/settings.py:231
msgid "Hindi"
msgstr ""

#: application/view/settings.py:232
msgid "Malaysian"
msgstr ""

#: application/view/settings.py:233
msgid "Bengali"
msgstr ""

#: application/view/settings.py:234
msgid "Persian"
msgstr ""

#: application/view/settings.py:235
msgid "Urdu"
msgstr ""

#: application/view/settings.py:236
msgid "Swahili"
msgstr ""

#: application/view/settings.py:237
msgid "Vietnamese"
msgstr ""

#: application/view/settings.py:238
msgid "Punjabi"
msgstr ""

#: application/view/settings.py:239
msgid "Javanese"
msgstr ""

#: application/view/settings.py:240
msgid "Tagalog"
msgstr ""

#: application/view/settings.py:241
msgid "Hausa"
msgstr ""

#: application/view/settings.py:242
msgid "Thai"
msgstr ""

#: application/view/settings.py:243
msgid "Polish"
msgstr ""

#: application/view/settings.py:244
msgid "Romanian"
msgstr ""

#: application/view/settings.py:245
msgid "Hungarian"
msgstr ""

#: application/view/settings.py:246
msgid "Swedish"
msgstr ""

#: application/view/settings.py:247
msgid "Hebrew"
msgstr ""

#: application/view/settings.py:248
msgid "Norwegian"
msgstr ""

#: application/view/settings.py:249
msgid "Finnish"
msgstr ""

#: application/view/settings.py:250
msgid "Danish"
msgstr ""

#: application/view/settings.py:251
msgid "Ukrainian"
msgstr ""

#: application/view/settings.py:252
msgid "Tamil"
msgstr ""

#: application/view/settings.py:253
msgid "Marathi"
msgstr ""

#: application/view/settings.py:254
msgid "Burmese"
msgstr ""

#: application/view/settings.py:255
msgid "Amharic"
msgstr ""

#: application/view/settings.py:256
msgid "Azerbaijani"
msgstr ""

#: application/view/settings.py:257
msgid "Kazakh"
msgstr ""

#: application/view/settings.py:258
msgid "Serbian"
msgstr ""

#: application/view/settings.py:259
msgid "Croatian"
msgstr ""

#: application/view/settings.py:260
msgid "Slovak"
msgstr ""

#: application/view/settings.py:261
msgid "Bulgarian"
msgstr ""

#: application/view/settings.py:262
msgid "Icelandic"
msgstr ""

#: application/view/settings.py:263
msgid "Lithuanian"
msgstr ""

#: application/view/settings.py:264
msgid "Latvian"
msgstr ""

#: application/view/settings.py:265
msgid "Estonian"
msgstr ""

#: application/view/settings.py:266
msgid "Macedonian"
msgstr ""

#: application/view/settings.py:267
msgid "Albanian"
msgstr ""

#: application/view/settings.py:268
msgid "Galician"
msgstr ""

#: application/view/settings.py:269
msgid "Welsh"
msgstr ""

#: application/view/settings.py:270
msgid "Basque"
msgstr ""

#: application/view/settings.py:271
msgid "Nepali"
msgstr ""

#: application/view/share.py:60
msgid "There is no {} email yet."
msgstr ""

#: application/view/share.py:108 application/view/share.py:133
#: application/view/share.py:155 application/view/share.py:177
msgid "Saved to your {} account."
msgstr ""

#: application/view/share.py:111 application/view/share.py:129
#: application/view/share.py:158 application/view/share.py:179
msgid "Failed save to {}."
msgstr ""

#: application/view/share.py:112 application/view/share.py:130
#: application/view/share.py:159 application/view/share.py:180
msgid "Reason :"
msgstr ""

#: application/view/share.py:121
msgid "Unauthorized {} account!"
msgstr ""

#: application/view/share.py:134
msgid "See details below:"
msgstr ""

#: application/view/share.py:157
msgid "Unknown: {}"
msgstr ""

#: application/view/subscribe.py:81 application/view/subscribe.py:167
msgid "Duplicated subscription!"
msgstr ""

#: application/view/subscribe.py:126
msgid "The Title or Url is empty."
msgstr ""

#: application/view/subscribe.py:139
msgid "Failed to fetch the recipe."
msgstr ""

#: application/view/subscribe.py:153 application/view/subscribe.py:331
msgid "Failed to save the recipe. Error:"
msgstr ""

#: application/view/subscribe.py:195
msgid "The Rss does not exist."
msgstr ""

#: application/view/subscribe.py:278
msgid "You can only delete the uploaded recipe."
msgstr ""

#: application/view/subscribe.py:283
msgid "The recipe have been subscribed, please unsubscribe it before delete."
msgstr ""

#: application/view/subscribe.py:304 application/view/translator.py:51
#: application/view/translator.py:104 application/view/translator.py:117
#: application/view/translator.py:140 application/view/translator.py:188
#: application/view/translator.py:201 application/view/translator.py:230
#: application/view/translator.py:270 application/view/translator.py:283
msgid "This recipe has not been subscribed to yet."
msgstr ""

#: application/view/subscribe.py:318
msgid "Can not read uploaded file, Error:"
msgstr ""

#: application/view/subscribe.py:326
msgid ""
"Failed to decode the recipe. Please ensure that your recipe is saved in "
"utf-8 encoding."
msgstr ""

#: application/view/subscribe.py:349
msgid "Cannot find any subclass of BasicNewsRecipe."
msgstr ""

#: application/view/subscribe.py:354
msgid "The recipe is already in the library."
msgstr ""

#: application/view/subscribe.py:387
msgid "The login information for this recipe has been cleared."
msgstr ""

#: application/view/subscribe.py:391
msgid "The login information for this recipe has been saved."
msgstr ""

#: application/view/translator.py:81 application/view/translator.py:165
msgid "The api key is required."
msgstr ""

