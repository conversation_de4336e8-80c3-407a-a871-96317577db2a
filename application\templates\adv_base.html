{% extends "base.html" %}

{% block css -%}
<style type="text/css">
.submenu-box {
    padding: .5em 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.submenu-box .pure-menu-selected {
    font-weight: bold;
    color: #0078e7;
}

.submenu-box span {
    display: block;
    padding: .5em 1em;
    cursor: default;
}

@media (min-width: 768px) {
    .submenu-box {
        margin-right: 20px;
    }
}
</style>
{% endblock -%}

{% block content -%}
<div class="main">
  <div class="pure-g">
    <div class="pure-u-1 pure-u-md-7-24">
      <div class="pure-menu">
        <ul class="pure-menu-list submenu-box">
          {% if advCurr=='deliverNow' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Deliver Now")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/delivernow" class="pure-menu-link">{{_("Deliver Now")}} </a>
          </li>
          {% endif -%}
          {% if advCurr=='inboundMail' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Inbound Mail")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/inboundmail" class="pure-menu-link">{{_("Inbound Mail")}} </a>
          </li>
          {% endif -%}
          {% if advCurr=='archive' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Archive")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/archive" class="pure-menu-link">{{_("Archive")}} </a>
          </li>
          {% endif -%}
          {% if advCurr=='dictionary' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Dictionary")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/dict" class="pure-menu-link">{{_("Dictionary")}}</a>
          </li>
          {% endif -%}
          {% if advCurr=='proxy' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Proxy")}}
              {%if hasProxy -%}
              <sup style="font-size:0.8em;margin-left:-5px;font-weight:bold;font-style:normal;border:none;">●</sup>
              {% endif -%}
            </span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/proxy" class="pure-menu-link">{{_("Proxy")}}
            {%if hasProxy -%}
            <sup style="font-size:0.8em;margin-left:-5px;font-weight:bold;font-style:normal;border:none;">●</sup>
            {% endif -%}
            </a>
          </li>
          {% endif -%}
          {% if advCurr=='import' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Import Feeds")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/import" class="pure-menu-link">{{_("Import Feeds")}} </a>
          </li>
          {% endif -%}
          {% if advCurr=='uploadCover' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Cover Image")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/cover" class="pure-menu-link">{{_("Cover Image")}}</a>
          </li>
          {% endif -%}
          {% if advCurr=='uploadCss' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Stylesheet")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/css" class="pure-menu-link">{{_("Stylesheet")}}</a>
          </li>
          {% endif -%}
          {% if advCurr=='calibreOptions' -%}
          <li class="pure-menu-item pure-menu-selected">
            <span>{{_("Calibre Options")}}</span>
          </li>
          {% else -%}
          <li class="pure-menu-item">
            <a href="/adv/calibre" class="pure-menu-link">{{_("Calibre Options")}}</a>
          </li>
          {% endif -%}
        </ul>
      </div>
    </div>
    <div class="pure-u-1 pure-u-md-17-24">
      {% block advcontent -%}
      {% endblock -%}
    </div>
  </div>
</div>
{% endblock -%}