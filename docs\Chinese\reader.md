---
sort: 4
---
# 在线阅读


## 概述
KindleEar支持邮件推送和在线阅读，内置一个为电子墨水屏进行专门优化的在线阅读器。    
邮件推送的好处是可以离线阅读，下载到电子书设备后可以随身携带随时阅读；在线阅读的好处是不占用电子书设备空间，不需要手动删除电子书，而且可以支持更多的电子书设备。   

## 启用和配置
1. 配置 `EBOOK_SAVE_DIR` 为一个有可写权限的目录，可以在config.py里面修改或通过环境变量传递。 

2. 在线阅读功能仅 Docker/VPS 平台支持，如果你使用的是作者预先制作的Docker镜像，则在线阅读功能默认已经开启。（因GAE平台没有可写的磁盘权限，所以无法启用在线阅读功能。）   

3. 建议在网页的 "设置" 页面 设置合适的 "网络书架" 时间， KindleEar会自动清理超过这个时间的电子书，省心省力。   

4. 在线阅读页面专门为电子墨水屏优化，使用翻页代替滚动，页面被分成5个点击区域：    
  * 左上角默认为激活查词模式，可以通过选项菜单调整为 "打开前一篇文章"   
  * 右上角为 "下一篇文章"   
  * 页面上部中间区域为 "打开菜单"   
  * 左侧中间部分为 "上一页"   
  * 页面的其余区域全部为 "下一页"，不管使用左手或右手持书，都很便捷的切换下一页   
  * 到当前文章的最后一页继续点击下一页区域自动打开下一篇文章   
  * 在文章第一页点击上一页区域自动打开上一篇文章    

5. 快捷键
  * 空格键，右方向键，PageDown - 下一篇文章
  * 左方向键 - 上一篇文章
  * 上方向键 - 上一本书
  * 下方向键 - 下一本书

6. Kindle浏览器不支持cookie持久化，每次打开都需要输入账号密码，为了避免这个麻烦，可以在书签中添加账号和密码的查询字符串：    
`https://youdomain/reader?username=YourName&password=YourPassword`     


7. 如果需要有的订阅需要推送，有的订阅仅需要在线阅读，可以创建两个账号，一个用于推送一个用于在线阅读。   


## 词典功能  
因为Kindle浏览器限制太大，所以只是有限的支持点击查词功能，无法支持划词翻译，也无法支持词组，如果需要很多的查词动作，还是建议推送到Kindle再阅读。   
查词原理是通过自动提取最靠近点击区域的一个单词，单词前后需要有空格符，所以中日韩只可以查询单个字，不能查询词组。  
提取出单词后，发送给你部署的KindleEar网站翻译，接收到答复后再显示出来。   


### 安装词典
1. KindleEar支持在线词典 [dict.org](https://dict.org/), [dict.cc](https://www.dict.cc/), [dict.cn](http://dict.cn/), [韦氏词典](https://www.merriam-webster.com/)，[牛津词典](https://www.oxfordlearnersdictionaries.com/)， 这几个词典不需要安装，开箱即用。    
2. 在线词典很方便，但是避免有时候因为网络原因不是太稳定，所以如果要稳定使用，最好还是使用离线词典，为此，KindleEar同时支持 mdict/stardict/lingvo/babylon 格式词典，下载对应的词典后，解压到 `data/dict` 目录（可以使用子目录整理不同的词典）。   
  * mdict: 只需要 mdx 文件，如果有css，则需要位于同样目录   
  * stardict: 需要 ifo, idx, dict 或 dict.dz   
  * lingvo: 只需要 dsl 文件，不支持 dsl.dz，需要将 dsl.dz 解压缩为 dsl (使用gzip/7zip/winrar等软件)     
  * babylon: 只需要 bgl 文件 （如果查词乱码，可以修改 \*.enc 文件为准确的编码）    
3. 离线词典第一次查词会比较慢，因为要创建索引文件(后缀为trie)，之后就很快了。 
如果要使用大型词典（比如几百兆以上），在生成索引的过程中会消耗比较多的内存，如你的服务器内存比较小，可能会创建索引失败，你可以在你的本地机器先使用对应词典查一次单词，待本地生成trie文件后，拷贝到服务器对应目录即可。   
4. 已经默认支持美式英语的构词法规则，可以查询单词时态语态复数等变形，如果需要支持其他语种的构词法，请下载对应的hunspell格式的文件（.dic/.aff），然后拷贝到 `data/dict/morphology` (请直接创建此目录) ，注意不要存放到子目录下，KindleEar会自动使用和书本语言相匹配的构词法规则。   
至于到哪里下载Hunspell/MySpell构词法文件，可以到github/sourceforge等网站上搜索，下面是几个直链。  
[LibreOffice](https://github.com/LibreOffice/dictionaries)    
[Firefox](https://addons.mozilla.org/en-US/firefox/language-tools/)    
[sztaki](http://hlt.sztaki.hu/resources/hunspell/)     
[wooorm](https://github.com/wooorm/dictionaries)    


### 使用词典
1. 为了使用词典功能，首先在 "高级设置"/"词典" 选项页里面配置需要使用的翻译引擎和语种。   

2. 在阅读时遇到不认识的单词：     
2.1. 如果已经激活左上角词典模式（默认），则点击页面左上角进入查词模式；     
2.2. 打开菜单，点击 "词典" 图标；        
任意一种方式进入查词模式后，页面左上角会出现一个词典角标，点击需要查询的单词即可，查词后会自动退出查词模式。   

3. 点击弹出的查词框内任意地方关闭查词框。    


## 已知BUG  
1. chrome有部分文章的显示内容不完整   
这个是chrome内核的iframe高度计算不准确导致，暂时没有很好的解决方案，如果碰到此情况，可以使用 Ctrl+Click 手动扩展阅读页面的高度，以让更多的内容显示出来，每次点击扩展200像素。   

