{% extends "base.html" %}
{% block titleTag -%}
<title>{{_("Word lookup")}} - KindleEar</title>
{% endblock -%}
{% block menubar -%}
{% endblock -%}
{% block css %}
<style type="text/css">
  .pure-form select { /* override pure css */
    height: 1.5em;
    border: 1px solid #ccc;
    background-color: white;
    padding: 0px 10px;
  }
  .pure-form input {
    height: 1.8em;
    border: 1px solid #ccc;
    background-color: white;
    padding: 10px 10px;
  }
  .tr_container {
    margin: 20px;
    padding: 0px 10px 40px 10px;
    border-top: 1px solid #ccc;
  }
  .tr_container span {
    margin: 5px;
    padding: 0px;
  }
  .tr_engine {
    display: none;
    margin: 10px 0px 20px 10px;
    padding: 20px 30px 20px 30px;
    border: 1px solid #ccc;
    border-radius: 30px;
    box-shadow: 2px 2px 5px #ccc;
    background-color: #F5F5F5;
  }
</style>
{% endblock -%}

{% block content -%}
<div>
  <form class="pure-form pure-form-aligned" action="" method="POST" onsubmit="return false;">
    {% if tips -%}
    <div class="notice-box error">{{tips}}</div>
    {% endif -%}

    <fieldset>
      <div class="pure-control-group">
        <label>{{_("Source language")}}</label>
        <select name="language" id="language" class="pure-u-1 pure-u-sm-1-2">
          {% for code,name in langMap.items() -%}
          <option value="{{code}}">{{name}}</option>
          {% endfor -%}
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Engine")}}</label>
        <select name="engine" id="engine" class="pure-u-1 pure-u-sm-1-2" onchange="DictEngineChanged()">
          <!-- 由脚本填充 -->
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Database")}}</label>
        <select name="database" id="database" class="pure-u-1 pure-u-sm-1-2">
          <!-- 由脚本填充 -->
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Word")}}</label>
        <input type="text" name="text" id="search_for" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group">
        <label>
          <button class="pure-button pure-button-disabled" id="history-back" onclick="historyBack()">&#9665;</button>
          <button class="pure-button pure-button-disabled" id="history-forward" onclick="historyForward()">&#9655;</button>
        </label>
        <button class="pure-button pure-input-rounded pure-button-primary" onclick="startQueryDict()" style="margin-left:2em">{{_('Word lookup')}}</button>
      </div>
    </fieldset>
  </form>
  <div class="tr_container">
    <div class="tr_engine" id="tr_engine"></div>
    <div class="tr_result" id="tr_result"></div>
  </div>
</div>
{% endblock -%}

{% block js -%}
<script type="text/javascript">
var g_dictEngines = {{engines|safe}};
var g_lastLanguage = '';
var g_lastEngine = '';
var g_lastDatabase = '';
var g_history = [];
var g_currHisIdx = -1;

//填充可用的引擎列表
function PopulateDictEngines() {
  var engineSel = $('#engine');
  engineSel.empty();
  for (var name in g_dictEngines) {
    var txt = `<option value="${name}">${name}</option>`;
    engineSel.append($(txt));
  }
}

//词典引擎变化时填充数据库名字列表
function DictEngineChanged() {
  var engineSel = $('#engine');
  var dbSel = $('#database');
  var engine = engineSel.val();
  dbSel.empty();
  var databases = g_dictEngines[engine].databases;
  if (!databases) {
    return;
  }
  for (var name in databases) {
    var txt = `<option value="${name}">${databases[name]}</option>`;
    dbSel.append($(txt));
  }
}

//开始查词，可以传入一个word，否则内部获取网页上的word
function startQueryDict(word) {
  var language = $('#language').val();
  var engine = $('#engine').val();
  var database = $('#database').val();
  if (word) {
    $('#search_for').val(word);
  } else {
    word = $('#search_for').val();
    if (word) { //保存查词历史
      addToHistory(word);
    } else {
      return;
    }
  }
  
  $.post('/reader/dict', {word: word, language: language, engine: engine, database: database}, function (resp) {
    if (resp.status == 'ok') {
      showTrResult(word, resp.definition, resp.dictname);
    } else {
      alert(resp.status);
    }

    //保存当前配置
    if (g_lastLanguage != language) {
      window.localStorage.setItem('lastDictLanguage', language);
    }
    if (g_lastEngine != engine) {
      window.localStorage.setItem('lastDictEngine', engine);
    }
    if (g_lastDatabase != database) {
      window.localStorage.setItem('lastDictDatabase', database);
    }
    g_lastLanguage = language;
    g_lastEngine = engine;
    g_lastDatabase = database;
  });
}

//显示翻译结果
function showTrResult(word, text, dictname) {
  var engineDiv = $('#tr_engine');
  var resultDiv = $('#tr_result');
  var resultElement = resultDiv[0];
  engineDiv.html('From: <strong>' + dictname + '</strong>');
  engineDiv.css('display', text ? 'inline-block' : 'none');
  text = text ? text.replace(/\n/g, '<br/>') : ('No definitions found for "' + word + '"');
  if (resultElement.attachShadow) { //使用shadow dom技术隔离css
    //console.log('This browser supports Shadow DOM.');
    if (!resultElement.shadowRoot) { //在第一个执行attachShadow后，这个变量会自动被设置
      resultElement.attachShadow({mode: 'open'});
      resultElement.shadowRoot.addEventListener('click', AClickEvent);
    }
    resultElement.shadowRoot.innerHTML = text;
  } else { //不支持shadow dom的浏览器
    resultDiv.html(text);
  }
}

//将一个新词添加到历史列表
function addToHistory(word) {
  if ((g_history.length == 0) || (g_history[g_history.length - 1] != word)) {
    g_history.push(word);
  }
  g_currHisIdx = g_history.length - 1;
  if (g_history.length > 1) {
    $('#history-back').removeClass('pure-button-disabled');
    $('#history-forward').addClass('pure-button-disabled');
  }
}

//返回前一个查词结果
function historyBack() {
  if (g_currHisIdx > 0) {
    g_currHisIdx--;
    startQueryDict(g_history[g_currHisIdx]);
    $('#history-forward').removeClass('pure-button-disabled');
  }
  if (g_currHisIdx <= 0) {
    $('#history-back').addClass('pure-button-disabled');
  }
}

//返回后一个查词结果
function historyForward() {
  if (g_currHisIdx < g_history.length - 1) {
    g_currHisIdx++;
    startQueryDict(g_history[g_currHisIdx]);
    $('#history-back').removeClass('pure-button-disabled');
  }
  if (g_currHisIdx >= g_history.length - 1) {
    $('#history-forward').addClass('pure-button-disabled');
  }
}

//添加链接事件
function AClickEvent(event) {
  var target = event ? event.target : null;
  if (!target) {
    return;
  }
  var href = $(target).attr('href') || '';
  if (href.startsWith('https://kindleear/entry/')) {
    event.preventDefault();
    event.stopPropagation();
    var word = href.substring(24);
    if (word) {
      startQueryDict(word);
      addToHistory(word);
    }
  }
}

//注册各种事件
function regEvents() {
  $("#search_for").keydown(function(event) {
    if (event.keyCode === 13) {
      event.preventDefault();
      startQueryDict();
    }
  });

  //点击词典内的跳转，可以直接查其他词
  $('#tr_result').on('click', 'a', AClickEvent);
}

//自动选择上次使用的词典
function restoreLast() {
  g_lastLanguage = window.localStorage.getItem('lastDictLanguage');
  g_lastEngine = window.localStorage.getItem('lastDictEngine');
  g_lastDatabase = window.localStorage.getItem('lastDictDatabase');
  if (g_lastLanguage) {
    $('#language').val(g_lastLanguage);
  }
  if (g_lastEngine) {
    var sel = $('#engine');
    sel.val(g_lastEngine);
    if (!sel.val()) {
      sel.prop('selectedIndex', 0);
    }
    DictEngineChanged();
  }
  if (g_lastDatabase) {
    var sel = $('#database');
    sel.val(g_lastDatabase);
    if (!sel.val()) {
      sel.prop('selectedIndex', 0);
    }
  }
}

//初始化页面
$(document).ready(function() {
  PopulateDictEngines();
  DictEngineChanged();
  restoreLast();
  regEvents();
});
</script>
{% endblock -%}
{% block footer -%}
{% endblock -%}
