# Chinese translations for KindleEar.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the KindleEar project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: KindleEar v3.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-10 19:49-0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: KindleEar <<EMAIL>>\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.14.0\n"

#: application/templates/admin.html:3 application/templates/base.html:53
#: application/templates/base.html:192 application/templates/settings.html:263
msgid "Account"
msgstr "계정"

#: application/templates/admin.html:19
msgid "Signup settings"
msgstr "가입 설정"

#: application/templates/admin.html:19
#: application/templates/adv_calibre_options.html:18
#: application/templates/adv_proxy.html:18
msgid "Save"
msgstr "저장"

#: application/templates/admin.html:21
#: application/templates/user_account.html:31
msgid "Email service"
msgstr "이메일 서비스"

#: application/templates/admin.html:23
#: application/templates/user_account.html:34
msgid "Same as admin"
msgstr "관리자와 동일"

#: application/templates/admin.html:24
#: application/templates/user_account.html:35
msgid "Independent"
msgstr "독립적"

#: application/templates/admin.html:28
msgid "Signup type"
msgstr "가입 유형"

#: application/templates/admin.html:30
msgid "Public"
msgstr "공개"

#: application/templates/admin.html:31
msgid "One time code"
msgstr "일회성 코드"

#: application/templates/admin.html:32
msgid "Permanent code"
msgstr "영구 코드"

#: application/templates/admin.html:36
msgid "Invitation codes"
msgstr "초대 코드"

#: application/templates/admin.html:37
msgid "one code per line"
msgstr "한 줄에 하나의 코드"

#: application/templates/admin.html:43
msgid "Accounts"
msgstr "계정"

#: application/templates/admin.html:43
#: application/templates/adv_inboundmail.html:61
#: application/templates/my.html:34 application/view/admin.py:57
#: application/view/admin.py:64 application/view/admin.py:91
msgid "Add"
msgstr "추가"

#: application/templates/admin.html:54
#: application/templates/adv_archive.html:69
#: application/templates/home.html:19 application/templates/login.html:24
#: application/templates/logs.html:70
#: application/templates/reset_password.html:19
#: application/templates/reset_password.html:20
#: application/templates/settings.html:241
#: application/templates/signup.html:21
#: application/templates/user_account.html:15
msgid "Username"
msgstr "사용자 이름"

#: application/templates/admin.html:55
msgid "AutoSend"
msgstr "자동 전송"

#: application/templates/admin.html:56
#: application/templates/change_password.html:27
#: application/templates/reset_password.html:26
#: application/templates/signup.html:33
#: application/templates/user_account.html:27
msgid "Email"
msgstr "Email"

#: application/templates/admin.html:57
#: application/templates/user_account.html:39
msgid "Expiration"
msgstr "만료"

#: application/templates/admin.html:58
msgid "Operation"
msgstr "작업"

#: application/templates/admin.html:65
msgid "Yes"
msgstr "예"

#: application/templates/admin.html:65
msgid "No"
msgstr "아니요"

#: application/templates/admin.html:68
msgid "Never"
msgstr "없음"

#: application/templates/admin.html:70
#: application/templates/adv_inboundmail.html:26
#: application/templates/settings.html:58
#: application/templates/settings.html:181
#: application/templates/user_account.html:43
msgid "7 Days"
msgstr "7일"

#: application/templates/admin.html:72
#: application/templates/user_account.html:44
msgid "1 Month"
msgstr "1개월"

#: application/templates/admin.html:74
#: application/templates/user_account.html:45
msgid "3 Months"
msgstr "3개월"

#: application/templates/admin.html:76
#: application/templates/user_account.html:46
msgid "6 Months"
msgstr "6개월"

#: application/templates/admin.html:78
#: application/templates/user_account.html:47
msgid "1 Year"
msgstr "1년"

#: application/templates/admin.html:80
#: application/templates/user_account.html:48
msgid "2 Years"
msgstr "2년"

#: application/templates/admin.html:85
#: application/templates/change_password.html:3
#: application/templates/change_password.html:13 application/view/admin.py:129
#: application/view/admin.py:183
msgid "Edit account"
msgstr "계정 수정"

#: application/templates/admin.html:89
#: application/templates/adv_inboundmail.html:54
#: application/templates/adv_uploadcss.html:31
#: application/templates/base.html:25 application/templates/webmail.html:22
msgid "Delete"
msgstr "삭제"

#: application/templates/adv_archive.html:3
#: application/templates/adv_archive.html:14
#: application/templates/adv_base.html:57
#: application/templates/adv_base.html:61
msgid "Archive"
msgstr "보관"

#: application/templates/adv_archive.html:15
msgid "Append hyperlinks for archiving or sharing."
msgstr "보관 또는 공유를 위한 하이퍼링크 추가."

#: application/templates/adv_archive.html:40
msgid "Authorized"
msgstr "인증됨"

#: application/templates/adv_archive.html:42
msgid "Authorize"
msgstr "인증"

#: application/templates/adv_archive.html:53
msgid "Email or Username"
msgstr "이메일 또는 사용자 이름"

#: application/templates/adv_archive.html:56
#: application/templates/adv_archive.html:72
#: application/templates/base.html:54 application/templates/home.html:20
#: application/templates/login.html:28 application/templates/settings.html:245
#: application/templates/signup.html:25
#: application/templates/user_account.html:19
msgid "Password"
msgstr "비밀번호"

#: application/templates/adv_archive.html:59
#: application/templates/adv_archive.html:75
#: application/templates/base.html:64
msgid "Verify"
msgstr "확인"

#: application/templates/adv_archive.html:78
msgid "client_id"
msgstr "client_id"

#: application/templates/adv_archive.html:81
msgid "client_secret"
msgstr "client_secret"

#: application/templates/adv_archive.html:84
#: application/templates/settings.html:233
msgid "Host"
msgstr "호스트"

#: application/templates/adv_archive.html:119
#: application/templates/adv_dict.html:79
#: application/templates/adv_inboundmail.html:34
#: application/templates/book_audiolator.html:113
#: application/templates/book_summarizer.html:88
#: application/templates/book_translator.html:80
#: application/templates/settings.html:269
msgid "Save settings"
msgstr "설정 저장"

#: application/templates/adv_base.html:39
#: application/templates/adv_base.html:43
#: application/templates/adv_delivernow.html:8
msgid "Deliver Now"
msgstr "지금 전송"

#: application/templates/adv_base.html:48
#: application/templates/adv_base.html:52
#: application/templates/adv_inboundmail.html:3
#: application/templates/adv_inboundmail.html:9
#: application/templates/adv_inboundmail.html:14
msgid "Inbound Mail"
msgstr "수신 메일"

#: application/templates/adv_base.html:66
#: application/templates/adv_base.html:70
#: application/templates/adv_dict.html:3
#: application/templates/adv_dict.html:13
#: application/templates/reader.html:134
msgid "Dictionary"
msgstr "사전"

#: application/templates/adv_base.html:75
#: application/templates/adv_base.html:83
#: application/templates/adv_proxy.html:3
#: application/templates/adv_proxy.html:12
msgid "Proxy"
msgstr "프록시"

#: application/templates/adv_base.html:92
#: application/templates/adv_base.html:96
#: application/templates/adv_import.html:8
msgid "Import Feeds"
msgstr "피드 가져오기"

#: application/templates/adv_base.html:101
#: application/templates/adv_base.html:105
msgid "Cover Image"
msgstr "표지 이미지"

#: application/templates/adv_base.html:110
#: application/templates/adv_base.html:114
#: application/templates/adv_uploadcss.html:3
msgid "Stylesheet"
msgstr "스타일 시트"

#: application/templates/adv_base.html:119
#: application/templates/adv_base.html:123
#: application/templates/adv_calibre_options.html:3
#: application/templates/adv_calibre_options.html:12
msgid "Calibre Options"
msgstr "칼리버 옵션"

#: application/templates/adv_calibre_options.html:13
msgid "Set the parameters for Calibre, in JSON dictionary format."
msgstr "칼리버 매개변수를 설정합니다. JSON 딕셔너리 형식으로."

#: application/templates/adv_delivernow.html:3
msgid "Deliver now"
msgstr "지금 전송"

#: application/templates/adv_delivernow.html:9
msgid "Deliver selected recipes now."
msgstr "선택된 레시피를 지금 전송."

#: application/templates/adv_delivernow.html:12
msgid "There are no recipes subscribed"
msgstr "구독된 레시피가 없습니다."

#: application/templates/adv_delivernow.html:17
#: application/templates/base.html:101
msgid "Sep"
msgstr "Sep"

#: application/templates/adv_delivernow.html:22
msgid "Select all"
msgstr "모두 선택"

#: application/templates/adv_delivernow.html:23
msgid "Select none"
msgstr "선택 없음"

#: application/templates/adv_delivernow.html:28
msgid "Deliver"
msgstr "전송"

#: application/templates/adv_dict.html:15
msgid "Set up dictionaries for online reading."
msgstr "온라인 읽기를 위한 사전 설정."

#: application/templates/adv_dict.html:18
#: application/templates/adv_dict.html:40
#: application/templates/adv_dict.html:62
msgid "Book language"
msgstr "책 언어"

#: application/templates/adv_dict.html:27
#: application/templates/adv_dict.html:49
#: application/templates/adv_dict.html:66
#: application/templates/book_summarizer.html:23
#: application/templates/book_translator.html:26
#: application/templates/word_lookup.html:59
msgid "Engine"
msgstr "엔진"

#: application/templates/adv_dict.html:33
#: application/templates/adv_dict.html:55
#: application/templates/adv_dict.html:72
#: application/templates/word_lookup.html:65
msgid "Database"
msgstr "데이터베이스"

#: application/templates/adv_dict.html:63
msgid "Other languages"
msgstr "기타 언어"

#: application/templates/adv_dict.html:81
#: application/templates/word_lookup.html:3
#: application/templates/word_lookup.html:79
msgid "Word lookup"
msgstr "단어 조회"

#: application/templates/adv_dict.html:86 application/view/reader.py:29
#: application/view/reader.py:86
msgid "Online reading feature has not been activated yet."
msgstr "온라인 읽기 기능이 아직 활성화되지 않았습니다."

#: application/templates/adv_import.html:3
#: application/templates/adv_import.html:19
msgid "Import"
msgstr "가져오기"

#: application/templates/adv_import.html:9
msgid "Import custom rss from an OPML file."
msgstr "OPML 파일에서 사용자 정의 RSS 가져오기."

#: application/templates/adv_import.html:15
msgid "Import as fulltext rss by default"
msgstr "기본적으로 전체 텍스트 RSS로 가져오기"

#: application/templates/adv_import.html:20
msgid "Download"
msgstr "다운로드"

#: application/templates/adv_inboundmail.html:11
msgid ""
"To enable the inbound email feature, you also need to configure the "
"whitelist."
msgstr "수신 이메일 기능을 활성화하려면 화이트리스트도 구성해야 합니다."

#: application/templates/adv_inboundmail.html:16
#: application/templates/adv_uploadcover.html:15
#: application/templates/book_summarizer.html:19
#: application/templates/book_translator.html:22
#: application/templates/settings.html:143
msgid "Disable"
msgstr "비활성화"

#: application/templates/adv_inboundmail.html:17
msgid "Forward Only"
msgstr "오직 전달"

#: application/templates/adv_inboundmail.html:18
msgid "Save Only"
msgstr "오직 저장"

#: application/templates/adv_inboundmail.html:19
msgid "Save and Forward"
msgstr "저장 후 전달"

#: application/templates/adv_inboundmail.html:23
msgid "Email Retention"
msgstr "이메일 보관"

#: application/templates/adv_inboundmail.html:25
#: application/templates/settings.html:52
#: application/templates/settings.html:175
msgid "1 Day"
msgstr "1일"

#: application/templates/adv_inboundmail.html:27
#: application/templates/settings.html:59
msgid "30 Days"
msgstr "30일"

#: application/templates/adv_inboundmail.html:28
#: application/templates/settings.html:51
#: application/templates/settings.html:174
msgid "No limit"
msgstr "제한 없음"

#: application/templates/adv_inboundmail.html:36
msgid "Open webmail"
msgstr "웹메일 열기"

#: application/templates/adv_inboundmail.html:44
msgid "White List"
msgstr "화이트리스트"

#: application/templates/adv_inboundmail.html:46
#, python-format
msgid ""
"Emails sent to %(name)sxxx@%(mailHost)s will be forwarded to your kindle "
"email."
msgstr "%(name)sxxx@%(mailHost)s로 보내진 이메일은 귀하의 킨들 이메일로 전달됩니다."

#: application/templates/adv_inboundmail.html:47
msgid "Example"
msgstr "예시"

#: application/templates/adv_inboundmail.html:59
msgid "Please input mail address"
msgstr "이메일 주소를 입력하세요"

#: application/templates/adv_proxy.html:13
msgid "Supports"
msgstr "지원"

#: application/templates/adv_proxy.html:24
#: application/templates/adv_proxy.html:29
#: application/templates/book_audiolator.html:132
#: application/templates/book_summarizer.html:105
#: application/templates/book_translator.html:97
msgid "Test"
msgstr "테스트"

#: application/templates/adv_uploadcover.html:3
msgid "Cover image"
msgstr "표지 이미지"

#: application/templates/adv_uploadcover.html:9
msgid "Upload cover image"
msgstr "표지 이미지 업로드"

#: application/templates/adv_uploadcover.html:10
msgid ""
"Upload cover images from local with an aspect ratio of approximately 0.625."
msgstr "약 0.625의 가로 세로 비율을 가진 로컬에서 표지 이미지 업로드."

#: application/templates/adv_uploadcover.html:13
msgid "Include cover"
msgstr "표지 포함"

#: application/templates/adv_uploadcover.html:16
#: application/templates/book_summarizer.html:18
#: application/templates/book_translator.html:21
msgid "Enable"
msgstr "활성화"

#: application/templates/adv_uploadcover.html:20
msgid "Rule for cover"
msgstr "표지 규칙"

#: application/templates/adv_uploadcover.html:22
msgid "Random"
msgstr "무작위"

#: application/templates/adv_uploadcover.html:23
#: application/templates/base.html:127
msgid "Weekday"
msgstr "평일"

#: application/templates/adv_uploadcover.html:49
msgid "Upload/Update"
msgstr "업로드/업데이트"

#: application/templates/adv_uploadcss.html:22
msgid "Upload stylesheet"
msgstr "스타일 시트 업로드"

#: application/templates/adv_uploadcss.html:23
msgid "Upload a stylesheet from local (accept utf-8 only)."
msgstr "로컬에서 스타일 시트 업로드 (UTF-8만 허용)."

#: application/templates/adv_uploadcss.html:30
msgid "Upload"
msgstr "업로드"

#: application/templates/autoback.html:3
msgid "Auto back"
msgstr "자동 돌아가기"

#: application/templates/autoback.html:28
msgid "Auto back to previous page after 5 seconds"
msgstr "5초 후 이전 페이지로 자동 돌아가기"

#: application/templates/autoback.html:29
#: application/templates/tipsback.html:15
msgid "Click to back"
msgstr "뒤로 가기 클릭"

#: application/templates/base.html:24 application/templates/reader.html:189
msgid "Confirm Deletion"
msgstr "삭제 확인"

#: application/templates/base.html:26
msgid "Delete (Ctrl for no confirm)"
msgstr "삭제 (Ctrl로 확인 없이 삭제)"

#: application/templates/base.html:27
msgid "View Source Code"
msgstr "소스 코드 보기"

#: application/templates/base.html:28
msgid "Subscribe (Deliver Separately)"
msgstr "구독 (별도로 전송)"

#: application/templates/base.html:29
msgid "Subscribe"
msgstr "구독"

#: application/templates/base.html:30
msgid "Cannot add this custom rss, Error:"
msgstr "이 사용자 정의 RSS를 추가할 수 없습니다, 오류:"

#: application/templates/base.html:31
msgid "Cannot delete this feed, Error:"
msgstr "이 피드를 삭제할 수 없습니다, 오류:"

#: application/templates/base.html:32
msgid "Fulltext"
msgstr "전체 텍스트"

#: application/templates/base.html:33 application/templates/base.html:43
msgid "Share"
msgstr "공유"

#: application/templates/base.html:34 application/templates/reader.html:190
msgid "Are you sure to delete?"
msgstr "정말 삭제하시겠습니까?"

#: application/templates/base.html:35
msgid "Report to the server that this feed is invalid."
msgstr "이 피드가 유효하지 않다고 서버에 보고하십시오."

#: application/templates/base.html:36
msgid "Are you sure to REMOVE ALL CUSTOM RSS?"
msgstr "모든 사용자 정의 RSS를 제거하시겠습니까?"

#: application/templates/base.html:37
msgid "Share links, share happiness"
msgstr "링크를 공유하고, 행복을 나누세요"

#: application/templates/base.html:38
msgid "Category"
msgstr "카테고리"

#: application/templates/base.html:39
#: application/templates/book_audiolator.html:58
#: application/templates/book_summarizer.html:43
#: application/templates/settings.html:159
msgid "Language"
msgstr "언어"

#: application/templates/base.html:40
msgid ""
"Please write a category in text field if the one you wish is not in the "
"list."
msgstr "원하는 카테고리가 목록에 없으면 텍스트 필드에 작성하세요."

#: application/templates/base.html:41
msgid "Ok"
msgstr "확인"

#: application/templates/base.html:42
msgid "Cancel"
msgstr "취소"

#: application/templates/base.html:44
msgid "Language code invalid"
msgstr "언어 코드가 유효하지 않습니다."

#: application/templates/base.html:45
msgid "Thank you for sharing."
msgstr "공유해 주셔서 감사합니다."

#: application/templates/base.html:46 application/templates/reader.html:159
msgid "Close"
msgstr "닫기"

#: application/templates/base.html:47
msgid "Unsubscribe"
msgstr "구독 취소"

#: application/templates/base.html:48
msgid "Cannot subscribe this recipe, Error:"
msgstr "이 레시피를 구독할 수 없습니다, 오류:"

#: application/templates/base.html:49
msgid "Are you sure to Unsubscribe ({0})?"
msgstr "정말 구독을 취소하시겠습니까? ({0})"

#: application/templates/base.html:50
msgid "Cannot unsubscribe this recipe, Error:"
msgstr "이 레시피를 구독 취소할 수 없습니다, 오류:"

#: application/templates/base.html:51
msgid "The recipe is already subscribed."
msgstr "이 레시피는 이미 구독되어 있습니다."

#: application/templates/base.html:52
msgid "Website login lnformation"
msgstr "웹사이트 로그인 정보"

#: application/templates/base.html:55
msgid "Submit"
msgstr "제출"

#: application/templates/base.html:56
msgid ""
"If any field is left blank, the server will clear the saved login "
"information."
msgstr "어떤 필드가 비어 있으면 서버가 저장된 로그인 정보를 삭제합니다."

#: application/templates/base.html:57
msgid "Cannot set the website login information, Error:"
msgstr "웹사이트 로그인 정보를 설정할 수 없습니다, 오류:"

#: application/templates/base.html:58 application/templates/my.html:58
msgid "Upload custom recipe"
msgstr "사용자 정의 레시피 업로드"

#: application/templates/base.html:59
msgid "Congratulations"
msgstr "축하합니다"

#: application/templates/base.html:60
msgid "Thanks"
msgstr "감사합니다"

#: application/templates/base.html:61
msgid ""
"Your recipe has been uploaded, and it can be found in the Library section. "
"If you dont see it, please make sure to switch to the correct language."
msgstr ""
"귀하의 레시피가 업로드되었습니다. Library 섹션에서 찾을 수 있습니다. 보이지 않으면 올바른 언어로 전환되었는지 확인하십시오."

#: application/templates/base.html:62
msgid "Your recipe have been deleted."
msgstr "귀하의 레시피가 삭제되었습니다."

#: application/templates/base.html:63
msgid "Kindleify Selection"
msgstr "Kindleify 선택"

#: application/templates/base.html:65
msgid "Verified"
msgstr "확인됨"

#: application/templates/base.html:66 application/view/login.py:79
#: application/view/share.py:157
msgid "The username does not exist or password is wrong."
msgstr "사용자가 존재하지 않거나 비밀번호가 잘못되었습니다."

#: application/templates/base.html:67
msgid "The file you chosen is not an acceptable type."
msgstr "선택한 파일은 허용되지 않는 유형입니다."

#: application/templates/base.html:68
msgid "The file have been uploaded successfully."
msgstr "파일이 성공적으로 업로드되었습니다."

#: application/templates/base.html:69 application/templates/library.html:67
msgid "This feed has been successfully subscribed."
msgstr "이 피드를 성공적으로 구독했습니다."

#: application/templates/base.html:70
msgid "Thank you for your feedback, this feed will be reviewed soon."
msgstr "피드백 감사합니다. 이 피드는 곧 검토될 것입니다."

#: application/templates/base.html:71
msgid "Are you confirming to share the recipe ({0})?"
msgstr "레시피({0})를 공유하시겠습니까?"

#: application/templates/base.html:72
msgid "[All]"
msgstr "[전체]"

#: application/templates/base.html:73
msgid "[By Time]"
msgstr "[시간별]"

#: application/templates/base.html:74
msgid "[Random]"
msgstr "[무작위]"

#: application/templates/base.html:75
msgid "[Uncategoried]"
msgstr "[카테고리 없음]"

#: application/templates/base.html:76
msgid "There are no links found."
msgstr "링크가 없습니다."

#: application/templates/base.html:77
msgid "Invalid report"
msgstr "유효하지 않은 보고서"

#: application/templates/base.html:78
msgid "Are you confirming that this link is invalid or off the cloud?"
msgstr "이 링크가 유효하지 않거나 클라우드에서 벗어났다고 확인하시겠습니까?"

#: application/templates/base.html:79
msgid "Customize delivery time"
msgstr "배송 시간 사용자 정의"

#: application/templates/base.html:80 application/templates/settings.html:72
msgid "Delivery days"
msgstr "배송일"

#: application/templates/base.html:81 application/templates/settings.html:74
msgid "Mon"
msgstr "월"

#: application/templates/base.html:82 application/templates/settings.html:76
msgid "Tue"
msgstr "화"

#: application/templates/base.html:83 application/templates/settings.html:78
msgid "Wed"
msgstr "수"

#: application/templates/base.html:84 application/templates/settings.html:80
msgid "Thu"
msgstr "목"

#: application/templates/base.html:85 application/templates/settings.html:82
msgid "Fri"
msgstr "금"

#: application/templates/base.html:86 application/templates/settings.html:84
msgid "Sat"
msgstr "토"

#: application/templates/base.html:87 application/templates/settings.html:86
msgid "Sun"
msgstr "일"

#: application/templates/base.html:88
msgid "Delivery times"
msgstr "배송 시간"

#: application/templates/base.html:89
msgid ""
"The customized delivery time for the recipe has been successfully saved."
msgstr "레시피의 사용자 정의 배송 시간이 성공적으로 저장되었습니다."

#: application/templates/base.html:90
msgid "The account have been deleted."
msgstr "계정이 삭제되었습니다."

#: application/templates/base.html:91 application/view/share.py:147
msgid "The username or password is empty."
msgstr "사용자 이름 또는 비밀번호가 비어 있습니다."

#: application/templates/base.html:92 application/view/admin.py:81
#: application/view/admin.py:165 application/view/admin.py:191
#: application/view/login.py:220 application/view/login.py:273
msgid "The two new passwords are dismatch."
msgstr "두 개의 새 비밀번호가 일치하지 않습니다."

#: application/templates/base.html:93
msgid "Password changed successfully."
msgstr "비밀번호가 성공적으로 변경되었습니다."

#: application/templates/base.html:94
msgid "Account added successfully."
msgstr "계정이 성공적으로 추가되었습니다."

#: application/templates/base.html:95 application/view/login.py:128
msgid "login required"
msgstr "로그인 필요"

#: application/templates/base.html:96
msgid "Upload cover files successfully."
msgstr "커버 파일이 성공적으로 업로드되었습니다."

#: application/templates/base.html:97
msgid ""
"Total size of the files you selected exceeds 16MB. Please reduce the image "
"resolution or upload in batches."
msgstr "선택한 파일의 총 크기가 16MB를 초과합니다. 이미지 해상도를 줄이거나 배치로 업로드하십시오."

#: application/templates/base.html:98
#: application/templates/book_translator.html:3
#: application/templates/book_translator.html:17
msgid "Bilingual Translator"
msgstr "이중 언어 번역기"

#: application/templates/base.html:99
#: application/templates/book_summarizer.html:3
#: application/templates/book_summarizer.html:14
msgid "AI Summarizer"
msgstr "AI 요약기"

#: application/templates/base.html:100
msgid "Upl"
msgstr "Upl"

#: application/templates/base.html:102
msgid "Log"
msgstr "Log"

#: application/templates/base.html:103
msgid "Emb"
msgstr "Emb"

#: application/templates/base.html:104
msgid "Tr"
msgstr "Tr"

#: application/templates/base.html:105
msgid "Tts"
msgstr "Tts"

#: application/templates/base.html:106
msgid "Ai"
msgstr "Ai"

#: application/templates/base.html:107
msgid ""
"The test email has been successfully sent to the following addresses. Please"
" check your inbox or spam folder to confirm its delivery. Depending on your "
"email server, there may be a slight delay."
msgstr ""
"테스트 이메일이 다음 주소로 성공적으로 전송되었습니다. 받은 편지함 또는 스팸 폴더를 확인하여 전송 여부를 확인하십시오. 이메일 서버에 "
"따라 약간의 지연이 있을 수 있습니다."

#: application/templates/base.html:108
msgid "Processing..."
msgstr "처리 중..."

#: application/templates/base.html:109
msgid "The configuration validation is correct."
msgstr "구성 검증이 정확합니다."

#: application/templates/base.html:110 application/templates/logs.html:23
#: application/templates/logs.html:72 application/templates/my.html:17
#: application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/settings.html:155
msgid "Title"
msgstr "제목"

#: application/templates/base.html:111
#: application/templates/book_audiolator.html:3
#: application/templates/book_audiolator.html:20
msgid "Text to Speech"
msgstr "텍스트 음성 변환"

#: application/templates/base.html:112
msgid "Action"
msgstr "작업"

#: application/templates/base.html:113
msgid "File"
msgstr "파일"

#: application/templates/base.html:114
msgid "Upload Only"
msgstr "업로드만"

#: application/templates/base.html:115
msgid "Send"
msgstr "보내기"

#: application/templates/base.html:116 application/templates/logs.html:54
msgid "There is nothing here."
msgstr "여기에 아무것도 없습니다."

#: application/templates/base.html:117
msgid "Please select a single item."
msgstr "하나의 항목을 선택하십시오."

#: application/templates/base.html:118 application/templates/reader.html:191
msgid "Please select at least one item."
msgstr "최소 하나의 항목을 선택하십시오."

#: application/templates/base.html:119 application/view/admin.py:77
#: application/view/admin.py:152 application/view/admin.py:189
#: application/view/adv.py:459 application/view/extension.py:34
#: application/view/extension.py:63 application/view/inbound_email.py:470
#: application/view/inbound_email.py:478 application/view/inbound_email.py:493
#: application/view/inbound_email.py:514 application/view/login.py:216
#: application/view/login.py:245 application/view/reader.py:109
#: application/view/reader.py:126 application/view/share.py:37
msgid "Some parameters are missing or wrong."
msgstr "일부 매개변수가 누락되었거나 잘못되었습니다."

#: application/templates/base.html:120
msgid "The email has been sent."
msgstr "이메일이 전송되었습니다."

#: application/templates/base.html:121 application/templates/webmail.html:29
msgid "From"
msgstr "보낸 사람"

#: application/templates/base.html:122 application/templates/logs.html:25
#: application/templates/logs.html:74 application/templates/webmail.html:30
msgid "To"
msgstr "받는 사람"

#: application/templates/base.html:123 application/templates/webmail.html:31
msgid "Subject"
msgstr "제목"

#: application/templates/base.html:124 application/templates/logs.html:22
#: application/templates/logs.html:71 application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/webmail.html:32
msgid "Time"
msgstr "시간"

#: application/templates/base.html:125 application/templates/logs.html:24
#: application/templates/logs.html:73 application/templates/webmail.html:33
msgid "Size"
msgstr "크기"

#: application/templates/base.html:126
msgid "Date type"
msgstr "날짜 유형"

#: application/templates/base.html:128
msgid "Date"
msgstr "날짜"

#: application/templates/base.html:129
msgid "This setting is prioritized."
msgstr "이 설정은 우선시됩니다."

#: application/templates/base.html:130
msgid "Combine multiple values with commas."
msgstr "여러 값을 쉼표로 구분하십시오."

#: application/templates/base.html:131
msgid "Put dictionary in dict folder"
msgstr "사전을 dict 폴더에 넣으십시오"

#: application/templates/base.html:156 application/templates/home.html:16
msgid "Logout"
msgstr "로그아웃"

#: application/templates/base.html:158 application/templates/home.html:21
#: application/templates/login.html:3 application/templates/login.html:22
#: application/templates/login.html:33
msgid "Login"
msgstr "로그인"

#: application/templates/base.html:160 application/templates/signup.html:3
#: application/templates/signup.html:19 application/templates/signup.html:43
msgid "Signup"
msgstr "회원가입"

#: application/templates/base.html:189 application/templates/home.html:15
#: application/templates/my.html:3
msgid "Feeds"
msgstr "피드"

#: application/templates/base.html:190 application/templates/settings.html:3
msgid "Settings"
msgstr "설정"

#: application/templates/base.html:191 application/templates/logs.html:3
msgid "Logs"
msgstr "로그"

#: application/templates/base.html:193
msgid "Advanced"
msgstr "고급"

#: application/templates/base.html:194 application/templates/library.html:3
msgid "Shared"
msgstr "공유됨"

#: application/templates/base.html:195 application/templates/reader.html:6
msgid "Reader"
msgstr "리더"

#: application/templates/book_audiolator.html:22
#: application/templates/book_summarizer.html:16
#: application/templates/book_translator.html:19
msgid "State"
msgstr "상태"

#: application/templates/book_audiolator.html:24
msgid "Send Ebook and Audio"
msgstr "전자책 및 오디오 보내기"

#: application/templates/book_audiolator.html:25
msgid "Send Audio only"
msgstr "오디오만 보내기"

#: application/templates/book_audiolator.html:26
msgid "Disable TTS"
msgstr "TTS 비활성화"

#: application/templates/book_audiolator.html:30
msgid "Send Audio To"
msgstr "오디오 보내기 대상"

#: application/templates/book_audiolator.html:31
msgid "Empty to use Kindle_email"
msgstr "Kindle_email을 사용하려면 비워 두세요"

#: application/templates/book_audiolator.html:35
msgid "TTS Engine"
msgstr "TTS 엔진"

#: application/templates/book_audiolator.html:41
#: application/templates/book_summarizer.html:35
#: application/templates/book_translator.html:32
msgid "Api Host"
msgstr "API 호스트"

#: application/templates/book_audiolator.html:42
#: application/templates/book_summarizer.html:36
#: application/templates/book_summarizer.html:75
msgid "Leave empty to use default"
msgstr "기본값을 사용하려면 비워 두세요"

#: application/templates/book_audiolator.html:46
msgid "Region"
msgstr "지역"

#: application/templates/book_audiolator.html:53
#: application/templates/book_summarizer.html:39
#: application/templates/book_translator.html:36
msgid "Api Key"
msgstr "Api Key"

#: application/templates/book_audiolator.html:66
msgid "Voice name"
msgstr "음성 이름"

#: application/templates/book_audiolator.html:73
msgid "Voice speed"
msgstr "음성 속도"

#: application/templates/book_audiolator.html:75
msgid "Extra slow"
msgstr "매우 느림"

#: application/templates/book_audiolator.html:76
msgid "Slow"
msgstr "느림"

#: application/templates/book_audiolator.html:77
#: application/templates/book_audiolator.html:87
#: application/templates/book_audiolator.html:97
msgid "Medium"
msgstr "보통"

#: application/templates/book_audiolator.html:78
msgid "Fast"
msgstr "빠름"

#: application/templates/book_audiolator.html:79
msgid "Extra fast"
msgstr "매우 빠름"

#: application/templates/book_audiolator.html:83
msgid "Voice pitch"
msgstr "음성 높낮이"

#: application/templates/book_audiolator.html:85
msgid "Extra low"
msgstr "매우 낮음"

#: application/templates/book_audiolator.html:86
msgid "Low"
msgstr "낮음"

#: application/templates/book_audiolator.html:88
msgid "High"
msgstr "높음"

#: application/templates/book_audiolator.html:89
msgid "Extra high"
msgstr "매우 높음"

#: application/templates/book_audiolator.html:93
msgid "Voice volume"
msgstr "음성 볼륨"

#: application/templates/book_audiolator.html:95
msgid "Extra soft"
msgstr "매우 작음"

#: application/templates/book_audiolator.html:96
msgid "Soft"
msgstr "작음"

#: application/templates/book_audiolator.html:98
msgid "Loud"
msgstr "큰 소리"

#: application/templates/book_audiolator.html:99
msgid "Extra loud"
msgstr "매우 큰 소리"

#: application/templates/book_audiolator.html:105
#: application/templates/book_summarizer.html:80
#: application/templates/book_translator.html:72
msgid "Apply to all subscribed recipes"
msgstr "구독한 모든 레시피에 적용"

#: application/templates/book_audiolator.html:110
#: application/templates/book_summarizer.html:85
#: application/templates/book_translator.html:77
msgid ""
"Note: Enabling this feature will significantly increase consumed CPU "
"instance hours."
msgstr "참고: 이 기능을 활성화하면 CPU 인스턴스 시간 소모가 크게 증가합니다."

#: application/templates/book_audiolator.html:119
#: application/templates/book_summarizer.html:94
#: application/templates/book_translator.html:86
msgid "Test (Please save settings firstly)"
msgstr "테스트(먼저 설정을 저장해주세요)"

#: application/templates/book_audiolator.html:121
#: application/templates/book_summarizer.html:96
#: application/templates/book_translator.html:88
msgid "Text"
msgstr "텍스트"

#: application/templates/book_audiolator.html:127
msgid "Your browser does not support the audio element."
msgstr "귀하의 브라우저는 오디오 요소를 지원하지 않습니다."

#: application/templates/book_summarizer.html:29
msgid "Model"
msgstr "Model"

#: application/templates/book_summarizer.html:45
msgid "Auto"
msgstr "자동"

#: application/templates/book_summarizer.html:56
msgid "Summary words"
msgstr "요약 단어"

#: application/templates/book_summarizer.html:70
msgid "Summary style"
msgstr "요약 스타일"

#: application/templates/book_summarizer.html:74
msgid "Placeholders available:"
msgstr "사용 가능한 자리 표시자:"

#: application/templates/book_summarizer.html:74
msgid "Custom prompt"
msgstr "사용자 정의 프롬프트"

#: application/templates/book_summarizer.html:100
msgid "Summary"
msgstr "요약"

#: application/templates/book_translator.html:37
msgid "One key per line"
msgstr "한 줄에 한 키"

#: application/templates/book_translator.html:40
#: application/templates/word_lookup.html:51
msgid "Source language"
msgstr "원본 언어"

#: application/templates/book_translator.html:46
msgid "Target language"
msgstr "대상 언어"

#: application/templates/book_translator.html:52
msgid "Translation Position"
msgstr "번역 위치"

#: application/templates/book_translator.html:54
msgid "Below original"
msgstr "원본 아래"

#: application/templates/book_translator.html:55
msgid "Above original"
msgstr "원본 위"

#: application/templates/book_translator.html:56
msgid "Left to original"
msgstr "원본 왼쪽"

#: application/templates/book_translator.html:57
msgid "Right to original"
msgstr "원본 오른쪽"

#: application/templates/book_translator.html:58
msgid "Translated text only"
msgstr "번역된 텍스트만"

#: application/templates/book_translator.html:62
msgid "Original text style"
msgstr "원본 텍스트 스타일"

#: application/templates/book_translator.html:66
msgid "Translated text style"
msgstr "번역된 텍스트 스타일"

#: application/templates/book_translator.html:92
msgid "Translation"
msgstr "번역"

#: application/templates/change_password.html:15
msgid "Old password"
msgstr "기존 비밀번호"

#: application/templates/change_password.html:19
#: application/templates/reset_password.html:31
#: application/templates/reset_password.html:32
msgid "New password"
msgstr "새 비밀번호"

#: application/templates/change_password.html:23
#: application/templates/reset_password.html:35
#: application/templates/reset_password.html:36
#: application/templates/signup.html:29
#: application/templates/user_account.html:23
msgid "Confirm password"
msgstr "비밀번호 확인"

#: application/templates/change_password.html:31
msgid "Share key"
msgstr "공유 키"

#: application/templates/change_password.html:37
msgid "Confirm Change"
msgstr "변경 확인"

#: application/templates/debug_cmd.html:3
msgid "Debug cmd"
msgstr "디버그 명령"

#: application/templates/home.html:3
msgid "Home"
msgstr "홈"

#: application/templates/home.html:10 application/templates/login.html:18
msgid "You are in DEMO mode. Logging out will delete all data."
msgstr "데모 모드에 있습니다. 로그아웃하면 모든 데이터가 삭제됩니다."

#: application/templates/home.html:12
msgid "Sharing Joyful News Every Step of the Way"
msgstr "매 순간 기쁜 소식을 공유합니다"

#: application/templates/home.html:31
msgid "Inherited From Calibre"
msgstr "Calibre에서 상속됨"

#: application/templates/home.html:34
#, python-format
msgid ""
"Empowered by %(calibre)s, you can easily create e-books on a Python-"
"supported online platform and seamlessly transfer them to your e-reader or "
"other reading devices."
msgstr ""
"%(calibre)s의 지원으로, Python 지원 온라인 플랫폼에서 전자책을 쉽게 만들고 전자책 리더기나 다른 읽기 장치로 원활하게 "
"전송할 수 있습니다."

#: application/templates/home.html:42
msgid "Share Your Ideas"
msgstr "아이디어를 공유하세요"

#: application/templates/home.html:45
#, python-format
msgid ""
"With the open-source %(kindleear)s application, you can set up your own "
"server to deliver daily news feeds to your e-reader and effortlessly share "
"the service with friends."
msgstr ""
"오픈 소스 %(kindleear)s 애플리케이션을 사용하여, 자신의 서버를 설정하고 매일 뉴스 피드를 전자책 리더기로 전송하며 서비스를 "
"친구들과 손쉽게 공유할 수 있습니다."

#: application/templates/library.html:50 application/templates/my.html:61
msgid "Search"
msgstr "검색"

#: application/templates/login.html:38 application/view/login.py:197
#: application/view/login.py:204
msgid ""
"The website does not allow registration. You can ask the owner for an "
"account."
msgstr "이 웹사이트는 등록을 허용하지 않습니다. 계정을 원하시면 소유자에게 문의하세요."

#: application/templates/logs.html:11
msgid "Only display last 20 logs"
msgstr "최근 20개의 로그만 표시"

#: application/templates/logs.html:26 application/templates/logs.html:75
msgid "Status"
msgstr "상태"

#: application/templates/logs.html:58
msgid "Logs of other users"
msgstr "다른 사용자의 로그"

#: application/templates/my.html:12 application/templates/settings.html:153
msgid "Custom RSS"
msgstr "사용자 정의 RSS"

#: application/templates/my.html:23
msgid "Content Embedded"
msgstr "내장된 콘텐츠"

#: application/templates/my.html:27
msgid "Deliver Separately"
msgstr "별도로 전송"

#: application/templates/my.html:46
msgid "Subscribed"
msgstr "구독됨"

#: application/templates/my.html:51
msgid "Library"
msgstr "라이브러리"

#: application/templates/my.html:51
msgid "get more recipes"
msgstr "레시피 더 받기"

#: application/templates/my.html:68
msgid "Subscription to selected recipe successful."
msgstr "선택한 레시피 구독이 성공적으로 완료되었습니다."

#: application/templates/my.html:71
msgid "Bookmarklet"
msgstr "북마클릿"

#: application/templates/my.html:75
msgid "Send to Kindle"
msgstr "Kindle로 보내기"

#: application/templates/my.html:79
msgid "Subscribe with KindleEar"
msgstr "KindleEar로 구독"

#: application/templates/my.html:82
msgid "Drag and drop this link to your bookmarks"
msgstr "이 링크를 북마크로 드래그 앤 드롭하세요"

#: application/templates/my.html:86
msgid "Chrome extension"
msgstr "Chrome 확장 프로그램"

#: application/templates/my.html:87
msgid "Edge extension"
msgstr "Edge 확장 프로그램"

#: application/templates/my.html:89
msgid "Browser extensions also available"
msgstr "브라우저 확장 프로그램도 사용 가능"

#: application/templates/reader.html:57
msgid "Push current book"
msgstr "현재 책 푸시"

#: application/templates/reader.html:63
msgid "Push current article"
msgstr "현재 기사 푸시"

#: application/templates/reader.html:71
msgid "Delete selected books"
msgstr "선택한 책 삭제"

#: application/templates/reader.html:77
msgid "Allow click links"
msgstr "링크 클릭 허용"

#: application/templates/reader.html:83
msgid "Top-left dict mode"
msgstr "왼쪽 상단 사전 모드"

#: application/templates/reader.html:89
msgid "Dark mode"
msgstr "다크 모드"

#: application/templates/reader.html:95
msgid "eInk mode"
msgstr "eInk 모드"

#: application/templates/reader.html:101
msgid "Increase font size"
msgstr "글꼴 크기 증가"

#: application/templates/reader.html:107
msgid "Decrease font size"
msgstr "글꼴 크기 감소"

#: application/templates/reader.html:113
msgid "Visualize Touch Regions"
msgstr "터치 영역 시각화"

#: application/templates/reader.html:119
msgid "Help"
msgstr "도움말"

#: application/templates/reader.html:125
#: application/templates/reader_404.html:135
msgid "Menu"
msgstr "메뉴"

#: application/templates/reader.html:139
msgid "Collapse all"
msgstr "모두 접기"

#: application/templates/reader.html:144
msgid "Expand all"
msgstr "모두 펼치기"

#: application/templates/reader.html:149
#: application/templates/reader_404.html:140
msgid "Prev"
msgstr "이전"

#: application/templates/reader.html:154
#: application/templates/reader_404.html:143
msgid "Next page"
msgstr "다음 페이지"

#: application/templates/reader.html:192
msgid "Pushed successfully."
msgstr "푸시 성공"

#: application/templates/reader.html:193
msgid "There are currently no books or articles being read."
msgstr "현재 읽고 있는 책이나 기사가 없습니다."

#: application/templates/reset_password.html:3
#: application/templates/reset_password.html:41
msgid "Reset password"
msgstr "비밀번호 재설정"

#: application/templates/settings.html:14
msgid ""
"Your account will pause after {0}, please log in again before it expires."
msgstr "{0} 후 계정이 일시 중지됩니다. 만료되기 전에 다시 로그인하세요."

#: application/templates/settings.html:23
msgid "Base"
msgstr "기본"

#: application/templates/settings.html:25
msgid "Auto delivery"
msgstr "자동 배달"

#: application/templates/settings.html:28
msgid "Recipes and custom RSS"
msgstr "레시피 및 맞춤 RSS"

#: application/templates/settings.html:29
msgid "Recipes only"
msgstr "레시피만"

#: application/templates/settings.html:30
msgid "Disable all"
msgstr "모두 비활성화"

#: application/templates/settings.html:34
msgid "Kindle E-mail"
msgstr "Kindle 이메일"

#: application/templates/settings.html:35
msgid "Seperated by comma"
msgstr "쉼표로 구분"

#: application/templates/settings.html:39
msgid "Delivery mode"
msgstr "배달 모드"

#: application/templates/settings.html:42
msgid "Email delivery & online reading"
msgstr "이메일 배달 및 온라인 읽기"

#: application/templates/settings.html:43
msgid "Email delivery"
msgstr "이메일 배달"

#: application/templates/settings.html:44
msgid "Online reading"
msgstr "온라인 읽기"

#: application/templates/settings.html:48
msgid "Retention days for online books"
msgstr "온라인 책 보관 일수"

#: application/templates/settings.html:48
msgid "Web shelf"
msgstr "웹 선반"

#: application/templates/settings.html:53
#: application/templates/settings.html:176
msgid "2 Days"
msgstr "2일"

#: application/templates/settings.html:54
#: application/templates/settings.html:177
msgid "3 Days"
msgstr "3일"

#: application/templates/settings.html:55
#: application/templates/settings.html:178
msgid "4 Days"
msgstr "4일"

#: application/templates/settings.html:56
#: application/templates/settings.html:179
msgid "5 Days"
msgstr "5일"

#: application/templates/settings.html:57
#: application/templates/settings.html:180
msgid "6 Days"
msgstr "6일"

#: application/templates/settings.html:64
msgid "Time zone"
msgstr "시간대"

#: application/templates/settings.html:89
msgid "Delivery time"
msgstr "배달 시간"

#: application/templates/settings.html:97
msgid "Book type"
msgstr "책 유형"

#: application/templates/settings.html:104
msgid "Determines final file size"
msgstr "최종 파일 크기 결정"

#: application/templates/settings.html:104
msgid "Device type"
msgstr "디바이스 유형"

#: application/templates/settings.html:113
msgid "Title format"
msgstr "제목 형식"

#: application/templates/settings.html:116
msgid "Title Only"
msgstr "제목만"

#: application/templates/settings.html:130
msgid "Remove hyperlinks"
msgstr "하이퍼링크 제거"

#: application/templates/settings.html:133
msgid "Do not remove hyperlinks"
msgstr "하이퍼링크 제거하지 않음"

#: application/templates/settings.html:134
msgid "Remove image links"
msgstr "이미지 링크 제거"

#: application/templates/settings.html:135
msgid "Remove text links"
msgstr "텍스트 링크 제거"

#: application/templates/settings.html:136
msgid "Remove all hyperlinks"
msgstr "모든 하이퍼링크 제거"

#: application/templates/settings.html:140
msgid "Navbar"
msgstr "네비게이션 바"

#: application/templates/settings.html:144
msgid "Top Center"
msgstr "상단 가운데"

#: application/templates/settings.html:145
msgid "Top Left"
msgstr "상단 왼쪽"

#: application/templates/settings.html:146
msgid "Bottom Center"
msgstr "하단 가운데"

#: application/templates/settings.html:147
msgid "Bottom Left"
msgstr "하단 왼쪽"

#: application/templates/settings.html:159
msgid "Sets the lookup dictionary"
msgstr "조회 사전 설정"

#: application/templates/settings.html:171
msgid "Oldest article"
msgstr "가장 오래된 기사"

#: application/templates/settings.html:185
msgid "Time format"
msgstr "시간 형식"

#: application/templates/settings.html:198
msgid "Author format"
msgstr "저자 형식"

#: application/templates/settings.html:215
msgid "Send Mail Service"
msgstr "메일 서비스 발송"

#: application/templates/settings.html:217
msgid "Service"
msgstr "서비스"

#: application/templates/settings.html:225
msgid "ApiKey"
msgstr "ApiKey"

#: application/templates/settings.html:229
msgid "SecretKey"
msgstr "SecretKey"

#: application/templates/settings.html:237
msgid "Port"
msgstr "포트"

#: application/templates/settings.html:249
msgid "Save path"
msgstr "저장 경로"

#: application/templates/settings.html:257
#, python-format
msgid ""
"Important: Please activate your kindle firstly, then goto %(personal)s Page "
"and add %(sender)s to 'Approved Personal Document E-mail List'."
msgstr ""
"중요: 먼저 Kindle을 활성화한 후 %(personal)s 페이지로 가서 %(sender)s를 '승인된 개인 문서 이메일 목록'에 "
"추가하십시오."

#: application/templates/settings.html:257
msgid "Personal Document Settings"
msgstr "개인 문서 설정"

#: application/templates/settings.html:263
#, python-format
msgid ""
"You have not yet set up your email address. Please go to the %(admin)s page "
"to add your email address firstly."
msgstr "아직 이메일 주소를 설정하지 않았습니다. 먼저 %(admin)s 페이지로 가서 이메일 주소를 추가하십시오."

#: application/templates/settings.html:271
msgid "Send Test Email"
msgstr "테스트 이메일 발송"

#: application/templates/signup.html:38
msgid "Invitation code"
msgstr "초대 코드"

#: application/templates/user_account.html:3
msgid "User account"
msgstr "사용자 계정"

#: application/templates/user_account.html:42
msgid "Never expire"
msgstr "만료되지 않음"

#: application/templates/webmail.html:3
msgid "Webmail"
msgstr "웹메일"

#: application/templates/webmail.html:17
msgid "Refresh"
msgstr "새로 고침"

#: application/templates/webmail.html:18
msgid "Read/Unread"
msgstr "읽음/읽지 않음"

#: application/templates/webmail.html:19
msgid "Reply"
msgstr "답장"

#: application/templates/webmail.html:20
msgid "Forward"
msgstr "전달"

#: application/templates/webmail.html:21
msgid "Fwd as Attach"
msgstr "첨부 파일로 전달"

#: application/templates/word_lookup.html:71
msgid "Word"
msgstr "단어"

#: application/view/admin.py:48 application/view/adv.py:437
#: application/view/adv.py:528 application/view/settings.py:67
#: application/view/translator.py:88 application/view/translator.py:172
#: application/view/translator.py:254
msgid "Settings Saved!"
msgstr "설정이 저장되었습니다!"

#: application/view/admin.py:57 application/view/admin.py:64
#: application/view/admin.py:91
msgid "Add account"
msgstr "계정 추가"

#: application/view/admin.py:63 application/view/admin.py:103
#: application/view/admin.py:135
msgid "You do not have sufficient privileges."
msgstr "권한이 충분하지 않습니다."

#: application/view/admin.py:79 application/view/login.py:44
#: application/view/login.py:222
msgid "The username includes unsafe chars."
msgstr "사용자 이름에 안전하지 않은 문자가 포함되어 있습니다."

#: application/view/admin.py:83 application/view/login.py:224
msgid "Already exist the username."
msgstr "이미 해당 사용자 이름이 존재합니다."

#: application/view/admin.py:88
msgid "The password includes non-ascii chars."
msgstr "비밀번호에 비ASCII 문자가 포함되어 있습니다."

#: application/view/admin.py:107 application/view/admin.py:132
#: application/view/admin.py:163 application/view/extension.py:32
#: application/view/extension.py:61
msgid "The username '{}' does not exist."
msgstr "사용자 이름 '{}'은(는) 존재하지 않습니다."

#: application/view/admin.py:123
msgid "The password will not be changed if the fields are empties."
msgstr "필드가 비어 있으면 비밀번호는 변경되지 않습니다."

#: application/view/admin.py:130 application/view/admin.py:184
msgid "Change"
msgstr "변경"

#: application/view/admin.py:181
msgid "Change success."
msgstr "변경 성공"

#: application/view/admin.py:194
msgid "The old password is wrong."
msgstr "기존 비밀번호가 잘못되었습니다."

#: application/view/admin.py:196
msgid "Changes saved successfully."
msgstr "변경 사항이 성공적으로 저장되었습니다."

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108 application/view/adv.py:109
#: application/view/adv.py:110 application/view/adv.py:111
#: application/view/adv.py:112 application/view/adv.py:113
msgid "Append hyperlink '{}' to article"
msgstr "기사에 하이퍼링크 '{}' 추가"

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108
msgid "Save to {}"
msgstr "{}에 저장"

#: application/view/adv.py:104
msgid "evernote"
msgstr "evernote"

#: application/view/adv.py:105
msgid "wiz"
msgstr "wiz"

#: application/view/adv.py:106
msgid "pocket"
msgstr "pocket"

#: application/view/adv.py:107
msgid "instapaper"
msgstr "instapaper"

#: application/view/adv.py:108
msgid "wallabag"
msgstr "wallabag"

#: application/view/adv.py:109 application/view/adv.py:110
#: application/view/adv.py:111 application/view/adv.py:112
msgid "Share on {}"
msgstr "{}에 공유"

#: application/view/adv.py:109
msgid "weibo"
msgstr "웨이보"

#: application/view/adv.py:110
msgid "facebook"
msgstr "facebook"

#: application/view/adv.py:112
msgid "tumblr"
msgstr "tumblr"

#: application/view/adv.py:113
msgid "Open in browser"
msgstr "브라우저에서 열기"

#: application/view/adv.py:114
msgid "Append qrcode of url to article"
msgstr "기사에 URL의 QR 코드 추가"

#: application/view/adv.py:381 application/view/share.py:54
#: application/view/subscribe.py:250
msgid "Unknown command: {}"
msgstr "알 수 없는 명령: {}"

#: application/view/adv.py:439 application/view/adv.py:530
msgid "The format is invalid."
msgstr "형식이 유효하지 않습니다."

#: application/view/adv.py:562
msgid "Authorization Error!<br/>{}"
msgstr "인증 오류!<br/>{}"

#: application/view/adv.py:583
msgid "Success authorized by Pocket!"
msgstr "Pocket에서 인증 성공!"

#: application/view/adv.py:589
msgid ""
"Failed to request authorization of Pocket!<hr/>See details "
"below:<br/><br/>{}"
msgstr "Pocket 인증 요청 실패!<hr/>자세한 내용은 아래를 참조하십시오:<br/><br/>{}"

#: application/view/adv.py:610
msgid "The Instapaper service encountered an error. Please try again later."
msgstr "Instapaper 서비스에서 오류가 발생했습니다. 나중에 다시 시도하십시오."

#: application/view/adv.py:623
msgid "Request type [{}] unsupported"
msgstr "요청 유형 [{}]을(를) 지원하지 않습니다."

#: application/view/deliver.py:82 application/view/login.py:169
#: application/view/share.py:41
msgid "The username does not exist or the email is empty."
msgstr "사용자 이름이 존재하지 않거나 이메일이 비어 있습니다."

#: application/view/deliver.py:109
msgid "The following recipes has been added to the push queue."
msgstr "다음 레시피가 푸시 대기열에 추가되었습니다."

#: application/view/deliver.py:112
msgid "There are no recipes to deliver."
msgstr "배달할 레시피가 없습니다."

#: application/view/extension.py:69
msgid "The rules parameter is invalid."
msgstr "규칙 매개변수가 유효하지 않습니다."

#: application/view/library.py:32
msgid "Cannot fetch data from {}, status: {}"
msgstr "{}, 상태: {}에서 데이터를 가져올 수 없습니다."

#: application/view/library.py:48 application/view/subscribe.py:238
#: application/view/subscribe.py:380 application/view/subscribe.py:409
#: application/view/subscribe.py:416 application/view/translator.py:30
msgid "The recipe does not exist."
msgstr "레시피가 존재하지 않습니다."

#: application/view/login.py:27 application/view/login.py:76
msgid "Please use {}/{} to login at first time."
msgstr "처음 로그인 시 {}/{}을(를) 사용하십시오."

#: application/view/login.py:40
msgid "Username is empty."
msgstr "사용자 이름이 비어 있습니다."

#: application/view/login.py:42
msgid "The len of username reached the limit of 25 chars."
msgstr "사용자 이름 길이가 25자를 초과했습니다."

#: application/view/login.py:80
msgid "Forgot password?"
msgstr "비밀번호를 잊으셨나요?"

#: application/view/login.py:148 application/view/login.py:275
msgid "The token is wrong or expired."
msgstr "토큰이 잘못되었거나 만료되었습니다."

#: application/view/login.py:151
msgid "Please input the correct username and email to reset password."
msgstr "비밀번호를 재설정하려면 올바른 사용자 이름과 이메일을 입력하십시오."

#: application/view/login.py:153
msgid "The email of account '{name}' is {email}."
msgstr "계정 '{name}'의 이메일은 {email}입니다."

#: application/view/login.py:174
msgid "Reset password success, Please close this page and login again."
msgstr "비밀번호 재설정 성공, 이 페이지를 닫고 다시 로그인하세요."

#: application/view/login.py:177
msgid "The email you input is not associated with this account."
msgstr "입력한 이메일은 이 계정과 연결되어 있지 않습니다."

#: application/view/login.py:186
msgid "The link to reset your password has been sent to your email."
msgstr "비밀번호 재설정 링크가 귀하의 이메일로 전송되었습니다."

#: application/view/login.py:187
msgid "Please check your email inbox within 24 hours."
msgstr "24시간 이내에 이메일 받은 편지함을 확인하십시오."

#: application/view/login.py:218
msgid "The invitation code is invalid."
msgstr "초대 코드가 유효하지 않습니다."

#: application/view/login.py:226
msgid ""
"Failed to create an account. Please contact the administrator for "
"assistance."
msgstr "계정 생성에 실패했습니다. 관리자에게 도움을 요청하세요."

#: application/view/login.py:236
msgid "Successfully created account."
msgstr "계정이 성공적으로 생성되었습니다."

#: application/view/login.py:247
msgid "Reset KindleEar password"
msgstr "KindleEar 비밀번호 재설정"

#: application/view/login.py:248
msgid "This is an automated email. Please do not reply to it."
msgstr "이 이메일은 자동 발송된 것입니다. 답장하지 마십시오."

#: application/view/login.py:249
msgid "You can click the following link to reset your KindleEar password."
msgstr "다음 링크를 클릭하여 KindleEar 비밀번호를 재설정할 수 있습니다."

#: application/view/reader.py:88
msgid "The article is missing?"
msgstr "글이 누락되었나요?"

#: application/view/reader.py:190 application/view/translator.py:121
#: application/view/translator.py:205 application/view/translator.py:287
msgid "The text is empty."
msgstr "텍스트가 비어 있습니다."

#: application/view/reader.py:239
msgid "No definitions found for '{}'."
msgstr "'{}'에 대한 정의가 없습니다."

#: application/view/reader.py:240
msgid "Did you mean?"
msgstr "이것을 의미하셨나요?"

#: application/view/reader.py:324 application/view/reader.py:331
msgid "Failed to push: {}"
msgstr "푸시 실패: {}"

#: application/view/reader.py:379
msgid "Failed to create ebook."
msgstr "전자책 생성 실패."

#: application/view/settings.py:131
msgid ""
"You have not yet set up your email address. Please go to the 'Account' page "
"to add your email address firstly."
msgstr "아직 이메일 주소를 설정하지 않았습니다. '계정' 페이지로 이동하여 이메일 주소를 먼저 추가하세요."

#: application/view/settings.py:215
msgid "English"
msgstr "영어"

#: application/view/settings.py:216
msgid "Simplified Chinese"
msgstr "简体中文"

#: application/view/settings.py:217
msgid "Traditional Chinese"
msgstr "繁體中文"

#: application/view/settings.py:218
msgid "French"
msgstr "프랑스어"

#: application/view/settings.py:219
msgid "Spanish"
msgstr "스페인어"

#: application/view/settings.py:220
msgid "Portuguese"
msgstr "포르투갈어"

#: application/view/settings.py:221
msgid "German"
msgstr "독일어"

#: application/view/settings.py:222
msgid "Italian"
msgstr "이탈리아어"

#: application/view/settings.py:223
msgid "Japanese"
msgstr "일본어"

#: application/view/settings.py:224
msgid "Russian"
msgstr "러시아어"

#: application/view/settings.py:225
msgid "Turkish"
msgstr "터키어"

#: application/view/settings.py:226
msgid "Korean"
msgstr "한국어"

#: application/view/settings.py:227
msgid "Arabic"
msgstr "아랍어"

#: application/view/settings.py:228
msgid "Czech"
msgstr "체코어"

#: application/view/settings.py:229
msgid "Dutch"
msgstr "네덜란드어"

#: application/view/settings.py:230
msgid "Greek"
msgstr "그리스어"

#: application/view/settings.py:231
msgid "Hindi"
msgstr "힌디어"

#: application/view/settings.py:232
msgid "Malaysian"
msgstr "말레이시아어"

#: application/view/settings.py:233
msgid "Bengali"
msgstr "벵골어"

#: application/view/settings.py:234
msgid "Persian"
msgstr "페르시아어"

#: application/view/settings.py:235
msgid "Urdu"
msgstr "우르두어"

#: application/view/settings.py:236
msgid "Swahili"
msgstr "스와힐리어"

#: application/view/settings.py:237
msgid "Vietnamese"
msgstr "베트남어"

#: application/view/settings.py:238
msgid "Punjabi"
msgstr "펀자비어"

#: application/view/settings.py:239
msgid "Javanese"
msgstr "자바어"

#: application/view/settings.py:240
msgid "Tagalog"
msgstr "타가로그어"

#: application/view/settings.py:241
msgid "Hausa"
msgstr "하우사어"

#: application/view/settings.py:242
msgid "Thai"
msgstr "태국어"

#: application/view/settings.py:243
msgid "Polish"
msgstr "폴란드어"

#: application/view/settings.py:244
msgid "Romanian"
msgstr "루마니아어"

#: application/view/settings.py:245
msgid "Hungarian"
msgstr "헝가리어"

#: application/view/settings.py:246
msgid "Swedish"
msgstr "스웨덴어"

#: application/view/settings.py:247
msgid "Hebrew"
msgstr "히브리어"

#: application/view/settings.py:248
msgid "Norwegian"
msgstr "노르웨이어"

#: application/view/settings.py:249
msgid "Finnish"
msgstr "핀란드어"

#: application/view/settings.py:250
msgid "Danish"
msgstr "덴마크어"

#: application/view/settings.py:251
msgid "Ukrainian"
msgstr "우크라이나어"

#: application/view/settings.py:252
msgid "Tamil"
msgstr "타밀어"

#: application/view/settings.py:253
msgid "Marathi"
msgstr "마라티어"

#: application/view/settings.py:254
msgid "Burmese"
msgstr "미얀마어"

#: application/view/settings.py:255
msgid "Amharic"
msgstr "암하라어"

#: application/view/settings.py:256
msgid "Azerbaijani"
msgstr "아제르바이잔어"

#: application/view/settings.py:257
msgid "Kazakh"
msgstr "카자흐어"

#: application/view/settings.py:258
msgid "Serbian"
msgstr "Српски"

#: application/view/settings.py:259
msgid "Croatian"
msgstr "Hrvatski"

#: application/view/settings.py:260
msgid "Slovak"
msgstr "Slovenčina"

#: application/view/settings.py:261
msgid "Bulgarian"
msgstr "Български"

#: application/view/settings.py:262
msgid "Icelandic"
msgstr "Íslenska"

#: application/view/settings.py:263
msgid "Lithuanian"
msgstr "Lietuvių"

#: application/view/settings.py:264
msgid "Latvian"
msgstr "Latviešu"

#: application/view/settings.py:265
msgid "Estonian"
msgstr "Eesti"

#: application/view/settings.py:266
msgid "Macedonian"
msgstr "Македонски"

#: application/view/settings.py:267
msgid "Albanian"
msgstr "Shqip"

#: application/view/settings.py:268
msgid "Galician"
msgstr "Galego"

#: application/view/settings.py:269
msgid "Welsh"
msgstr "Cymraeg"

#: application/view/settings.py:270
msgid "Basque"
msgstr "Euskara"

#: application/view/settings.py:271
msgid "Nepali"
msgstr "नेपाली"

#: application/view/share.py:60
msgid "There is no {} email yet."
msgstr "{} 이메일이 아직 없습니다."

#: application/view/share.py:108 application/view/share.py:133
#: application/view/share.py:155 application/view/share.py:177
msgid "Saved to your {} account."
msgstr "귀하의 {} 계정에 저장되었습니다."

#: application/view/share.py:111 application/view/share.py:129
#: application/view/share.py:158 application/view/share.py:179
msgid "Failed save to {}."
msgstr "{} 저장 실패."

#: application/view/share.py:112 application/view/share.py:130
#: application/view/share.py:159 application/view/share.py:180
msgid "Reason :"
msgstr "이유 :"

#: application/view/share.py:121
msgid "Unauthorized {} account!"
msgstr "허가되지 않은 {} 계정!"

#: application/view/share.py:134
msgid "See details below:"
msgstr "아래에서 세부 정보를 확인하세요:"

#: application/view/share.py:157
msgid "Unknown: {}"
msgstr "알 수 없음: {}"

#: application/view/subscribe.py:81 application/view/subscribe.py:167
msgid "Duplicated subscription!"
msgstr "중복된 구독입니다!"

#: application/view/subscribe.py:126
msgid "The Title or Url is empty."
msgstr "제목 또는 URL이 비어 있습니다."

#: application/view/subscribe.py:139
msgid "Failed to fetch the recipe."
msgstr "레시피를 가져오지 못했습니다."

#: application/view/subscribe.py:153 application/view/subscribe.py:331
msgid "Failed to save the recipe. Error:"
msgstr "레시피 저장 실패. 오류:"

#: application/view/subscribe.py:195
msgid "The Rss does not exist."
msgstr "Rss가 존재하지 않습니다."

#: application/view/subscribe.py:278
msgid "You can only delete the uploaded recipe."
msgstr "업로드된 레시피만 삭제할 수 있습니다."

#: application/view/subscribe.py:283
msgid "The recipe have been subscribed, please unsubscribe it before delete."
msgstr "레시피가 구독되었습니다. 삭제하기 전에 구독을 취소하세요."

#: application/view/subscribe.py:304 application/view/translator.py:51
#: application/view/translator.py:104 application/view/translator.py:117
#: application/view/translator.py:140 application/view/translator.py:188
#: application/view/translator.py:201 application/view/translator.py:230
#: application/view/translator.py:270 application/view/translator.py:283
msgid "This recipe has not been subscribed to yet."
msgstr "이 레시피는 아직 구독되지 않았습니다."

#: application/view/subscribe.py:318
msgid "Can not read uploaded file, Error:"
msgstr "업로드된 파일을 읽을 수 없습니다. 오류:"

#: application/view/subscribe.py:326
msgid ""
"Failed to decode the recipe. Please ensure that your recipe is saved in "
"utf-8 encoding."
msgstr "레시피를 디코딩하지 못했습니다. 레시피가 UTF-8 인코딩으로 저장되어 있는지 확인하세요."

#: application/view/subscribe.py:349
msgid "Cannot find any subclass of BasicNewsRecipe."
msgstr "BasicNewsRecipe의 하위 클래스를 찾을 수 없습니다."

#: application/view/subscribe.py:354
msgid "The recipe is already in the library."
msgstr "레시피는 이미 라이브러리에 있습니다."

#: application/view/subscribe.py:387
msgid "The login information for this recipe has been cleared."
msgstr "이 레시피의 로그인 정보가 삭제되었습니다."

#: application/view/subscribe.py:391
msgid "The login information for this recipe has been saved."
msgstr "이 레시피의 로그인 정보가 저장되었습니다."

#: application/view/translator.py:81 application/view/translator.py:165
msgid "The api key is required."
msgstr "API 키가 필요합니다."
