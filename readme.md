__English__ · [简体中文](readme_zh.md)

---

**[Project documentation](https://cdhigh.github.io/KindleEar)**     



[Demo site](https://kindleear.koyeb.app/)  
Note: The demo site is a free service hosted on Koyeb. The login username and password are `admin/admin`. All data will be automatically cleared after a period of inactivity, so feel free to perform any operations. You can use it to test ebook pushing.        



# Announcement
Nov 19, 2024  **Release of KindleEar 3.2, featuring an AI-driven article summarizer.**    
Api Gemini/Grok/Mistral/Groq are free to use by now.    

**Significant Updates:**
* Full support for Python 3    
* Redesigned software architecture   
* Cross-platform support, freeing you from dependence on GAE platform   
* Support email delivery and online reading (Docker version), built-in online reader optimized for e-ink screens   
* Support for Calibre's recipe format without the need for modification    
* Built-in library of over a thousand Calibre recipe files    
* AI-driven summaries to help you stay updated on news in less time
* Integrated bilingual translation feature, breaking language barriers for effortless information     retrieval and language learning    
* Built-in text-to-speech functionality, transforming daily news into audio for easy consumption without reading   
* Includes a browser extension, enabling effortless creation of web scraping recipe without coding, facilitating seamless content delivery from any website (brag)     



# Introduction
KindleEar is a web application which can be deployed on various Python-hosting platforms or VPS.   
It automatically aggregates various web content into epub/mobi/mp3 and delivers it to your Kindle or other e-book readers daily.   
Support online reading too, including an online reader specifically optimized for e-ink screens.   


## The features included:
* Unlimited RSS/ATOM/JSON or web content collection with support for Calibre's recipe format   
* Unlimited custom feeds, directly enter RSS/ATOM/JSON link and title for automatic push    
* Multiple account management, supporting multiple users and multiple Kindles    
* Generate epub/mobi with images and table of contents    
* Automatic daily scheduled push or online reading    
* Built-in sharing library, you can directly subscribe to feeds shared by other users, and you can also share your own feeds with others    
* Powerful and convenient email forwarding service   
* Integration with systems like Evernote/Pocket/Instapaper/wallabag   





![Screenshot](https://raw.githubusercontent.com/cdhigh/KindleEar/master/docs/images/scrshot.gif)




# License
   KindleEar is Licensed under the MIT license.


# Contributors
* @rexdf <https://github.com/rexdf> 
* @insert0003 <https://github.com/insert0003> 
* @zhu327 <https://github.com/zhu327> 
* @lord63 <https://github.com/lord63> 
* @th0mass <https://github.com/th0mass> 
* @seff <https://github.com/seff> 
* @miaowm5 <https://github.com/miaowm5> 
* @bookfere <https://github.com/bookfere> 


<a href="https://www.buymeacoffee.com/cdhigh" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/default-orange.png" alt="Buy Me A Coffee" height="41" width="174"></a>

