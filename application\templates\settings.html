{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Settings") }} - KindleEar</title>
{% endblock -%}
{% block bodytag -%}
<body class="settings">
{% endblock -%}

{% block content -%}
{% set now = g.now() %}
<div class="main">
  {% if user.expires -%}
  <div class="grey">
    {{_("Your account will pause after {0}, please log in again before it expires.").format(user.expires.strftime("%Y-%m-%d")) }}
  </div>
  {% endif -%}
  <form class="pure-form pure-form-aligned" action="" method="POST">
    {% if tips -%}
    <div class="notice-box error">{{tips}}</div>
    {% endif -%}

    <fieldset>
      <legend>{{_("Base")}}</legend>
      <div class="pure-control-group">
        <label>{{_("Auto delivery")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="enable_send">
          {% set enable_send = user.cfg('enable_send') %}
          <option value="all" {% if enable_send=='all' %}selected="selected"{% endif %}>{{_("Recipes and custom RSS")}}</option>
          <option value="recipes" {% if enable_send=='recipes' %}selected="selected"{% endif %}>{{_("Recipes only")}}</option>
          <option value="no" {% if not enable_send %}selected="selected"{% endif %}>{{_("Disable all")}}</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label><em class="required">*</em> {{_("Kindle E-mail")}}</label>
        <input type="email" name="kindle_email" value="{{user.cfg('kindle_email')}}" placeholder="{{_('Seperated by comma')}}" class="pure-u-1 pure-u-sm-1-2" multiple />
      </div>
      {% if g.allowReader -%}
      <div class="pure-control-group">
        <label>{{_("Delivery mode")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="delivery_mode">
          {% set delivery_mode = user.cfg('delivery_mode') %}
          <option value="email,local" {% if 'email' in delivery_mode and 'local' in delivery_mode %}selected="selected"{% endif %}>{{_("Email delivery & online reading")}}</option>
          <option value="email" {% if delivery_mode=='email' %}selected="selected"{% endif %}>{{_("Email delivery")}}</option>
          <option value="local" {% if delivery_mode=='local' %}selected="selected"{% endif %}>{{_("Online reading")}}</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label class="tooltip" data-msg="{{_('Retention days for online books')}}">{{_("Web shelf")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="webshelf_days">
          {% set onlineDays = user.cfg('webshelf_days') %}
          <option value="0" {% if not onlineDays %}selected="selected"{% endif %}>{{_("No limit")}}</option>
          <option value="1" {% if onlineDays==1 %}selected="selected"{% endif %}>{{_("1 Day")}}</option>
          <option value="2" {% if onlineDays==2 %}selected="selected"{% endif %}>{{_("2 Days")}}</option>
          <option value="3" {% if onlineDays==3 %}selected="selected"{% endif %}>{{_("3 Days")}}</option>
          <option value="4" {% if onlineDays==4 %}selected="selected"{% endif %}>{{_("4 Days")}}</option>
          <option value="5" {% if onlineDays==5 %}selected="selected"{% endif %}>{{_("5 Days")}}</option>
          <option value="6" {% if onlineDays==6 %}selected="selected"{% endif %}>{{_("6 Days")}}</option>
          <option value="7" {% if onlineDays==7 %}selected="selected"{% endif %}>{{_("7 Days")}}</option>
          <option value="30" {% if onlineDays==30 %}selected="selected"{% endif %}>{{_("30 Days")}}</option>
        </select>
      </div>
      {% endif -%}
      <div class="pure-control-group">
        <label>{{_("Time zone")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="timezone">
          {% for (name, offset) in all_timezones.items() -%}
          <option value="{{offset}}" {% if user.cfg('timezone')==offset %}selected="selected"{% endif %}>{{name}}</option>
          {% endfor -%}
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Delivery days")}}</label>
        <input type="checkbox" id="mon" name="Monday" {% if not user.send_days or 0 in user.send_days %}checked="1"{% endif %} />
        <span class="font90" style="cursor:default;" onclick="ToggleWeekBtn('#mon');">{{_("Mon")}} &nbsp;</span>
        <input type="checkbox" id="tue" name="Tuesday" {% if not user.send_days or 1 in user.send_days %}checked="1"{% endif %} />
        <span class="font90" style="cursor:default;" onclick="ToggleWeekBtn('#tue');">{{_("Tue")}} &nbsp;</span>
        <input type="checkbox" id="wed" name="Wednesday" {% if not user.send_days or 2 in user.send_days %}checked="1"{% endif %} />
        <span class="font90" style="cursor:default;" onclick="ToggleWeekBtn('#wed');">{{_("Wed")}} &nbsp;</span>
        <input type="checkbox" id="thu" name="Thursday" {% if not user.send_days or 3 in user.send_days %}checked="1"{% endif %} />
        <span class="font90" style="cursor:default;" onclick="ToggleWeekBtn('#thu');">{{_("Thu")}} &nbsp;</span>
        <input type="checkbox" id="fri" name="Friday" {% if not user.send_days or 4 in user.send_days %}checked="1"{% endif %} />
        <span class="font90" style="cursor:default;" onclick="ToggleWeekBtn('#fri');">{{_("Fri")}} &nbsp;</span>
        <input type="checkbox" id="sat" name="Saturday" {% if not user.send_days or 5 in user.send_days %}checked="1"{% endif %} />
        <span class="font90" style="cursor:default;" onclick="ToggleWeekBtn('#sat');">{{_("Sat")}} &nbsp;</span>
        <input type="checkbox" id="sun" name="Sunday" {% if not user.send_days or 6 in user.send_days %}checked="1"{% endif %} />
        <span class="font90" style="cursor:default;" onclick="ToggleWeekBtn('#sun');">{{_("Sun")}} &nbsp;</span>
      </div>
      <div class="pure-control-group">
        <label>{{_("Delivery time")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="send_time">
          {% for i in range(0, 24) -%}
          <option value="{{ i }}" {% if user.send_time|int == i %}selected="selected"{% endif %}>{{ "%02d:00" % i }}</option>
          {% endfor -%}
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Book type")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="book_type">
          <option value="epub" {% if user.book_cfg('type') == 'epub' %}selected="selected"{% endif %}>epub</option>
          <option value="mobi" {% if user.book_cfg('type') != 'epub' %}selected="selected"{% endif %}>mobi</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label class="tooltip" data-msg="{{_('Determines final file size')}}">{{_("Device type")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="device_type">
          {% set currDev = user.book_cfg('device') %}
          {% for dev in output_profiles -%}
          <option value="{{dev.short_name}}" {% if currDev == dev.short_name %}selected="selected"{% endif %}>{{dev.name}}</option>
          {% endfor -%}
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Title format")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="title_fmt">
          {% set title_fmt = user.book_cfg('title_fmt') %}
          <option value="" {% if not title_fmt %}selected="selected"{% endif %}>{{_("Title Only")}}</option>
          <option value="title_time" {% if title_fmt=='title_time' %}selected="selected"{% endif %}>{{_("Title")}} {{_("Time")}}</option>
          <option value="title_[time]" {% if title_fmt=='title_[time]' %}selected="selected"{% endif %}>{{_("Title")}} [{{_("Time")}}]</option>
        </select>
      </div>
      {#<div class="pure-control-group">
        <label>{{_("Book mode")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="book_mode">
          {% set book_mode = user.book_cfg('mode') %}
          <option value="periodical" {% if not book_mode or book_mode=='periodical' %}selected="selected"{% endif %}>{{_("Periodical")}}</option>
          <option value="comic" {% if book_mode=='comic' %}selected="selected"{% endif %}>{{_("Comic")}}</option>
        </select>
      </div>#}
      <div class="pure-control-group">
        <label>{{_("Remove hyperlinks")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="remove_hyperlinks">
          {% set rm_links = user.book_cfg('rm_links') %}
          <option value="" {% if not rm_links %}selected="selected"{% endif %}>{{_("Do not remove hyperlinks")}}</option>
          <option value="image" {% if rm_links=='image' %}selected="selected"{% endif %}>{{_("Remove image links")}}</option>
          <option value="text" {% if rm_links=='text' %}selected="selected"{% endif %}>{{_("Remove text links")}}</option>
          <option value="all" {% if rm_links=='all' %}selected="selected"{% endif %}>{{_("Remove all hyperlinks")}}</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Navbar")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="navbar">
          {% set navbar = user.book_cfg('navbar') %}
          <option value="" {% if not navbar %}selected="selected"{% endif %}>{{_("Disable")}}</option>
          <option value="top_center" {% if navbar=='top_center' %}selected="selected"{% endif %}>{{_("Top Center")}}</option>
          <option value="top_left" {% if navbar=='top_left' %}selected="selected"{% endif %}>{{_("Top Left")}}</option>
          <option value="bottom_center" {% if navbar=='bottom_center' %}selected="selected"{% endif %}>{{_("Bottom Center")}}</option>
          <option value="bottom_left" {% if navbar=='bottom_left' %}selected="selected"{% endif %}>{{_("Bottom Left")}}</option>
        </select>
      </div>
    </fieldset>

    <fieldset id="adv_fieldset">
      <legend>{{_("Custom RSS")}}</legend>
      <div class="pure-control-group">
        <label><em class="required">*</em> {{_("Title")}}</label>
        <input type="text" name="rss_title" value="{{ user.book_cfg('title') }}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group">
        <label class="tooltip" data-msg="{{_('Sets the lookup dictionary')}}">{{_("Language")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="book_language">
        {% for langCode in langMap -%}
        {% if user.book_cfg('language') == langCode -%}
        <option value="{{langCode}}" selected="selected">{{langMap[langCode]}}</option>
        {% else -%}
        <option value="{{langCode}}">{{langMap[langCode]}}</option>
        {% endif -%}
        {% endfor -%}
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Oldest article")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="oldest">
          {% set oldest = user.book_cfg('oldest_article') %}
          <option value="0" {% if not oldest %}selected="selected"{% endif %}>{{_("No limit")}}</option>
          <option value="1" {% if oldest==1 %}selected="selected"{% endif %}>{{_("1 Day")}}</option>
          <option value="2" {% if oldest==2 %}selected="selected"{% endif %}>{{_("2 Days")}}</option>
          <option value="3" {% if oldest==3 %}selected="selected"{% endif %}>{{_("3 Days")}}</option>
          <option value="4" {% if oldest==4 %}selected="selected"{% endif %}>{{_("4 Days")}}</option>
          <option value="5" {% if oldest==5 %}selected="selected"{% endif %}>{{_("5 Days")}}</option>
          <option value="6" {% if oldest==6 %}selected="selected"{% endif %}>{{_("6 Days")}}</option>
          <option value="7" {% if oldest==7 %}selected="selected"{% endif %}>{{_("7 Days")}}</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Time format")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="time_fmt">
          {% set time_fmt = user.book_cfg('time_fmt') %}
          <option value="%Y-%m-%d" {% if time_fmt=="%Y-%m-%d" %}selected="selected"{% endif %}>{{now.strftime("%Y-%m-%d")}}</option>
          <option value="%a, %d %b %Y" {% if time_fmt=="%a, %d %b %Y" %}selected="selected"{% endif %}>{{now.strftime("%a, %d %b %Y")}}</option>
          <option value="%a, %b %d" {% if time_fmt=="%a, %b %d" %}selected="selected"{% endif %}>{{now.strftime("%a, %b %d")}}</option>
          <option value="%b %d" {% if time_fmt=="%b %d" %}selected="selected"{% endif %}>{{now.strftime("%b %d")}}</option>
          <option value="%m-%d" {% if time_fmt=="%m-%d" %}selected="selected"{% endif %}>{{now.strftime("%m-%d")}}</option>
          <option value="%m/%d" {% if time_fmt=="%m/%d" %}selected="selected"{% endif %}>{{now.strftime("%m/%d")}}</option>
          <option value="%d/%m" {% if time_fmt=="%d/%m" %}selected="selected"{% endif %}>{{now.strftime("%d/%m")}}</option>
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Author format")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="author_format">
          {% set author_fmt = user.book_cfg('author_fmt') %}
          <option value="" {% if not author_fmt %}selected="selected"{% endif %}>KindleEar</option>
          <option value="%Y-%m-%d" {% if author_fmt=='%Y-%m-%d' %}selected="selected"{% endif %}>{{now.strftime("%Y-%m-%d")}}</option>
          <option value="%m-%d" {% if author_fmt=='%m-%d' %}selected="selected"{% endif %}>{{now.strftime("%m-%d")}}</option>
          <option value="%m/%d" {% if author_fmt=='%m/%d' %}selected="selected"{% endif %}>{{now.strftime("%m/%d")}}</option>
          <option value="%d/%m" {% if author_fmt=='%d/%m' %}selected="selected"{% endif %}>{{now.strftime("%d/%m")}}</option>
          <option value="%b %d" {% if author_fmt=='%b %d' %}selected="selected"{% endif %}>{{now.strftime("%b %d")}}</option>
          <option value="%a, %b %d" {% if author_fmt=='%a, %b %d' %}selected="selected"{% endif %}>{{now.strftime("%a, %b %d")}}</option>
        </select>
      </div>
    </fieldset>
    {% set sm_srv = user.send_mail_service -%}
    {% set sm_srv_type = sm_srv.get('service', 'gae') -%}
    {% if session.get('role') == 'admin' or sm_srv_type != 'admin' -%}
    <fieldset id="send_mail_service">
      <legend>{{_("Send Mail Service")}}</legend>
      <div class="pure-control-group">
        <label>{{_("Service")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="sm_service" id="sm_service" onchange="SetSmOptiosVisualbility();">
          {% for (name, alias) in sm_services.items() -%}
          <option value="{{name}}" {% if sm_srv_type == name %}selected="selected"{% endif %}>{{alias}}</option>
          {% endfor -%}
        </select>
      </div>
      <div class="pure-control-group" id="sm_apikey">
        <label>{{_("ApiKey")}}</label>
        <input type="text" name="sm_apikey" value="{{sm_srv.get('apikey', '')}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group" id="sm_secret_key">
        <label>{{_("SecretKey")}}</label>
        <input type="text" name="sm_secret_key" value="{{sm_srv.get('secret_key', '')}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group" id="sm_host">
        <label>{{_("Host")}}</label>
        <input type="text" name="sm_host" value="{{sm_srv.get('host', '')}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group" id="sm_port">
        <label>{{_("Port")}}</label>
        <input type="text" name="sm_port" value="{{sm_srv.get('port', '')}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group" id="sm_username">
        <label>{{_("Username")}}</label>
        <input type="text" name="sm_username" value="{{sm_srv.get('username', '')}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group" id="sm_password">
        <label>{{_("Password")}}</label>
        <input type="password" name="sm_password" value="{{user.decrypt(sm_srv.get('password', ''))}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group" id="sm_save_path">
        <label>{{_("Save path")}}</label>
        <input type="text" name="sm_save_path" value="{{sm_srv.get('save_path', 'KindleEar')}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
    </fieldset>
    {% endif -%}
    {% if user.cfg('sender') -%}
    <p>
      {% autoescape off -%}
      {{_("Important: Please activate your kindle firstly, then goto %(personal)s Page and add %(sender)s to 'Approved Personal Document E-mail List'.", personal='<a href="https://www.amazon.com/hz/mycd/myx#/home/<USER>/payment" rel="external" target="_blank">' + _("Personal Document Settings") + '</a>', sender='<strong>' + user.cfg('sender') + '</strong>')|safe}}
      {% endautoescape -%}
    </p>
    {% else -%}
    <p style="color:red;">
      {% autoescape off -%}
      {{_("You have not yet set up your email address. Please go to the %(admin)s page to add your email address firstly.", admin='<a href="/account/change">' + _("Account") + '</a>')|safe}}
      {% endautoescape -%}
    </p>
    {% endif %}
    <hr/>
    <p style="text-align:center;">
      <button type="submit" class="pure-button pure-button-primary pure-input-rounded">{{_('Save settings')}} </button>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <button type="button" class="pure-button pure-input-rounded" onclick="SendTestEmail()"> {{_("Send Test Email")}}</button>
    </p>
  </form>
</div>
{% endblock -%}

{% block js -%}
<script type="text/javascript">
$(document).ready(function() {
  SetSmOptiosVisualbility();
});
</script>
{% endblock -%}