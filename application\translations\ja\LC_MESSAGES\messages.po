# Chinese translations for KindleEar.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the KindleEar project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: KindleEar v3.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-10 19:49-0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: KindleEar <<EMAIL>>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.14.0\n"

#: application/templates/admin.html:3 application/templates/base.html:53
#: application/templates/base.html:192 application/templates/settings.html:263
msgid "Account"
msgstr "アカウント"

#: application/templates/admin.html:19
msgid "Signup settings"
msgstr "サインアップ設定"

#: application/templates/admin.html:19
#: application/templates/adv_calibre_options.html:18
#: application/templates/adv_proxy.html:18
msgid "Save"
msgstr "保存"

#: application/templates/admin.html:21
#: application/templates/user_account.html:31
msgid "Email service"
msgstr "メールサービス"

#: application/templates/admin.html:23
#: application/templates/user_account.html:34
msgid "Same as admin"
msgstr "管理者と同じ"

#: application/templates/admin.html:24
#: application/templates/user_account.html:35
msgid "Independent"
msgstr "独立"

#: application/templates/admin.html:28
msgid "Signup type"
msgstr "サインアップタイプ"

#: application/templates/admin.html:30
msgid "Public"
msgstr "公開"

#: application/templates/admin.html:31
msgid "One time code"
msgstr "一回限りのコード"

#: application/templates/admin.html:32
msgid "Permanent code"
msgstr "永久コード"

#: application/templates/admin.html:36
msgid "Invitation codes"
msgstr "招待コード"

#: application/templates/admin.html:37
msgid "one code per line"
msgstr "1行ごとにコード"

#: application/templates/admin.html:43
msgid "Accounts"
msgstr "アカウント"

#: application/templates/admin.html:43
#: application/templates/adv_inboundmail.html:61
#: application/templates/my.html:34 application/view/admin.py:57
#: application/view/admin.py:64 application/view/admin.py:91
msgid "Add"
msgstr "追加"

#: application/templates/admin.html:54
#: application/templates/adv_archive.html:69
#: application/templates/home.html:19 application/templates/login.html:24
#: application/templates/logs.html:70
#: application/templates/reset_password.html:19
#: application/templates/reset_password.html:20
#: application/templates/settings.html:241
#: application/templates/signup.html:21
#: application/templates/user_account.html:15
msgid "Username"
msgstr "ユーザー名"

#: application/templates/admin.html:55
msgid "AutoSend"
msgstr "自動送信"

#: application/templates/admin.html:56
#: application/templates/change_password.html:27
#: application/templates/reset_password.html:26
#: application/templates/signup.html:33
#: application/templates/user_account.html:27
msgid "Email"
msgstr "Email"

#: application/templates/admin.html:57
#: application/templates/user_account.html:39
msgid "Expiration"
msgstr "有効期限"

#: application/templates/admin.html:58
msgid "Operation"
msgstr "操作"

#: application/templates/admin.html:65
msgid "Yes"
msgstr "はい"

#: application/templates/admin.html:65
msgid "No"
msgstr "いいえ"

#: application/templates/admin.html:68
msgid "Never"
msgstr "決して"

#: application/templates/admin.html:70
#: application/templates/adv_inboundmail.html:26
#: application/templates/settings.html:58
#: application/templates/settings.html:181
#: application/templates/user_account.html:43
msgid "7 Days"
msgstr "7日間"

#: application/templates/admin.html:72
#: application/templates/user_account.html:44
msgid "1 Month"
msgstr "1ヶ月"

#: application/templates/admin.html:74
#: application/templates/user_account.html:45
msgid "3 Months"
msgstr "3ヶ月"

#: application/templates/admin.html:76
#: application/templates/user_account.html:46
msgid "6 Months"
msgstr "6ヶ月"

#: application/templates/admin.html:78
#: application/templates/user_account.html:47
msgid "1 Year"
msgstr "1年"

#: application/templates/admin.html:80
#: application/templates/user_account.html:48
msgid "2 Years"
msgstr "2年"

#: application/templates/admin.html:85
#: application/templates/change_password.html:3
#: application/templates/change_password.html:13 application/view/admin.py:129
#: application/view/admin.py:183
msgid "Edit account"
msgstr "アカウントを編集"

#: application/templates/admin.html:89
#: application/templates/adv_inboundmail.html:54
#: application/templates/adv_uploadcss.html:31
#: application/templates/base.html:25 application/templates/webmail.html:22
msgid "Delete"
msgstr "削除"

#: application/templates/adv_archive.html:3
#: application/templates/adv_archive.html:14
#: application/templates/adv_base.html:57
#: application/templates/adv_base.html:61
msgid "Archive"
msgstr "アーカイブ"

#: application/templates/adv_archive.html:15
msgid "Append hyperlinks for archiving or sharing."
msgstr "アーカイブまたは共有用のハイパーリンクを追加。"

#: application/templates/adv_archive.html:40
msgid "Authorized"
msgstr "認証済み"

#: application/templates/adv_archive.html:42
msgid "Authorize"
msgstr "認証"

#: application/templates/adv_archive.html:53
msgid "Email or Username"
msgstr "メールアドレスまたはユーザー名"

#: application/templates/adv_archive.html:56
#: application/templates/adv_archive.html:72
#: application/templates/base.html:54 application/templates/home.html:20
#: application/templates/login.html:28 application/templates/settings.html:245
#: application/templates/signup.html:25
#: application/templates/user_account.html:19
msgid "Password"
msgstr "パスワード"

#: application/templates/adv_archive.html:59
#: application/templates/adv_archive.html:75
#: application/templates/base.html:64
msgid "Verify"
msgstr "確認"

#: application/templates/adv_archive.html:78
msgid "client_id"
msgstr "client_id"

#: application/templates/adv_archive.html:81
msgid "client_secret"
msgstr "client_secret"

#: application/templates/adv_archive.html:84
#: application/templates/settings.html:233
msgid "Host"
msgstr "ホスト"

#: application/templates/adv_archive.html:119
#: application/templates/adv_dict.html:79
#: application/templates/adv_inboundmail.html:34
#: application/templates/book_audiolator.html:113
#: application/templates/book_summarizer.html:88
#: application/templates/book_translator.html:80
#: application/templates/settings.html:269
msgid "Save settings"
msgstr "設定を保存"

#: application/templates/adv_base.html:39
#: application/templates/adv_base.html:43
#: application/templates/adv_delivernow.html:8
msgid "Deliver Now"
msgstr "今すぐ配信"

#: application/templates/adv_base.html:48
#: application/templates/adv_base.html:52
#: application/templates/adv_inboundmail.html:3
#: application/templates/adv_inboundmail.html:9
#: application/templates/adv_inboundmail.html:14
msgid "Inbound Mail"
msgstr "受信メール"

#: application/templates/adv_base.html:66
#: application/templates/adv_base.html:70
#: application/templates/adv_dict.html:3
#: application/templates/adv_dict.html:13
#: application/templates/reader.html:134
msgid "Dictionary"
msgstr "辞書"

#: application/templates/adv_base.html:75
#: application/templates/adv_base.html:83
#: application/templates/adv_proxy.html:3
#: application/templates/adv_proxy.html:12
msgid "Proxy"
msgstr "プロキシ"

#: application/templates/adv_base.html:92
#: application/templates/adv_base.html:96
#: application/templates/adv_import.html:8
msgid "Import Feeds"
msgstr "フィードをインポート"

#: application/templates/adv_base.html:101
#: application/templates/adv_base.html:105
msgid "Cover Image"
msgstr "カバー画像"

#: application/templates/adv_base.html:110
#: application/templates/adv_base.html:114
#: application/templates/adv_uploadcss.html:3
msgid "Stylesheet"
msgstr "スタイルシート"

#: application/templates/adv_base.html:119
#: application/templates/adv_base.html:123
#: application/templates/adv_calibre_options.html:3
#: application/templates/adv_calibre_options.html:12
msgid "Calibre Options"
msgstr "Calibreオプション"

#: application/templates/adv_calibre_options.html:13
msgid "Set the parameters for Calibre, in JSON dictionary format."
msgstr "CalibreのパラメータをJSON辞書形式で設定します。"

#: application/templates/adv_delivernow.html:3
msgid "Deliver now"
msgstr "今すぐ配信"

#: application/templates/adv_delivernow.html:9
msgid "Deliver selected recipes now."
msgstr "選択したレシピを今すぐ配信。"

#: application/templates/adv_delivernow.html:12
msgid "There are no recipes subscribed"
msgstr "購読中のレシピはありません。"

#: application/templates/adv_delivernow.html:17
#: application/templates/base.html:101
msgid "Sep"
msgstr "Sep"

#: application/templates/adv_delivernow.html:22
msgid "Select all"
msgstr "すべて選択"

#: application/templates/adv_delivernow.html:23
msgid "Select none"
msgstr "選択解除"

#: application/templates/adv_delivernow.html:28
msgid "Deliver"
msgstr "配信"

#: application/templates/adv_dict.html:15
msgid "Set up dictionaries for online reading."
msgstr "オンラインリーディング用の辞書を設定します。"

#: application/templates/adv_dict.html:18
#: application/templates/adv_dict.html:40
#: application/templates/adv_dict.html:62
msgid "Book language"
msgstr "本の言語"

#: application/templates/adv_dict.html:27
#: application/templates/adv_dict.html:49
#: application/templates/adv_dict.html:66
#: application/templates/book_summarizer.html:23
#: application/templates/book_translator.html:26
#: application/templates/word_lookup.html:59
msgid "Engine"
msgstr "エンジン"

#: application/templates/adv_dict.html:33
#: application/templates/adv_dict.html:55
#: application/templates/adv_dict.html:72
#: application/templates/word_lookup.html:65
msgid "Database"
msgstr "データベース"

#: application/templates/adv_dict.html:63
msgid "Other languages"
msgstr "他の言語"

#: application/templates/adv_dict.html:81
#: application/templates/word_lookup.html:3
#: application/templates/word_lookup.html:79
msgid "Word lookup"
msgstr "単語検索"

#: application/templates/adv_dict.html:86 application/view/reader.py:29
#: application/view/reader.py:86
msgid "Online reading feature has not been activated yet."
msgstr "オンラインリーディング機能はまだ有効化されていません。"

#: application/templates/adv_import.html:3
#: application/templates/adv_import.html:19
msgid "Import"
msgstr "インポート"

#: application/templates/adv_import.html:9
msgid "Import custom rss from an OPML file."
msgstr "OPMLファイルからカスタムRSSをインポート。"

#: application/templates/adv_import.html:15
msgid "Import as fulltext rss by default"
msgstr "デフォルトで全文RSSとしてインポート"

#: application/templates/adv_import.html:20
msgid "Download"
msgstr "ダウンロード"

#: application/templates/adv_inboundmail.html:11
msgid ""
"To enable the inbound email feature, you also need to configure the "
"whitelist."
msgstr "受信メール機能を有効にするには、ホワイトリストの設定も必要です。"

#: application/templates/adv_inboundmail.html:16
#: application/templates/adv_uploadcover.html:15
#: application/templates/book_summarizer.html:19
#: application/templates/book_translator.html:22
#: application/templates/settings.html:143
msgid "Disable"
msgstr "無効化"

#: application/templates/adv_inboundmail.html:17
msgid "Forward Only"
msgstr "転送のみ"

#: application/templates/adv_inboundmail.html:18
msgid "Save Only"
msgstr "保存のみ"

#: application/templates/adv_inboundmail.html:19
msgid "Save and Forward"
msgstr "保存して転送"

#: application/templates/adv_inboundmail.html:23
msgid "Email Retention"
msgstr "メール保存期間"

#: application/templates/adv_inboundmail.html:25
#: application/templates/settings.html:52
#: application/templates/settings.html:175
msgid "1 Day"
msgstr "1日"

#: application/templates/adv_inboundmail.html:27
#: application/templates/settings.html:59
msgid "30 Days"
msgstr "30日間"

#: application/templates/adv_inboundmail.html:28
#: application/templates/settings.html:51
#: application/templates/settings.html:174
msgid "No limit"
msgstr "制限なし"

#: application/templates/adv_inboundmail.html:36
msgid "Open webmail"
msgstr "Webメールを開く"

#: application/templates/adv_inboundmail.html:44
msgid "White List"
msgstr "ホワイトリスト"

#: application/templates/adv_inboundmail.html:46
#, python-format
msgid ""
"Emails sent to %(name)sxxx@%(mailHost)s will be forwarded to your kindle "
"email."
msgstr "%(name)sxxx@%(mailHost)sに送信されたメールは、あなたのKindleメールに転送されます。"

#: application/templates/adv_inboundmail.html:47
msgid "Example"
msgstr "例"

#: application/templates/adv_inboundmail.html:59
msgid "Please input mail address"
msgstr "メールアドレスを入力してください"

#: application/templates/adv_proxy.html:13
msgid "Supports"
msgstr "サポート"

#: application/templates/adv_proxy.html:24
#: application/templates/adv_proxy.html:29
#: application/templates/book_audiolator.html:132
#: application/templates/book_summarizer.html:105
#: application/templates/book_translator.html:97
msgid "Test"
msgstr "テスト"

#: application/templates/adv_uploadcover.html:3
msgid "Cover image"
msgstr "カバー画像"

#: application/templates/adv_uploadcover.html:9
msgid "Upload cover image"
msgstr "カバー画像をアップロード"

#: application/templates/adv_uploadcover.html:10
msgid ""
"Upload cover images from local with an aspect ratio of approximately 0.625."
msgstr "アスペクト比0.625のローカルカバー画像をアップロードしてください。"

#: application/templates/adv_uploadcover.html:13
msgid "Include cover"
msgstr "カバーを含む"

#: application/templates/adv_uploadcover.html:16
#: application/templates/book_summarizer.html:18
#: application/templates/book_translator.html:21
msgid "Enable"
msgstr "有効化"

#: application/templates/adv_uploadcover.html:20
msgid "Rule for cover"
msgstr "カバーのルール"

#: application/templates/adv_uploadcover.html:22
msgid "Random"
msgstr "ランダム"

#: application/templates/adv_uploadcover.html:23
#: application/templates/base.html:127
msgid "Weekday"
msgstr "平日"

#: application/templates/adv_uploadcover.html:49
msgid "Upload/Update"
msgstr "アップロード/更新"

#: application/templates/adv_uploadcss.html:22
msgid "Upload stylesheet"
msgstr "スタイルシートをアップロード"

#: application/templates/adv_uploadcss.html:23
msgid "Upload a stylesheet from local (accept utf-8 only)."
msgstr "ローカルからスタイルシートをアップロード（UTF-8のみ対応）。"

#: application/templates/adv_uploadcss.html:30
msgid "Upload"
msgstr "アップロード"

#: application/templates/autoback.html:3
msgid "Auto back"
msgstr "自動戻る"

#: application/templates/autoback.html:28
msgid "Auto back to previous page after 5 seconds"
msgstr "5秒後に前のページに自動で戻る"

#: application/templates/autoback.html:29
#: application/templates/tipsback.html:15
msgid "Click to back"
msgstr "戻るにはクリック"

#: application/templates/base.html:24 application/templates/reader.html:189
msgid "Confirm Deletion"
msgstr "削除を確認"

#: application/templates/base.html:26
msgid "Delete (Ctrl for no confirm)"
msgstr "削除（確認なしでCtrl）"

#: application/templates/base.html:27
msgid "View Source Code"
msgstr "ソースコードを表示"

#: application/templates/base.html:28
msgid "Subscribe (Deliver Separately)"
msgstr "購読（個別配信）"

#: application/templates/base.html:29
msgid "Subscribe"
msgstr "購読"

#: application/templates/base.html:30
msgid "Cannot add this custom rss, Error:"
msgstr "このカスタムRSSを追加できません、エラー："

#: application/templates/base.html:31
msgid "Cannot delete this feed, Error:"
msgstr "このフィードを削除できません、エラー："

#: application/templates/base.html:32
msgid "Fulltext"
msgstr "全文"

#: application/templates/base.html:33 application/templates/base.html:43
msgid "Share"
msgstr "共有"

#: application/templates/base.html:34 application/templates/reader.html:190
msgid "Are you sure to delete?"
msgstr "本当に削除しますか？"

#: application/templates/base.html:35
msgid "Report to the server that this feed is invalid."
msgstr "このフィードが無効であることをサーバーに報告します。"

#: application/templates/base.html:36
msgid "Are you sure to REMOVE ALL CUSTOM RSS?"
msgstr "すべてのカスタムRSSを削除してもよろしいですか？"

#: application/templates/base.html:37
msgid "Share links, share happiness"
msgstr "リンクを共有し、幸福を共有する"

#: application/templates/base.html:38
msgid "Category"
msgstr "カテゴリー"

#: application/templates/base.html:39
#: application/templates/book_audiolator.html:58
#: application/templates/book_summarizer.html:43
#: application/templates/settings.html:159
msgid "Language"
msgstr "言語"

#: application/templates/base.html:40
msgid ""
"Please write a category in text field if the one you wish is not in the "
"list."
msgstr "希望のカテゴリーがリストにない場合は、テキストフィールドに書いてください。"

#: application/templates/base.html:41
msgid "Ok"
msgstr "OK"

#: application/templates/base.html:42
msgid "Cancel"
msgstr "キャンセル"

#: application/templates/base.html:44
msgid "Language code invalid"
msgstr "言語コードが無効です"

#: application/templates/base.html:45
msgid "Thank you for sharing."
msgstr "共有ありがとうございます。"

#: application/templates/base.html:46 application/templates/reader.html:159
msgid "Close"
msgstr "閉じる"

#: application/templates/base.html:47
msgid "Unsubscribe"
msgstr "購読解除"

#: application/templates/base.html:48
msgid "Cannot subscribe this recipe, Error:"
msgstr "このレシピを購読できません、エラー："

#: application/templates/base.html:49
msgid "Are you sure to Unsubscribe ({0})?"
msgstr "本当に購読解除しますか（{0}）？"

#: application/templates/base.html:50
msgid "Cannot unsubscribe this recipe, Error:"
msgstr "このレシピを購読解除できません、エラー："

#: application/templates/base.html:51
msgid "The recipe is already subscribed."
msgstr "このレシピは既に購読されています。"

#: application/templates/base.html:52
msgid "Website login lnformation"
msgstr "ウェブサイトのログイン情報"

#: application/templates/base.html:55
msgid "Submit"
msgstr "送信"

#: application/templates/base.html:56
msgid ""
"If any field is left blank, the server will clear the saved login "
"information."
msgstr "任意のフィールドが空白のままだと、サーバーは保存されたログイン情報をクリアします。"

#: application/templates/base.html:57
msgid "Cannot set the website login information, Error:"
msgstr "ウェブサイトのログイン情報を設定できません、エラー："

#: application/templates/base.html:58 application/templates/my.html:58
msgid "Upload custom recipe"
msgstr "カスタムレシピをアップロード"

#: application/templates/base.html:59
msgid "Congratulations"
msgstr "おめでとうございます"

#: application/templates/base.html:60
msgid "Thanks"
msgstr "ありがとうございます"

#: application/templates/base.html:61
msgid ""
"Your recipe has been uploaded, and it can be found in the Library section. "
"If you dont see it, please make sure to switch to the correct language."
msgstr "レシピがアップロードされました。ライブラリセクションで見つけることができます。見当たらない場合は、正しい言語に切り替えてください。"

#: application/templates/base.html:62
msgid "Your recipe have been deleted."
msgstr "レシピは削除されました。"

#: application/templates/base.html:63
msgid "Kindleify Selection"
msgstr "Kindleify選択"

#: application/templates/base.html:65
msgid "Verified"
msgstr "確認済み"

#: application/templates/base.html:66 application/view/login.py:79
#: application/view/share.py:157
msgid "The username does not exist or password is wrong."
msgstr "ユーザー名が存在しないか、パスワードが間違っています。"

#: application/templates/base.html:67
msgid "The file you chosen is not an acceptable type."
msgstr "選択したファイルは許可されたタイプではありません。"

#: application/templates/base.html:68
msgid "The file have been uploaded successfully."
msgstr "ファイルは正常にアップロードされました。"

#: application/templates/base.html:69 application/templates/library.html:67
msgid "This feed has been successfully subscribed."
msgstr "このフィードは正常に購読されました。"

#: application/templates/base.html:70
msgid "Thank you for your feedback, this feed will be reviewed soon."
msgstr "フィードバックありがとうございます。このフィードは近日中にレビューされます。"

#: application/templates/base.html:71
msgid "Are you confirming to share the recipe ({0})?"
msgstr "レシピ({0})を共有することを確認しますか？"

#: application/templates/base.html:72
msgid "[All]"
msgstr "[すべて]"

#: application/templates/base.html:73
msgid "[By Time]"
msgstr "[時間順]"

#: application/templates/base.html:74
msgid "[Random]"
msgstr "[ランダム]"

#: application/templates/base.html:75
msgid "[Uncategoried]"
msgstr "[カテゴリなし]"

#: application/templates/base.html:76
msgid "There are no links found."
msgstr "リンクが見つかりません。"

#: application/templates/base.html:77
msgid "Invalid report"
msgstr "無効なレポート"

#: application/templates/base.html:78
msgid "Are you confirming that this link is invalid or off the cloud?"
msgstr "このリンクが無効か、クラウド外であることを確認しますか？"

#: application/templates/base.html:79
msgid "Customize delivery time"
msgstr "配達時間をカスタマイズ"

#: application/templates/base.html:80 application/templates/settings.html:72
msgid "Delivery days"
msgstr "配達日"

#: application/templates/base.html:81 application/templates/settings.html:74
msgid "Mon"
msgstr "月"

#: application/templates/base.html:82 application/templates/settings.html:76
msgid "Tue"
msgstr "火"

#: application/templates/base.html:83 application/templates/settings.html:78
msgid "Wed"
msgstr "水"

#: application/templates/base.html:84 application/templates/settings.html:80
msgid "Thu"
msgstr "木"

#: application/templates/base.html:85 application/templates/settings.html:82
msgid "Fri"
msgstr "金"

#: application/templates/base.html:86 application/templates/settings.html:84
msgid "Sat"
msgstr "土"

#: application/templates/base.html:87 application/templates/settings.html:86
msgid "Sun"
msgstr "日"

#: application/templates/base.html:88
msgid "Delivery times"
msgstr "配達時間"

#: application/templates/base.html:89
msgid ""
"The customized delivery time for the recipe has been successfully saved."
msgstr "レシピのカスタマイズされた配達時間は正常に保存されました。"

#: application/templates/base.html:90
msgid "The account have been deleted."
msgstr "アカウントは削除されました。"

#: application/templates/base.html:91 application/view/share.py:147
msgid "The username or password is empty."
msgstr "ユーザー名またはパスワードが空です。"

#: application/templates/base.html:92 application/view/admin.py:81
#: application/view/admin.py:165 application/view/admin.py:191
#: application/view/login.py:220 application/view/login.py:273
msgid "The two new passwords are dismatch."
msgstr "2つの新しいパスワードが一致しません。"

#: application/templates/base.html:93
msgid "Password changed successfully."
msgstr "パスワードが正常に変更されました。"

#: application/templates/base.html:94
msgid "Account added successfully."
msgstr "アカウントが正常に追加されました。"

#: application/templates/base.html:95 application/view/login.py:128
msgid "login required"
msgstr "ログインが必要です"

#: application/templates/base.html:96
msgid "Upload cover files successfully."
msgstr "カバーファイルは正常にアップロードされました。"

#: application/templates/base.html:97
msgid ""
"Total size of the files you selected exceeds 16MB. Please reduce the image "
"resolution or upload in batches."
msgstr "選択したファイルの合計サイズが16MBを超えています。画像の解像度を下げるか、バッチでアップロードしてください。"

#: application/templates/base.html:98
#: application/templates/book_translator.html:3
#: application/templates/book_translator.html:17
msgid "Bilingual Translator"
msgstr "バイリンガル翻訳者"

#: application/templates/base.html:99
#: application/templates/book_summarizer.html:3
#: application/templates/book_summarizer.html:14
msgid "AI Summarizer"
msgstr "AI要約者"

#: application/templates/base.html:100
msgid "Upl"
msgstr "Upl"

#: application/templates/base.html:102
msgid "Log"
msgstr "Log"

#: application/templates/base.html:103
msgid "Emb"
msgstr "Emb"

#: application/templates/base.html:104
msgid "Tr"
msgstr "Tr"

#: application/templates/base.html:105
msgid "Tts"
msgstr "Tts"

#: application/templates/base.html:106
msgid "Ai"
msgstr "Ai"

#: application/templates/base.html:107
msgid ""
"The test email has been successfully sent to the following addresses. Please"
" check your inbox or spam folder to confirm its delivery. Depending on your "
"email server, there may be a slight delay."
msgstr ""
"テストメールは以下のアドレスに正常に送信されました。受信箱またはスパムフォルダを確認して、配信を確認してください。メールサーバーによっては、若干の遅延が生じる場合があります。"

#: application/templates/base.html:108
msgid "Processing..."
msgstr "処理中..."

#: application/templates/base.html:109
msgid "The configuration validation is correct."
msgstr "設定の検証は正しいです。"

#: application/templates/base.html:110 application/templates/logs.html:23
#: application/templates/logs.html:72 application/templates/my.html:17
#: application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/settings.html:155
msgid "Title"
msgstr "タイトル"

#: application/templates/base.html:111
#: application/templates/book_audiolator.html:3
#: application/templates/book_audiolator.html:20
msgid "Text to Speech"
msgstr "テキスト読み上げ"

#: application/templates/base.html:112
msgid "Action"
msgstr "アクション"

#: application/templates/base.html:113
msgid "File"
msgstr "ファイル"

#: application/templates/base.html:114
msgid "Upload Only"
msgstr "アップロードのみ"

#: application/templates/base.html:115
msgid "Send"
msgstr "送信"

#: application/templates/base.html:116 application/templates/logs.html:54
msgid "There is nothing here."
msgstr "ここには何もありません。"

#: application/templates/base.html:117
msgid "Please select a single item."
msgstr "1つの項目を選択してください。"

#: application/templates/base.html:118 application/templates/reader.html:191
msgid "Please select at least one item."
msgstr "少なくとも1つの項目を選択してください。"

#: application/templates/base.html:119 application/view/admin.py:77
#: application/view/admin.py:152 application/view/admin.py:189
#: application/view/adv.py:459 application/view/extension.py:34
#: application/view/extension.py:63 application/view/inbound_email.py:470
#: application/view/inbound_email.py:478 application/view/inbound_email.py:493
#: application/view/inbound_email.py:514 application/view/login.py:216
#: application/view/login.py:245 application/view/reader.py:109
#: application/view/reader.py:126 application/view/share.py:37
msgid "Some parameters are missing or wrong."
msgstr "いくつかのパラメータが不足しているか、間違っています。"

#: application/templates/base.html:120
msgid "The email has been sent."
msgstr "メールは送信されました。"

#: application/templates/base.html:121 application/templates/webmail.html:29
msgid "From"
msgstr "差出人"

#: application/templates/base.html:122 application/templates/logs.html:25
#: application/templates/logs.html:74 application/templates/webmail.html:30
msgid "To"
msgstr "宛先"

#: application/templates/base.html:123 application/templates/webmail.html:31
msgid "Subject"
msgstr "件名"

#: application/templates/base.html:124 application/templates/logs.html:22
#: application/templates/logs.html:71 application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/webmail.html:32
msgid "Time"
msgstr "時間"

#: application/templates/base.html:125 application/templates/logs.html:24
#: application/templates/logs.html:73 application/templates/webmail.html:33
msgid "Size"
msgstr "サイズ"

#: application/templates/base.html:126
msgid "Date type"
msgstr "日付タイプ"

#: application/templates/base.html:128
msgid "Date"
msgstr "日付"

#: application/templates/base.html:129
msgid "This setting is prioritized."
msgstr "この設定は優先されています。"

#: application/templates/base.html:130
msgid "Combine multiple values with commas."
msgstr "複数の値をカンマで区切って結合してください。"

#: application/templates/base.html:131
msgid "Put dictionary in dict folder"
msgstr "辞書をdictフォルダに入れてください"

#: application/templates/base.html:156 application/templates/home.html:16
msgid "Logout"
msgstr "ログアウト"

#: application/templates/base.html:158 application/templates/home.html:21
#: application/templates/login.html:3 application/templates/login.html:22
#: application/templates/login.html:33
msgid "Login"
msgstr "ログイン"

#: application/templates/base.html:160 application/templates/signup.html:3
#: application/templates/signup.html:19 application/templates/signup.html:43
msgid "Signup"
msgstr "サインアップ"

#: application/templates/base.html:189 application/templates/home.html:15
#: application/templates/my.html:3
msgid "Feeds"
msgstr "フィード"

#: application/templates/base.html:190 application/templates/settings.html:3
msgid "Settings"
msgstr "設定"

#: application/templates/base.html:191 application/templates/logs.html:3
msgid "Logs"
msgstr "ログ"

#: application/templates/base.html:193
msgid "Advanced"
msgstr "詳細"

#: application/templates/base.html:194 application/templates/library.html:3
msgid "Shared"
msgstr "共有"

#: application/templates/base.html:195 application/templates/reader.html:6
msgid "Reader"
msgstr "リーダー"

#: application/templates/book_audiolator.html:22
#: application/templates/book_summarizer.html:16
#: application/templates/book_translator.html:19
msgid "State"
msgstr "状態"

#: application/templates/book_audiolator.html:24
msgid "Send Ebook and Audio"
msgstr "電子書籍とオーディオを送信"

#: application/templates/book_audiolator.html:25
msgid "Send Audio only"
msgstr "音声のみを送信"

#: application/templates/book_audiolator.html:26
msgid "Disable TTS"
msgstr "TTSを無効にする"

#: application/templates/book_audiolator.html:30
msgid "Send Audio To"
msgstr "音声を送信先"

#: application/templates/book_audiolator.html:31
msgid "Empty to use Kindle_email"
msgstr "Kindle_emailを使用するには空にしてください"

#: application/templates/book_audiolator.html:35
msgid "TTS Engine"
msgstr "TTSエンジン"

#: application/templates/book_audiolator.html:41
#: application/templates/book_summarizer.html:35
#: application/templates/book_translator.html:32
msgid "Api Host"
msgstr "APIホスト"

#: application/templates/book_audiolator.html:42
#: application/templates/book_summarizer.html:36
#: application/templates/book_summarizer.html:75
msgid "Leave empty to use default"
msgstr "デフォルトを使用するには空にしてください"

#: application/templates/book_audiolator.html:46
msgid "Region"
msgstr "地域"

#: application/templates/book_audiolator.html:53
#: application/templates/book_summarizer.html:39
#: application/templates/book_translator.html:36
msgid "Api Key"
msgstr "Api Key"

#: application/templates/book_audiolator.html:66
msgid "Voice name"
msgstr "音声名"

#: application/templates/book_audiolator.html:73
msgid "Voice speed"
msgstr "音声の速さ"

#: application/templates/book_audiolator.html:75
msgid "Extra slow"
msgstr "極端に遅い"

#: application/templates/book_audiolator.html:76
msgid "Slow"
msgstr "遅い"

#: application/templates/book_audiolator.html:77
#: application/templates/book_audiolator.html:87
#: application/templates/book_audiolator.html:97
msgid "Medium"
msgstr "普通"

#: application/templates/book_audiolator.html:78
msgid "Fast"
msgstr "速い"

#: application/templates/book_audiolator.html:79
msgid "Extra fast"
msgstr "極端に速い"

#: application/templates/book_audiolator.html:83
msgid "Voice pitch"
msgstr "音声の高さ"

#: application/templates/book_audiolator.html:85
msgid "Extra low"
msgstr "極端に低い"

#: application/templates/book_audiolator.html:86
msgid "Low"
msgstr "低い"

#: application/templates/book_audiolator.html:88
msgid "High"
msgstr "高い"

#: application/templates/book_audiolator.html:89
msgid "Extra high"
msgstr "極端に高い"

#: application/templates/book_audiolator.html:93
msgid "Voice volume"
msgstr "音量"

#: application/templates/book_audiolator.html:95
msgid "Extra soft"
msgstr "極端に小さい"

#: application/templates/book_audiolator.html:96
msgid "Soft"
msgstr "小さい"

#: application/templates/book_audiolator.html:98
msgid "Loud"
msgstr "大きい"

#: application/templates/book_audiolator.html:99
msgid "Extra loud"
msgstr "極端に大きい"

#: application/templates/book_audiolator.html:105
#: application/templates/book_summarizer.html:80
#: application/templates/book_translator.html:72
msgid "Apply to all subscribed recipes"
msgstr "すべての購読レシピに適用"

#: application/templates/book_audiolator.html:110
#: application/templates/book_summarizer.html:85
#: application/templates/book_translator.html:77
msgid ""
"Note: Enabling this feature will significantly increase consumed CPU "
"instance hours."
msgstr "注意：この機能を有効にすると、消費されるCPUインスタンス時間が大幅に増加します。"

#: application/templates/book_audiolator.html:119
#: application/templates/book_summarizer.html:94
#: application/templates/book_translator.html:86
msgid "Test (Please save settings firstly)"
msgstr "テスト（設定を先に保存してください）"

#: application/templates/book_audiolator.html:121
#: application/templates/book_summarizer.html:96
#: application/templates/book_translator.html:88
msgid "Text"
msgstr "テキスト"

#: application/templates/book_audiolator.html:127
msgid "Your browser does not support the audio element."
msgstr "あなたのブラウザは音声要素をサポートしていません。"

#: application/templates/book_summarizer.html:29
msgid "Model"
msgstr "Model"

#: application/templates/book_summarizer.html:45
msgid "Auto"
msgstr "自動"

#: application/templates/book_summarizer.html:56
msgid "Summary words"
msgstr "要約の単語"

#: application/templates/book_summarizer.html:70
msgid "Summary style"
msgstr "要約スタイル"

#: application/templates/book_summarizer.html:74
msgid "Placeholders available:"
msgstr "使用可能なプレースホルダー："

#: application/templates/book_summarizer.html:74
msgid "Custom prompt"
msgstr "カスタムプロンプト"

#: application/templates/book_summarizer.html:100
msgid "Summary"
msgstr "要約"

#: application/templates/book_translator.html:37
msgid "One key per line"
msgstr "1行ごとに1つのキー"

#: application/templates/book_translator.html:40
#: application/templates/word_lookup.html:51
msgid "Source language"
msgstr "ソース言語"

#: application/templates/book_translator.html:46
msgid "Target language"
msgstr "ターゲット言語"

#: application/templates/book_translator.html:52
msgid "Translation Position"
msgstr "翻訳位置"

#: application/templates/book_translator.html:54
msgid "Below original"
msgstr "オリジナルの下"

#: application/templates/book_translator.html:55
msgid "Above original"
msgstr "オリジナルの上"

#: application/templates/book_translator.html:56
msgid "Left to original"
msgstr "オリジナルの左"

#: application/templates/book_translator.html:57
msgid "Right to original"
msgstr "オリジナルの右"

#: application/templates/book_translator.html:58
msgid "Translated text only"
msgstr "翻訳されたテキストのみ"

#: application/templates/book_translator.html:62
msgid "Original text style"
msgstr "オリジナルテキストスタイル"

#: application/templates/book_translator.html:66
msgid "Translated text style"
msgstr "翻訳テキストスタイル"

#: application/templates/book_translator.html:92
msgid "Translation"
msgstr "翻訳"

#: application/templates/change_password.html:15
msgid "Old password"
msgstr "旧パスワード"

#: application/templates/change_password.html:19
#: application/templates/reset_password.html:31
#: application/templates/reset_password.html:32
msgid "New password"
msgstr "新しいパスワード"

#: application/templates/change_password.html:23
#: application/templates/reset_password.html:35
#: application/templates/reset_password.html:36
#: application/templates/signup.html:29
#: application/templates/user_account.html:23
msgid "Confirm password"
msgstr "パスワード確認"

#: application/templates/change_password.html:31
msgid "Share key"
msgstr "共有キー"

#: application/templates/change_password.html:37
msgid "Confirm Change"
msgstr "変更を確認"

#: application/templates/debug_cmd.html:3
msgid "Debug cmd"
msgstr "デバッグコマンド"

#: application/templates/home.html:3
msgid "Home"
msgstr "ホーム"

#: application/templates/home.html:10 application/templates/login.html:18
msgid "You are in DEMO mode. Logging out will delete all data."
msgstr "デモモードです。ログアウトするとすべてのデータが削除されます。"

#: application/templates/home.html:12
msgid "Sharing Joyful News Every Step of the Way"
msgstr "すべてのステップで喜ばしいニュースを共有"

#: application/templates/home.html:31
msgid "Inherited From Calibre"
msgstr "Calibreから継承"

#: application/templates/home.html:34
#, python-format
msgid ""
"Empowered by %(calibre)s, you can easily create e-books on a Python-"
"supported online platform and seamlessly transfer them to your e-reader or "
"other reading devices."
msgstr ""
"%(calibre)sによってサポートされ、Python対応のオンラインプラットフォームで簡単に電子書籍を作成し、電子リーダーや他の読書デバイスにシームレスに転送できます。"

#: application/templates/home.html:42
msgid "Share Your Ideas"
msgstr "アイデアを共有"

#: application/templates/home.html:45
#, python-format
msgid ""
"With the open-source %(kindleear)s application, you can set up your own "
"server to deliver daily news feeds to your e-reader and effortlessly share "
"the service with friends."
msgstr ""
"オープンソースの%(kindleear)sアプリケーションを使って、自分のサーバーを設定し、電子リーダーに毎日のニュースフィードを配信し、簡単にサービスを友達と共有できます。"

#: application/templates/library.html:50 application/templates/my.html:61
msgid "Search"
msgstr "検索"

#: application/templates/login.html:38 application/view/login.py:197
#: application/view/login.py:204
msgid ""
"The website does not allow registration. You can ask the owner for an "
"account."
msgstr "このウェブサイトでは登録を許可していません。アカウントについてはオーナーに問い合わせてください。"

#: application/templates/logs.html:11
msgid "Only display last 20 logs"
msgstr "最後の20件のログのみ表示"

#: application/templates/logs.html:26 application/templates/logs.html:75
msgid "Status"
msgstr "状態"

#: application/templates/logs.html:58
msgid "Logs of other users"
msgstr "他のユーザーのログ"

#: application/templates/my.html:12 application/templates/settings.html:153
msgid "Custom RSS"
msgstr "カスタムRSS"

#: application/templates/my.html:23
msgid "Content Embedded"
msgstr "コンテンツ埋め込み"

#: application/templates/my.html:27
msgid "Deliver Separately"
msgstr "個別に配信"

#: application/templates/my.html:46
msgid "Subscribed"
msgstr "購読済み"

#: application/templates/my.html:51
msgid "Library"
msgstr "ライブラリ"

#: application/templates/my.html:51
msgid "get more recipes"
msgstr "もっとrecipesを取得"

#: application/templates/my.html:68
msgid "Subscription to selected recipe successful."
msgstr "選択したレシピの購読に成功しました。"

#: application/templates/my.html:71
msgid "Bookmarklet"
msgstr "ブックマークレット"

#: application/templates/my.html:75
msgid "Send to Kindle"
msgstr "Kindleに送信"

#: application/templates/my.html:79
msgid "Subscribe with KindleEar"
msgstr "KindleEarで購読"

#: application/templates/my.html:82
msgid "Drag and drop this link to your bookmarks"
msgstr "このリンクをブックマークにドラッグアンドドロップ"

#: application/templates/my.html:86
msgid "Chrome extension"
msgstr "Chrome拡張機能"

#: application/templates/my.html:87
msgid "Edge extension"
msgstr "Edge拡張機能"

#: application/templates/my.html:89
msgid "Browser extensions also available"
msgstr "ブラウザ拡張機能も利用可能"

#: application/templates/reader.html:57
msgid "Push current book"
msgstr "現在の本をプッシュ"

#: application/templates/reader.html:63
msgid "Push current article"
msgstr "現在の記事をプッシュ"

#: application/templates/reader.html:71
msgid "Delete selected books"
msgstr "選択した本を削除"

#: application/templates/reader.html:77
msgid "Allow click links"
msgstr "リンクのクリックを許可"

#: application/templates/reader.html:83
msgid "Top-left dict mode"
msgstr "左上辞書モード"

#: application/templates/reader.html:89
msgid "Dark mode"
msgstr "ダークモード"

#: application/templates/reader.html:95
msgid "eInk mode"
msgstr "eInkモード"

#: application/templates/reader.html:101
msgid "Increase font size"
msgstr "フォントサイズを大きくする"

#: application/templates/reader.html:107
msgid "Decrease font size"
msgstr "フォントサイズを小さくする"

#: application/templates/reader.html:113
msgid "Visualize Touch Regions"
msgstr "タッチ領域を視覚化"

#: application/templates/reader.html:119
msgid "Help"
msgstr "ヘルプ"

#: application/templates/reader.html:125
#: application/templates/reader_404.html:135
msgid "Menu"
msgstr "メニュー"

#: application/templates/reader.html:139
msgid "Collapse all"
msgstr "すべて折りたたむ"

#: application/templates/reader.html:144
msgid "Expand all"
msgstr "すべて展開"

#: application/templates/reader.html:149
#: application/templates/reader_404.html:140
msgid "Prev"
msgstr "前へ"

#: application/templates/reader.html:154
#: application/templates/reader_404.html:143
msgid "Next page"
msgstr "次のページ"

#: application/templates/reader.html:192
msgid "Pushed successfully."
msgstr "正常にプッシュされました。"

#: application/templates/reader.html:193
msgid "There are currently no books or articles being read."
msgstr "現在、読まれている本や記事はありません。"

#: application/templates/reset_password.html:3
#: application/templates/reset_password.html:41
msgid "Reset password"
msgstr "パスワードをリセット"

#: application/templates/settings.html:14
msgid ""
"Your account will pause after {0}, please log in again before it expires."
msgstr "{0}後にアカウントが一時停止されます。期限前に再ログインしてください。"

#: application/templates/settings.html:23
msgid "Base"
msgstr "ベース"

#: application/templates/settings.html:25
msgid "Auto delivery"
msgstr "自動配信"

#: application/templates/settings.html:28
msgid "Recipes and custom RSS"
msgstr "RecipesとカスタムRSS"

#: application/templates/settings.html:29
msgid "Recipes only"
msgstr "Recipesのみ"

#: application/templates/settings.html:30
msgid "Disable all"
msgstr "すべて無効"

#: application/templates/settings.html:34
msgid "Kindle E-mail"
msgstr "Kindle Eメール"

#: application/templates/settings.html:35
msgid "Seperated by comma"
msgstr "カンマ区切り"

#: application/templates/settings.html:39
msgid "Delivery mode"
msgstr "配信モード"

#: application/templates/settings.html:42
msgid "Email delivery & online reading"
msgstr "メール配信＆オンライン読書"

#: application/templates/settings.html:43
msgid "Email delivery"
msgstr "メール配信"

#: application/templates/settings.html:44
msgid "Online reading"
msgstr "オンライン読書"

#: application/templates/settings.html:48
msgid "Retention days for online books"
msgstr "オンライン書籍の保持日数"

#: application/templates/settings.html:48
msgid "Web shelf"
msgstr "ウェブ棚"

#: application/templates/settings.html:53
#: application/templates/settings.html:176
msgid "2 Days"
msgstr "2日"

#: application/templates/settings.html:54
#: application/templates/settings.html:177
msgid "3 Days"
msgstr "3日"

#: application/templates/settings.html:55
#: application/templates/settings.html:178
msgid "4 Days"
msgstr "4日"

#: application/templates/settings.html:56
#: application/templates/settings.html:179
msgid "5 Days"
msgstr "5日"

#: application/templates/settings.html:57
#: application/templates/settings.html:180
msgid "6 Days"
msgstr "6日"

#: application/templates/settings.html:64
msgid "Time zone"
msgstr "タイムゾーン"

#: application/templates/settings.html:89
msgid "Delivery time"
msgstr "配信時間"

#: application/templates/settings.html:97
msgid "Book type"
msgstr "書籍タイプ"

#: application/templates/settings.html:104
msgid "Determines final file size"
msgstr "最終ファイルサイズを決定"

#: application/templates/settings.html:104
msgid "Device type"
msgstr "デバイスタイプ"

#: application/templates/settings.html:113
msgid "Title format"
msgstr "タイトル形式"

#: application/templates/settings.html:116
msgid "Title Only"
msgstr "タイトルのみ"

#: application/templates/settings.html:130
msgid "Remove hyperlinks"
msgstr "ハイパーリンクを削除"

#: application/templates/settings.html:133
msgid "Do not remove hyperlinks"
msgstr "ハイパーリンクを削除しない"

#: application/templates/settings.html:134
msgid "Remove image links"
msgstr "画像リンクを削除"

#: application/templates/settings.html:135
msgid "Remove text links"
msgstr "テキストリンクを削除"

#: application/templates/settings.html:136
msgid "Remove all hyperlinks"
msgstr "すべてのハイパーリンクを削除"

#: application/templates/settings.html:140
msgid "Navbar"
msgstr "ナビゲーションバー"

#: application/templates/settings.html:144
msgid "Top Center"
msgstr "上部中央"

#: application/templates/settings.html:145
msgid "Top Left"
msgstr "上部左"

#: application/templates/settings.html:146
msgid "Bottom Center"
msgstr "下部中央"

#: application/templates/settings.html:147
msgid "Bottom Left"
msgstr "下部左"

#: application/templates/settings.html:159
msgid "Sets the lookup dictionary"
msgstr "検索辞書を設定"

#: application/templates/settings.html:171
msgid "Oldest article"
msgstr "最古の記事"

#: application/templates/settings.html:185
msgid "Time format"
msgstr "時間形式"

#: application/templates/settings.html:198
msgid "Author format"
msgstr "著者形式"

#: application/templates/settings.html:215
msgid "Send Mail Service"
msgstr "メール送信サービス"

#: application/templates/settings.html:217
msgid "Service"
msgstr "サービス"

#: application/templates/settings.html:225
msgid "ApiKey"
msgstr "ApiKey"

#: application/templates/settings.html:229
msgid "SecretKey"
msgstr "SecretKey"

#: application/templates/settings.html:237
msgid "Port"
msgstr "ポート"

#: application/templates/settings.html:249
msgid "Save path"
msgstr "保存パス"

#: application/templates/settings.html:257
#, python-format
msgid ""
"Important: Please activate your kindle firstly, then goto %(personal)s Page "
"and add %(sender)s to 'Approved Personal Document E-mail List'."
msgstr ""
"重要: "
"まずKindleを有効化してください。その後、%(personal)sページに移動し、%(sender)sを「承認された個人文書のEメールリスト」に追加してください。"

#: application/templates/settings.html:257
msgid "Personal Document Settings"
msgstr "個人文書設定"

#: application/templates/settings.html:263
#, python-format
msgid ""
"You have not yet set up your email address. Please go to the %(admin)s page "
"to add your email address firstly."
msgstr "まだメールアドレスを設定していません。まず、%(admin)sページに移動してメールアドレスを追加してください。"

#: application/templates/settings.html:271
msgid "Send Test Email"
msgstr "テストメールを送信"

#: application/templates/signup.html:38
msgid "Invitation code"
msgstr "招待コード"

#: application/templates/user_account.html:3
msgid "User account"
msgstr "ユーザーアカウント"

#: application/templates/user_account.html:42
msgid "Never expire"
msgstr "期限なし"

#: application/templates/webmail.html:3
msgid "Webmail"
msgstr "ウェブメール"

#: application/templates/webmail.html:17
msgid "Refresh"
msgstr "更新"

#: application/templates/webmail.html:18
msgid "Read/Unread"
msgstr "既読/未読"

#: application/templates/webmail.html:19
msgid "Reply"
msgstr "返信"

#: application/templates/webmail.html:20
msgid "Forward"
msgstr "転送"

#: application/templates/webmail.html:21
msgid "Fwd as Attach"
msgstr "添付ファイルとして転送"

#: application/templates/word_lookup.html:71
msgid "Word"
msgstr "単語"

#: application/view/admin.py:48 application/view/adv.py:437
#: application/view/adv.py:528 application/view/settings.py:67
#: application/view/translator.py:88 application/view/translator.py:172
#: application/view/translator.py:254
msgid "Settings Saved!"
msgstr "設定が保存されました！"

#: application/view/admin.py:57 application/view/admin.py:64
#: application/view/admin.py:91
msgid "Add account"
msgstr "アカウントを追加"

#: application/view/admin.py:63 application/view/admin.py:103
#: application/view/admin.py:135
msgid "You do not have sufficient privileges."
msgstr "十分な権限がありません。"

#: application/view/admin.py:79 application/view/login.py:44
#: application/view/login.py:222
msgid "The username includes unsafe chars."
msgstr "ユーザー名に安全でない文字が含まれています。"

#: application/view/admin.py:83 application/view/login.py:224
msgid "Already exist the username."
msgstr "そのユーザー名は既に存在します。"

#: application/view/admin.py:88
msgid "The password includes non-ascii chars."
msgstr "パスワードに非ASCII文字が含まれています。"

#: application/view/admin.py:107 application/view/admin.py:132
#: application/view/admin.py:163 application/view/extension.py:32
#: application/view/extension.py:61
msgid "The username '{}' does not exist."
msgstr "ユーザー名 '{}' は存在しません。"

#: application/view/admin.py:123
msgid "The password will not be changed if the fields are empties."
msgstr "フィールドが空であれば、パスワードは変更されません。"

#: application/view/admin.py:130 application/view/admin.py:184
msgid "Change"
msgstr "変更"

#: application/view/admin.py:181
msgid "Change success."
msgstr "変更が成功しました。"

#: application/view/admin.py:194
msgid "The old password is wrong."
msgstr "旧パスワードが間違っています。"

#: application/view/admin.py:196
msgid "Changes saved successfully."
msgstr "変更が正常に保存されました。"

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108 application/view/adv.py:109
#: application/view/adv.py:110 application/view/adv.py:111
#: application/view/adv.py:112 application/view/adv.py:113
msgid "Append hyperlink '{}' to article"
msgstr "記事にハイパーリンク '{}' を追加"

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108
msgid "Save to {}"
msgstr "{} に保存"

#: application/view/adv.py:104
msgid "evernote"
msgstr "evernote"

#: application/view/adv.py:105
msgid "wiz"
msgstr "wiz"

#: application/view/adv.py:106
msgid "pocket"
msgstr "pocket"

#: application/view/adv.py:107
msgid "instapaper"
msgstr "instapaper"

#: application/view/adv.py:108
msgid "wallabag"
msgstr "wallabag"

#: application/view/adv.py:109 application/view/adv.py:110
#: application/view/adv.py:111 application/view/adv.py:112
msgid "Share on {}"
msgstr "{} で共有"

#: application/view/adv.py:109
msgid "weibo"
msgstr "微博"

#: application/view/adv.py:110
msgid "facebook"
msgstr "facebook"

#: application/view/adv.py:112
msgid "tumblr"
msgstr "tumblr"

#: application/view/adv.py:113
msgid "Open in browser"
msgstr "ブラウザで開く"

#: application/view/adv.py:114
msgid "Append qrcode of url to article"
msgstr "記事にURLのQRコードを追加"

#: application/view/adv.py:381 application/view/share.py:54
#: application/view/subscribe.py:250
msgid "Unknown command: {}"
msgstr "不明なコマンド: {}"

#: application/view/adv.py:439 application/view/adv.py:530
msgid "The format is invalid."
msgstr "形式が無効です。"

#: application/view/adv.py:562
msgid "Authorization Error!<br/>{}"
msgstr "認証エラー!<br/>{}"

#: application/view/adv.py:583
msgid "Success authorized by Pocket!"
msgstr "Pocketによる認証に成功しました！"

#: application/view/adv.py:589
msgid ""
"Failed to request authorization of Pocket!<hr/>See details "
"below:<br/><br/>{}"
msgstr "Pocketの認証リクエストに失敗しました！<hr/>詳細は以下をご覧ください：<br/><br/>{}"

#: application/view/adv.py:610
msgid "The Instapaper service encountered an error. Please try again later."
msgstr "Instapaperサービスでエラーが発生しました。後ほど再試行してください。"

#: application/view/adv.py:623
msgid "Request type [{}] unsupported"
msgstr "リクエストタイプ [{}] はサポートされていません"

#: application/view/deliver.py:82 application/view/login.py:169
#: application/view/share.py:41
msgid "The username does not exist or the email is empty."
msgstr "ユーザー名が存在しないか、メールアドレスが空です。"

#: application/view/deliver.py:109
msgid "The following recipes has been added to the push queue."
msgstr "次のレシピがプッシュキューに追加されました。"

#: application/view/deliver.py:112
msgid "There are no recipes to deliver."
msgstr "配信するレシピはありません。"

#: application/view/extension.py:69
msgid "The rules parameter is invalid."
msgstr "ルールのパラメータが無効です。"

#: application/view/library.py:32
msgid "Cannot fetch data from {}, status: {}"
msgstr "{} からデータを取得できません、ステータス: {}"

#: application/view/library.py:48 application/view/subscribe.py:238
#: application/view/subscribe.py:380 application/view/subscribe.py:409
#: application/view/subscribe.py:416 application/view/translator.py:30
msgid "The recipe does not exist."
msgstr "レシピは存在しません。"

#: application/view/login.py:27 application/view/login.py:76
msgid "Please use {}/{} to login at first time."
msgstr "初回ログインには {}/{} を使用してください。"

#: application/view/login.py:40
msgid "Username is empty."
msgstr "ユーザー名が空です。"

#: application/view/login.py:42
msgid "The len of username reached the limit of 25 chars."
msgstr "ユーザー名の長さが25文字の制限に達しました。"

#: application/view/login.py:80
msgid "Forgot password?"
msgstr "パスワードを忘れましたか？"

#: application/view/login.py:148 application/view/login.py:275
msgid "The token is wrong or expired."
msgstr "トークンが間違っているか期限切れです。"

#: application/view/login.py:151
msgid "Please input the correct username and email to reset password."
msgstr "パスワードをリセットするには、正しいユーザー名とメールアドレスを入力してください。"

#: application/view/login.py:153
msgid "The email of account '{name}' is {email}."
msgstr "アカウント '{name}' のメールアドレスは {email} です。"

#: application/view/login.py:174
msgid "Reset password success, Please close this page and login again."
msgstr "パスワードのリセットに成功しました。このページを閉じて、再度ログインしてください。"

#: application/view/login.py:177
msgid "The email you input is not associated with this account."
msgstr "入力されたメールアドレスはこのアカウントに関連付けられていません。"

#: application/view/login.py:186
msgid "The link to reset your password has been sent to your email."
msgstr "パスワードをリセットするためのリンクがメールで送信されました。"

#: application/view/login.py:187
msgid "Please check your email inbox within 24 hours."
msgstr "24時間以内にメールボックスを確認してください。"

#: application/view/login.py:218
msgid "The invitation code is invalid."
msgstr "招待コードは無効です。"

#: application/view/login.py:226
msgid ""
"Failed to create an account. Please contact the administrator for "
"assistance."
msgstr "アカウントの作成に失敗しました。管理者にお問い合わせください。"

#: application/view/login.py:236
msgid "Successfully created account."
msgstr "アカウントが正常に作成されました。"

#: application/view/login.py:247
msgid "Reset KindleEar password"
msgstr "KindleEarのパスワードをリセット"

#: application/view/login.py:248
msgid "This is an automated email. Please do not reply to it."
msgstr "これは自動送信されたメールです。返信しないでください。"

#: application/view/login.py:249
msgid "You can click the following link to reset your KindleEar password."
msgstr "以下のリンクをクリックしてKindleEarのパスワードをリセットできます。"

#: application/view/reader.py:88
msgid "The article is missing?"
msgstr "記事が見つかりませんか？"

#: application/view/reader.py:190 application/view/translator.py:121
#: application/view/translator.py:205 application/view/translator.py:287
msgid "The text is empty."
msgstr "テキストが空です。"

#: application/view/reader.py:239
msgid "No definitions found for '{}'."
msgstr "'{}' の定義が見つかりません。"

#: application/view/reader.py:240
msgid "Did you mean?"
msgstr "意味しましたか？"

#: application/view/reader.py:324 application/view/reader.py:331
msgid "Failed to push: {}"
msgstr "プッシュに失敗しました: {}"

#: application/view/reader.py:379
msgid "Failed to create ebook."
msgstr "電子書籍の作成に失敗しました。"

#: application/view/settings.py:131
msgid ""
"You have not yet set up your email address. Please go to the 'Account' page "
"to add your email address firstly."
msgstr "まだメールアドレスを設定していません。まず「アカウント」ページに移動して、メールアドレスを追加してください。"

#: application/view/settings.py:215
msgid "English"
msgstr "英語"

#: application/view/settings.py:216
msgid "Simplified Chinese"
msgstr "简体中文"

#: application/view/settings.py:217
msgid "Traditional Chinese"
msgstr "繁體中文"

#: application/view/settings.py:218
msgid "French"
msgstr "フランス語"

#: application/view/settings.py:219
msgid "Spanish"
msgstr "スペイン語"

#: application/view/settings.py:220
msgid "Portuguese"
msgstr "ポルトガル語"

#: application/view/settings.py:221
msgid "German"
msgstr "ドイツ語"

#: application/view/settings.py:222
msgid "Italian"
msgstr "イタリア語"

#: application/view/settings.py:223
msgid "Japanese"
msgstr "日本語"

#: application/view/settings.py:224
msgid "Russian"
msgstr "ロシア語"

#: application/view/settings.py:225
msgid "Turkish"
msgstr "トルコ語"

#: application/view/settings.py:226
msgid "Korean"
msgstr "韓国語"

#: application/view/settings.py:227
msgid "Arabic"
msgstr "アラビア語"

#: application/view/settings.py:228
msgid "Czech"
msgstr "チェコ語"

#: application/view/settings.py:229
msgid "Dutch"
msgstr "オランダ語"

#: application/view/settings.py:230
msgid "Greek"
msgstr "ギリシャ語"

#: application/view/settings.py:231
msgid "Hindi"
msgstr "ヒンディー語"

#: application/view/settings.py:232
msgid "Malaysian"
msgstr "マレー語"

#: application/view/settings.py:233
msgid "Bengali"
msgstr "ベンガル語"

#: application/view/settings.py:234
msgid "Persian"
msgstr "ペルシャ語"

#: application/view/settings.py:235
msgid "Urdu"
msgstr "ウルドゥー語"

#: application/view/settings.py:236
msgid "Swahili"
msgstr "スワヒリ語"

#: application/view/settings.py:237
msgid "Vietnamese"
msgstr "ベトナム語"

#: application/view/settings.py:238
msgid "Punjabi"
msgstr "パンジャビ語"

#: application/view/settings.py:239
msgid "Javanese"
msgstr "ジャワ語"

#: application/view/settings.py:240
msgid "Tagalog"
msgstr "タガログ語"

#: application/view/settings.py:241
msgid "Hausa"
msgstr "ハウサ語"

#: application/view/settings.py:242
msgid "Thai"
msgstr "タイ語"

#: application/view/settings.py:243
msgid "Polish"
msgstr "ポーランド語"

#: application/view/settings.py:244
msgid "Romanian"
msgstr "ルーマニア語"

#: application/view/settings.py:245
msgid "Hungarian"
msgstr "ハンガリー語"

#: application/view/settings.py:246
msgid "Swedish"
msgstr "スウェーデン語"

#: application/view/settings.py:247
msgid "Hebrew"
msgstr "ヘブライ語"

#: application/view/settings.py:248
msgid "Norwegian"
msgstr "ノルウェー語"

#: application/view/settings.py:249
msgid "Finnish"
msgstr "フィンランド語"

#: application/view/settings.py:250
msgid "Danish"
msgstr "デンマーク語"

#: application/view/settings.py:251
msgid "Ukrainian"
msgstr "ウクライナ語"

#: application/view/settings.py:252
msgid "Tamil"
msgstr "タミル語"

#: application/view/settings.py:253
msgid "Marathi"
msgstr "マラーティー語"

#: application/view/settings.py:254
msgid "Burmese"
msgstr "ビルマ語"

#: application/view/settings.py:255
msgid "Amharic"
msgstr "アムハラ語"

#: application/view/settings.py:256
msgid "Azerbaijani"
msgstr "アゼルバイジャン語"

#: application/view/settings.py:257
msgid "Kazakh"
msgstr "カザフ語"

#: application/view/settings.py:258
msgid "Serbian"
msgstr "セルビア語"

#: application/view/settings.py:259
msgid "Croatian"
msgstr "Hrvatski"

#: application/view/settings.py:260
msgid "Slovak"
msgstr "Slovenčina"

#: application/view/settings.py:261
msgid "Bulgarian"
msgstr "Български"

#: application/view/settings.py:262
msgid "Icelandic"
msgstr "Íslenska"

#: application/view/settings.py:263
msgid "Lithuanian"
msgstr "Lietuvių"

#: application/view/settings.py:264
msgid "Latvian"
msgstr "Latviešu"

#: application/view/settings.py:265
msgid "Estonian"
msgstr "Eesti"

#: application/view/settings.py:266
msgid "Macedonian"
msgstr "Македонски"

#: application/view/settings.py:267
msgid "Albanian"
msgstr "Shqip"

#: application/view/settings.py:268
msgid "Galician"
msgstr "Galego"

#: application/view/settings.py:269
msgid "Welsh"
msgstr "Cymraeg"

#: application/view/settings.py:270
msgid "Basque"
msgstr "Euskara"

#: application/view/settings.py:271
msgid "Nepali"
msgstr "नेपाली"

#: application/view/share.py:60
msgid "There is no {} email yet."
msgstr "{}のメールアドレスはまだ存在しません。"

#: application/view/share.py:108 application/view/share.py:133
#: application/view/share.py:155 application/view/share.py:177
msgid "Saved to your {} account."
msgstr "{}アカウントに保存されました。"

#: application/view/share.py:111 application/view/share.py:129
#: application/view/share.py:158 application/view/share.py:179
msgid "Failed save to {}."
msgstr "{}への保存に失敗しました。"

#: application/view/share.py:112 application/view/share.py:130
#: application/view/share.py:159 application/view/share.py:180
msgid "Reason :"
msgstr "理由："

#: application/view/share.py:121
msgid "Unauthorized {} account!"
msgstr "{}アカウントは認証されていません！"

#: application/view/share.py:134
msgid "See details below:"
msgstr "詳細は以下をご覧ください："

#: application/view/share.py:157
msgid "Unknown: {}"
msgstr "不明: {}"

#: application/view/subscribe.py:81 application/view/subscribe.py:167
msgid "Duplicated subscription!"
msgstr "重複したサブスクリプション！"

#: application/view/subscribe.py:126
msgid "The Title or Url is empty."
msgstr "タイトルまたはURLが空です。"

#: application/view/subscribe.py:139
msgid "Failed to fetch the recipe."
msgstr "レシピの取得に失敗しました。"

#: application/view/subscribe.py:153 application/view/subscribe.py:331
msgid "Failed to save the recipe. Error:"
msgstr "レシピの保存に失敗しました。エラー："

#: application/view/subscribe.py:195
msgid "The Rss does not exist."
msgstr "RSSは存在しません。"

#: application/view/subscribe.py:278
msgid "You can only delete the uploaded recipe."
msgstr "アップロードされたレシピのみ削除できます。"

#: application/view/subscribe.py:283
msgid "The recipe have been subscribed, please unsubscribe it before delete."
msgstr "このレシピはすでにサブスクライブされています。削除する前に解除してください。"

#: application/view/subscribe.py:304 application/view/translator.py:51
#: application/view/translator.py:104 application/view/translator.py:117
#: application/view/translator.py:140 application/view/translator.py:188
#: application/view/translator.py:201 application/view/translator.py:230
#: application/view/translator.py:270 application/view/translator.py:283
msgid "This recipe has not been subscribed to yet."
msgstr "このレシピはまだサブスクライブされていません。"

#: application/view/subscribe.py:318
msgid "Can not read uploaded file, Error:"
msgstr "アップロードされたファイルを読み取れません。エラー："

#: application/view/subscribe.py:326
msgid ""
"Failed to decode the recipe. Please ensure that your recipe is saved in "
"utf-8 encoding."
msgstr "レシピのデコードに失敗しました。レシピがUTF-8エンコーディングで保存されていることを確認してください。"

#: application/view/subscribe.py:349
msgid "Cannot find any subclass of BasicNewsRecipe."
msgstr "BasicNewsRecipeのサブクラスが見つかりません。"

#: application/view/subscribe.py:354
msgid "The recipe is already in the library."
msgstr "このレシピはすでにライブラリにあります。"

#: application/view/subscribe.py:387
msgid "The login information for this recipe has been cleared."
msgstr "このレシピのログイン情報は削除されました。"

#: application/view/subscribe.py:391
msgid "The login information for this recipe has been saved."
msgstr "このレシピのログイン情報は保存されました。"

#: application/view/translator.py:81 application/view/translator.py:165
msgid "The api key is required."
msgstr "APIキーが必要です。"
