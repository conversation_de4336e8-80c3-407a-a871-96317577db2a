{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Shared") }} - KindleEar</title>
{% endblock -%}
{% block css -%}
<style type="text/css">
.submenu-box {
  padding: .5em 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.submenu-box .pure-menu-selected {
  font-weight: bold;
  color: #0078e7;
}

.submenu-box span {
  display: block;
  padding: .5em 1em;
  cursor: default;
}

@media (min-width: 768px) {
  .submenu-box {
    margin-right: 20px;
  }
}
</style>
{% endblock -%}

{% block content -%}
<div class="main">
  <div id="library_tips">
    {% if tips %}
    <div class="notice-box">{{tips|safe}}</div>
    {% endif %}
  </div>
  <div class="pure-g">
    <div class="pure-u-1 pure-u-md-12-24">
      <div class="pure-form pure-form-aligned">
        <select class="pure-input-rounded width60" id="shared_rss_lang_pick">
          <!-- 由脚本填充 -->
        </select>
      </div>
    </div>
    <div class="pure-u-1 pure-u-md-12-24">
      <div class="pure-form pure-form-aligned">
        <input type="text" id="search_text" onkeyup="DoSearchInShared()" class="pure-input-rounded pure-input-rounded width100" placeholder="{{_('Search')}}" />
        <p></p>
      </div>
    </div>
  </div>
  <div class="pure-g">
    <div class="pure-u-1 pure-u-md-7-24">
      <div class="pure-menu">
        <ul class="pure-menu-list submenu-box" id="ul-category-menu">
        </ul>
      </div>
    </div>
    <div class="pure-u-1 pure-u-md-17-24" id="librarycontent">
      <!-- 由脚本填充 -->
    </div>
  </div>
  <div id="toast" class="toast">
    <i>&#10003; {{_('This feed has been successfully subscribed.')}}</i>
  </div>
</div>
{% endblock -%}

{% block jsfiles %}
<script type="text/javascript" src="/static/library.js" charset="utf-8"></script>
{% endblock %}
