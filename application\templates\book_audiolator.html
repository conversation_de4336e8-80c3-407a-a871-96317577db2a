{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Text to Speech") }} - KindleEar</title>
{% endblock -%}
{% set language = params.get('language', '') %}
{% set voice = params.get('voice', '') %}
{% set region = params.get('region', '') %}
{% set api_host = params.get('api_host', '') %}
{% set api_key = params.get('api_key', '') %}
{% set rate = params.get('rate', '') %}
{% set volume = params.get('volume', '') %}
{% set pitch = params.get('pitch', '') %}
{% block content -%}
<div class="main">
  <form class="pure-form pure-form-aligned" action="" method="POST">
    {% if tips -%}
    <div class="notice-box error">{{tips|safe}}</div>
    {% endif -%}
    <fieldset>
      <legend>{{_("Text to Speech")}} [{{title}}]</legend>
      <div class="pure-control-group">
        <label>{{_("State")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="enable" id="tts_state">
          <option value="both" {% if params.get('enable')=='both' %}selected="selected"{% endif %}>{{_('Send Ebook and Audio')}}</option>
          <option value="audio_only" {% if params.get('enable')=='audio_only' %}selected="selected"{% endif %}>{{_('Send Audio only')}}</option>
          <option value="" {% if not params.get('enable') %}selected="selected"{% endif %}>{{_('Disable TTS')}}</option>
        </select>
      </div>
      <div class="pure-control-group" id="tts_send_to">
        <label>{{_("Send Audio To")}}</label>
        <input type="text" name="send_to" id="tts_send_to_input" value="{{params.get('send_to', '')}}" class="pure-u-1 pure-u-sm-1-2" placeholder="{{_('Empty to use Kindle_email')}}" />
      </div>
      <div class="pure-control-group">
        <label><a id="tts_engine_a" href="" target="_blank" style="color:rgb(0,0,0,0.8)">
        {{_("TTS Engine")}}</a></label>
        <select class="pure-u-1 pure-u-sm-1-2" name="engine" id="tts_engine" onchange="TTSEngineFieldChanged('{{language}}', '{{region}}');TTSLanguageFieldChanged('{{voice}}')">
          <!-- 由脚本填充 -->
        </select>
      </div>
      <div class="pure-control-group" id="tts_api_host">
        <label>{{_("Api Host")}}</label>
        <input type="text" name="api_host" id="tts_api_host_input" value="{{api_host}}" placeholder="{{_('Leave empty to use default')}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group" id="tts_region_div">
        <label>
          <a id="tts_region_a" href="" target="_blank" style="color:rgb(0,0,0,0.8)">{{_("Region")}}</a>
        </label>
        <select class="pure-u-1 pure-u-sm-1-2" name="region" id="tts_region_sel">
          <!-- 由脚本填充 -->
        </select>
      </div>
      <div class="pure-control-group" id="tts_api_key">
        <label>{{_("Api Key")}}</label>
        <input type="text" name="api_key" id="tts_api_key_input" value="{{api_key}}" class="pure-u-1 pure-u-sm-1-2" />
      </div>
      <div class="pure-control-group">
        <label><a id="tts_language_a" href="" target="_blank" style="color:rgb(0,0,0,0.8)">
        {{_("Language")}}</a></label>
        <select class="pure-u-1 pure-u-sm-1-2" name="language" id="tts_language_sel" onchange="TTSLanguageFieldChanged('{{voice}}')">
          <!-- 由脚本填充 -->
        </select>
      </div>
      <div class="pure-control-group" id="tts_voice_div">
        <label>
          <a id="tts_voice_a" href="" target="_blank" style="color:rgb(0,0,0,0.8)">
          {{_("Voice name")}}</a>
        </label>
        <select class="pure-u-1 pure-u-sm-1-2" name="voice" id="tts_voice_sel">
          <!-- 由脚本填充 -->
        </select>
      </div>
      <div class="pure-control-group">
        <label>{{_("Voice speed")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="rate" id="tts_rate">
          <option value="x-slow" {% if rate=='x-slow' %}selected="selected"{% endif %}>{{_('Extra slow')}}</option>
          <option value="slow" {% if rate=='slow' %}selected="selected"{% endif %}>{{_('Slow')}}</option>
          <option value="medium" {% if not rate or rate=='medium' %}selected="selected"{% endif %}>{{_('Medium')}}</option>
          <option value="fast" {% if rate=='fast' %}selected="selected"{% endif %}>{{_('Fast')}}</option>
          <option value="x-fast" {% if rate=='x-fast' %}selected="selected"{% endif %}>{{_('Extra fast')}}</option>
        </select>
      </div>
      <div class="pure-control-group" id="tts_style">
        <label>{{_("Voice pitch")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="pitch" id="tts_pitch">
          <option value="x-low" {% if pitch=='x-low' %}selected="selected"{% endif %}>{{_('Extra low')}}</option>
          <option value="low" {% if pitch=='low' %}selected="selected"{% endif %}>{{_('Low')}}</option>
          <option value="medium" {% if not pitch or pitch=='medium' %}selected="selected"{% endif %}>{{_('Medium')}}</option>
          <option value="high" {% if pitch=='high' %}selected="selected"{% endif %}>{{_('High')}}</option>
          <option value="x-high" {% if pitch=='x-high' %}selected="selected"{% endif %}>{{_('Extra high')}}</option>
        </select>
      </div>
      <div class="pure-control-group" id="tts_style">
        <label>{{_("Voice volume")}}</label>
        <select class="pure-u-1 pure-u-sm-1-2" name="volume" id="tts_volume">
          <option value="x-soft" {% if volume=='x-soft' %}selected="selected"{% endif %}>{{_('Extra soft')}}</option>
          <option value="soft" {% if volume=='soft' %}selected="selected"{% endif %}>{{_('Soft')}}</option>
          <option value="medium" {% if not volume or volume=='medium' %}selected="selected"{% endif %}>{{_('Medium')}}</option>
          <option value="loud" {% if volume=='loud' %}selected="selected"{% endif %}>{{_('Loud')}}</option>
          <option value="x-loud" {% if volume=='x-loud' %}selected="selected"{% endif %}>{{_('Extra loud')}}</option>
        </select>
      </div>
      <br/>
      <label>
        <input type="checkbox" name="apply_all" />
        {{_("Apply to all subscribed recipes")}}
      </label>
      <input type="hidden" value="{{recipeId}}" name="recipeId" id="recipeId">
    </fieldset>
    <p style="text-align:center;color:red;margin:20px 0px 30px 0px;border:solid 1px silver;">
      {{_("Note: Enabling this feature will significantly increase consumed CPU instance hours.")}}
    </p>
    <p style="text-align:center;">
      <input type="submit" class="pure-button pure-button-primary pure-input-rounded" value="{{_('Save settings')}}" />
    </p>
  </form>
  <hr/>
  <form class="pure-form pure-form-aligned" action="" method="POST">
    <fieldset>
      <legend>{{_("Test (Please save settings firstly)")}}</legend>
      <div class="pure-control-group">
        <label>{{_("Text")}}</label>
        <textarea class="pure-u-1 pure-u-sm-1-2" name="tts_test_src_text" id="tts_test_src_text" rows="4">{{famous}}</textarea>
      </div>
      <div class="pure-control-group">
        <label></label>
        <audio controls id="tts_audio_player">
          {{_("Your browser does not support the audio element.")}}
        </audio>
      </div>
    </fieldset>
    <p style="text-align:center;">
      <input type="button" class="pure-button pure-button-primary pure-input-rounded" value="{{_('Test')}}" onclick="TestTTS('{{recipeId}}')" />
    </p>
  </form>
</div>
{% endblock -%}

{% block js -%}
<script type="text/javascript">
var g_tts_engines = {{engines|safe}};

$(document).ready(function() {
  PopulateTTSFields("{{params['engine']}}");
  TTSEngineFieldChanged('{{language}}', '{{region}}');
  TTSLanguageFieldChanged('{{voice}}');
});
</script>
{% endblock -%}
