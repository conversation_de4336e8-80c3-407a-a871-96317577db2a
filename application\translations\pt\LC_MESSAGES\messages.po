# Chinese translations for KindleEar.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the KindleEar project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: KindleEar v3.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-10 19:49-0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: KindleEar <<EMAIL>>\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.14.0\n"

#: application/templates/admin.html:3 application/templates/base.html:53
#: application/templates/base.html:192 application/templates/settings.html:263
msgid "Account"
msgstr "Conta"

#: application/templates/admin.html:19
msgid "Signup settings"
msgstr "Configurações de cadastro"

#: application/templates/admin.html:19
#: application/templates/adv_calibre_options.html:18
#: application/templates/adv_proxy.html:18
msgid "Save"
msgstr "Salvar"

#: application/templates/admin.html:21
#: application/templates/user_account.html:31
msgid "Email service"
msgstr "Serviço de e-mail"

#: application/templates/admin.html:23
#: application/templates/user_account.html:34
msgid "Same as admin"
msgstr "Mesmo que o administrador"

#: application/templates/admin.html:24
#: application/templates/user_account.html:35
msgid "Independent"
msgstr "Independente"

#: application/templates/admin.html:28
msgid "Signup type"
msgstr "Tipo de cadastro"

#: application/templates/admin.html:30
msgid "Public"
msgstr "Público"

#: application/templates/admin.html:31
msgid "One time code"
msgstr "Código único"

#: application/templates/admin.html:32
msgid "Permanent code"
msgstr "Código permanente"

#: application/templates/admin.html:36
msgid "Invitation codes"
msgstr "Códigos de convite"

#: application/templates/admin.html:37
msgid "one code per line"
msgstr "um código por linha"

#: application/templates/admin.html:43
msgid "Accounts"
msgstr "Contas"

#: application/templates/admin.html:43
#: application/templates/adv_inboundmail.html:61
#: application/templates/my.html:34 application/view/admin.py:57
#: application/view/admin.py:64 application/view/admin.py:91
msgid "Add"
msgstr "Adicionar"

#: application/templates/admin.html:54
#: application/templates/adv_archive.html:69
#: application/templates/home.html:19 application/templates/login.html:24
#: application/templates/logs.html:70
#: application/templates/reset_password.html:19
#: application/templates/reset_password.html:20
#: application/templates/settings.html:241
#: application/templates/signup.html:21
#: application/templates/user_account.html:15
msgid "Username"
msgstr "Nome de usuário"

#: application/templates/admin.html:55
msgid "AutoSend"
msgstr "Envio automático"

#: application/templates/admin.html:56
#: application/templates/change_password.html:27
#: application/templates/reset_password.html:26
#: application/templates/signup.html:33
#: application/templates/user_account.html:27
msgid "Email"
msgstr "Email"

#: application/templates/admin.html:57
#: application/templates/user_account.html:39
msgid "Expiration"
msgstr "Expiração"

#: application/templates/admin.html:58
msgid "Operation"
msgstr "Operação"

#: application/templates/admin.html:65
msgid "Yes"
msgstr "Sim"

#: application/templates/admin.html:65
msgid "No"
msgstr "Não"

#: application/templates/admin.html:68
msgid "Never"
msgstr "Nunca"

#: application/templates/admin.html:70
#: application/templates/adv_inboundmail.html:26
#: application/templates/settings.html:58
#: application/templates/settings.html:181
#: application/templates/user_account.html:43
msgid "7 Days"
msgstr "7 Dias"

#: application/templates/admin.html:72
#: application/templates/user_account.html:44
msgid "1 Month"
msgstr "1 Mês"

#: application/templates/admin.html:74
#: application/templates/user_account.html:45
msgid "3 Months"
msgstr "3 Meses"

#: application/templates/admin.html:76
#: application/templates/user_account.html:46
msgid "6 Months"
msgstr "6 Meses"

#: application/templates/admin.html:78
#: application/templates/user_account.html:47
msgid "1 Year"
msgstr "1 Ano"

#: application/templates/admin.html:80
#: application/templates/user_account.html:48
msgid "2 Years"
msgstr "2 Anos"

#: application/templates/admin.html:85
#: application/templates/change_password.html:3
#: application/templates/change_password.html:13 application/view/admin.py:129
#: application/view/admin.py:183
msgid "Edit account"
msgstr "Editar conta"

#: application/templates/admin.html:89
#: application/templates/adv_inboundmail.html:54
#: application/templates/adv_uploadcss.html:31
#: application/templates/base.html:25 application/templates/webmail.html:22
msgid "Delete"
msgstr "Excluir"

#: application/templates/adv_archive.html:3
#: application/templates/adv_archive.html:14
#: application/templates/adv_base.html:57
#: application/templates/adv_base.html:61
msgid "Archive"
msgstr "Arquivar"

#: application/templates/adv_archive.html:15
msgid "Append hyperlinks for archiving or sharing."
msgstr "Anexar links para arquivamento ou compartilhamento."

#: application/templates/adv_archive.html:40
msgid "Authorized"
msgstr "Autorizado"

#: application/templates/adv_archive.html:42
msgid "Authorize"
msgstr "Autorizar"

#: application/templates/adv_archive.html:53
msgid "Email or Username"
msgstr "E-mail ou nome de usuário"

#: application/templates/adv_archive.html:56
#: application/templates/adv_archive.html:72
#: application/templates/base.html:54 application/templates/home.html:20
#: application/templates/login.html:28 application/templates/settings.html:245
#: application/templates/signup.html:25
#: application/templates/user_account.html:19
msgid "Password"
msgstr "Senha"

#: application/templates/adv_archive.html:59
#: application/templates/adv_archive.html:75
#: application/templates/base.html:64
msgid "Verify"
msgstr "Verificar"

#: application/templates/adv_archive.html:78
msgid "client_id"
msgstr "client_id"

#: application/templates/adv_archive.html:81
msgid "client_secret"
msgstr "client_secret"

#: application/templates/adv_archive.html:84
#: application/templates/settings.html:233
msgid "Host"
msgstr "Hospedeiro"

#: application/templates/adv_archive.html:119
#: application/templates/adv_dict.html:79
#: application/templates/adv_inboundmail.html:34
#: application/templates/book_audiolator.html:113
#: application/templates/book_summarizer.html:88
#: application/templates/book_translator.html:80
#: application/templates/settings.html:269
msgid "Save settings"
msgstr "Salvar configurações"

#: application/templates/adv_base.html:39
#: application/templates/adv_base.html:43
#: application/templates/adv_delivernow.html:8
msgid "Deliver Now"
msgstr "Entregar agora"

#: application/templates/adv_base.html:48
#: application/templates/adv_base.html:52
#: application/templates/adv_inboundmail.html:3
#: application/templates/adv_inboundmail.html:9
#: application/templates/adv_inboundmail.html:14
msgid "Inbound Mail"
msgstr "Correio de entrada"

#: application/templates/adv_base.html:66
#: application/templates/adv_base.html:70
#: application/templates/adv_dict.html:3
#: application/templates/adv_dict.html:13
#: application/templates/reader.html:134
msgid "Dictionary"
msgstr "Dicionário"

#: application/templates/adv_base.html:75
#: application/templates/adv_base.html:83
#: application/templates/adv_proxy.html:3
#: application/templates/adv_proxy.html:12
msgid "Proxy"
msgstr "Proxy"

#: application/templates/adv_base.html:92
#: application/templates/adv_base.html:96
#: application/templates/adv_import.html:8
msgid "Import Feeds"
msgstr "Importar feeds"

#: application/templates/adv_base.html:101
#: application/templates/adv_base.html:105
msgid "Cover Image"
msgstr "Imagem de capa"

#: application/templates/adv_base.html:110
#: application/templates/adv_base.html:114
#: application/templates/adv_uploadcss.html:3
msgid "Stylesheet"
msgstr "Folha de estilo"

#: application/templates/adv_base.html:119
#: application/templates/adv_base.html:123
#: application/templates/adv_calibre_options.html:3
#: application/templates/adv_calibre_options.html:12
msgid "Calibre Options"
msgstr "Opções do Calibre"

#: application/templates/adv_calibre_options.html:13
msgid "Set the parameters for Calibre, in JSON dictionary format."
msgstr "Defina os parâmetros para o Calibre, no formato de dicionário JSON."

#: application/templates/adv_delivernow.html:3
msgid "Deliver now"
msgstr "Entregar agora"

#: application/templates/adv_delivernow.html:9
msgid "Deliver selected recipes now."
msgstr "Entregar receitas selecionadas agora."

#: application/templates/adv_delivernow.html:12
msgid "There are no recipes subscribed"
msgstr "Não há receitas assinadas"

#: application/templates/adv_delivernow.html:17
#: application/templates/base.html:101
msgid "Sep"
msgstr "Sep"

#: application/templates/adv_delivernow.html:22
msgid "Select all"
msgstr "Selecionar tudo"

#: application/templates/adv_delivernow.html:23
msgid "Select none"
msgstr "Selecionar nenhum"

#: application/templates/adv_delivernow.html:28
msgid "Deliver"
msgstr "Entregar"

#: application/templates/adv_dict.html:15
msgid "Set up dictionaries for online reading."
msgstr "Configurar dicionários para leitura online."

#: application/templates/adv_dict.html:18
#: application/templates/adv_dict.html:40
#: application/templates/adv_dict.html:62
msgid "Book language"
msgstr "Idioma do livro"

#: application/templates/adv_dict.html:27
#: application/templates/adv_dict.html:49
#: application/templates/adv_dict.html:66
#: application/templates/book_summarizer.html:23
#: application/templates/book_translator.html:26
#: application/templates/word_lookup.html:59
msgid "Engine"
msgstr "Motor"

#: application/templates/adv_dict.html:33
#: application/templates/adv_dict.html:55
#: application/templates/adv_dict.html:72
#: application/templates/word_lookup.html:65
msgid "Database"
msgstr "Banco de dados"

#: application/templates/adv_dict.html:63
msgid "Other languages"
msgstr "Outros idiomas"

#: application/templates/adv_dict.html:81
#: application/templates/word_lookup.html:3
#: application/templates/word_lookup.html:79
msgid "Word lookup"
msgstr "Busca de palavras"

#: application/templates/adv_dict.html:86 application/view/reader.py:29
#: application/view/reader.py:86
msgid "Online reading feature has not been activated yet."
msgstr "A funcionalidade de leitura online ainda não foi ativada."

#: application/templates/adv_import.html:3
#: application/templates/adv_import.html:19
msgid "Import"
msgstr "Importar"

#: application/templates/adv_import.html:9
msgid "Import custom rss from an OPML file."
msgstr "Importar RSS personalizado de um arquivo OPML."

#: application/templates/adv_import.html:15
msgid "Import as fulltext rss by default"
msgstr "Importar como RSS de texto completo por padrão"

#: application/templates/adv_import.html:20
msgid "Download"
msgstr "Baixar"

#: application/templates/adv_inboundmail.html:11
msgid ""
"To enable the inbound email feature, you also need to configure the "
"whitelist."
msgstr ""
"Para ativar a funcionalidade de e-mail de entrada, você também precisa "
"configurar a lista branca."

#: application/templates/adv_inboundmail.html:16
#: application/templates/adv_uploadcover.html:15
#: application/templates/book_summarizer.html:19
#: application/templates/book_translator.html:22
#: application/templates/settings.html:143
msgid "Disable"
msgstr "Desativar"

#: application/templates/adv_inboundmail.html:17
msgid "Forward Only"
msgstr "Somente encaminhar"

#: application/templates/adv_inboundmail.html:18
msgid "Save Only"
msgstr "Somente salvar"

#: application/templates/adv_inboundmail.html:19
msgid "Save and Forward"
msgstr "Salvar e encaminhar"

#: application/templates/adv_inboundmail.html:23
msgid "Email Retention"
msgstr "Retenção de e-mail"

#: application/templates/adv_inboundmail.html:25
#: application/templates/settings.html:52
#: application/templates/settings.html:175
msgid "1 Day"
msgstr "1 Dia"

#: application/templates/adv_inboundmail.html:27
#: application/templates/settings.html:59
msgid "30 Days"
msgstr "30 Dias"

#: application/templates/adv_inboundmail.html:28
#: application/templates/settings.html:51
#: application/templates/settings.html:174
msgid "No limit"
msgstr "Sem limite"

#: application/templates/adv_inboundmail.html:36
msgid "Open webmail"
msgstr "Abrir webmail"

#: application/templates/adv_inboundmail.html:44
msgid "White List"
msgstr "Lista branca"

#: application/templates/adv_inboundmail.html:46
#, python-format
msgid ""
"Emails sent to %(name)sxxx@%(mailHost)s will be forwarded to your kindle "
"email."
msgstr ""
"Os e-mails enviados para %(name)sxxx@%(mailHost)s serão encaminhados para "
"seu e-mail Kindle."

#: application/templates/adv_inboundmail.html:47
msgid "Example"
msgstr "Exemplo"

#: application/templates/adv_inboundmail.html:59
msgid "Please input mail address"
msgstr "Por favor, insira o endereço de e-mail"

#: application/templates/adv_proxy.html:13
msgid "Supports"
msgstr "Suporta"

#: application/templates/adv_proxy.html:24
#: application/templates/adv_proxy.html:29
#: application/templates/book_audiolator.html:132
#: application/templates/book_summarizer.html:105
#: application/templates/book_translator.html:97
msgid "Test"
msgstr "Teste"

#: application/templates/adv_uploadcover.html:3
msgid "Cover image"
msgstr "Imagem de capa"

#: application/templates/adv_uploadcover.html:9
msgid "Upload cover image"
msgstr "Carregar imagem de capa"

#: application/templates/adv_uploadcover.html:10
msgid ""
"Upload cover images from local with an aspect ratio of approximately 0.625."
msgstr ""
"Carregue imagens de capa locais com uma proporção de aspecto de "
"aproximadamente 0.625."

#: application/templates/adv_uploadcover.html:13
msgid "Include cover"
msgstr "Incluir capa"

#: application/templates/adv_uploadcover.html:16
#: application/templates/book_summarizer.html:18
#: application/templates/book_translator.html:21
msgid "Enable"
msgstr "Ativar"

#: application/templates/adv_uploadcover.html:20
msgid "Rule for cover"
msgstr "Regra para capa"

#: application/templates/adv_uploadcover.html:22
msgid "Random"
msgstr "Aleatório"

#: application/templates/adv_uploadcover.html:23
#: application/templates/base.html:127
msgid "Weekday"
msgstr "Dia da semana"

#: application/templates/adv_uploadcover.html:49
msgid "Upload/Update"
msgstr "Carregar/Atualizar"

#: application/templates/adv_uploadcss.html:22
msgid "Upload stylesheet"
msgstr "Carregar folha de estilo"

#: application/templates/adv_uploadcss.html:23
msgid "Upload a stylesheet from local (accept utf-8 only)."
msgstr "Carregar uma folha de estilo local (aceita apenas utf-8)."

#: application/templates/adv_uploadcss.html:30
msgid "Upload"
msgstr "Carregar"

#: application/templates/autoback.html:3
msgid "Auto back"
msgstr "Voltar automaticamente"

#: application/templates/autoback.html:28
msgid "Auto back to previous page after 5 seconds"
msgstr "Voltar para a página anterior automaticamente após 5 segundos"

#: application/templates/autoback.html:29
#: application/templates/tipsback.html:15
msgid "Click to back"
msgstr "Clique para voltar"

#: application/templates/base.html:24 application/templates/reader.html:189
msgid "Confirm Deletion"
msgstr "Confirmar exclusão"

#: application/templates/base.html:26
msgid "Delete (Ctrl for no confirm)"
msgstr "Excluir (Ctrl para não confirmar)"

#: application/templates/base.html:27
msgid "View Source Code"
msgstr "Ver código fonte"

#: application/templates/base.html:28
msgid "Subscribe (Deliver Separately)"
msgstr "Assinar (Entregar separadamente)"

#: application/templates/base.html:29
msgid "Subscribe"
msgstr "Assinar"

#: application/templates/base.html:30
msgid "Cannot add this custom rss, Error:"
msgstr "Não é possível adicionar este RSS personalizado, Erro:"

#: application/templates/base.html:31
msgid "Cannot delete this feed, Error:"
msgstr "Não é possível excluir este feed, Erro:"

#: application/templates/base.html:32
msgid "Fulltext"
msgstr "Texto completo"

#: application/templates/base.html:33 application/templates/base.html:43
msgid "Share"
msgstr "Compartilhar"

#: application/templates/base.html:34 application/templates/reader.html:190
msgid "Are you sure to delete?"
msgstr "Tem certeza de que deseja excluir?"

#: application/templates/base.html:35
msgid "Report to the server that this feed is invalid."
msgstr "Informe ao servidor que este feed é inválido."

#: application/templates/base.html:36
msgid "Are you sure to REMOVE ALL CUSTOM RSS?"
msgstr "Tem certeza de que deseja REMOVER TODOS OS RSS PERSONALIZADOS?"

#: application/templates/base.html:37
msgid "Share links, share happiness"
msgstr "Compartilhe links, compartilhe felicidade"

#: application/templates/base.html:38
msgid "Category"
msgstr "Categoria"

#: application/templates/base.html:39
#: application/templates/book_audiolator.html:58
#: application/templates/book_summarizer.html:43
#: application/templates/settings.html:159
msgid "Language"
msgstr "Idioma"

#: application/templates/base.html:40
msgid ""
"Please write a category in text field if the one you wish is not in the "
"list."
msgstr ""
"Por favor, escreva uma categoria no campo de texto se a desejada não estiver"
" na lista."

#: application/templates/base.html:41
msgid "Ok"
msgstr "Ok"

#: application/templates/base.html:42
msgid "Cancel"
msgstr "Cancelar"

#: application/templates/base.html:44
msgid "Language code invalid"
msgstr "Código de idioma inválido"

#: application/templates/base.html:45
msgid "Thank you for sharing."
msgstr "Obrigado por compartilhar."

#: application/templates/base.html:46 application/templates/reader.html:159
msgid "Close"
msgstr "Fechar"

#: application/templates/base.html:47
msgid "Unsubscribe"
msgstr "Cancelar inscrição"

#: application/templates/base.html:48
msgid "Cannot subscribe this recipe, Error:"
msgstr "Não é possível assinar esta receita, Erro:"

#: application/templates/base.html:49
msgid "Are you sure to Unsubscribe ({0})?"
msgstr "Tem certeza de que deseja cancelar a inscrição ({0})?"

#: application/templates/base.html:50
msgid "Cannot unsubscribe this recipe, Error:"
msgstr "Não é possível cancelar a inscrição nesta receita, Erro:"

#: application/templates/base.html:51
msgid "The recipe is already subscribed."
msgstr "A receita já foi assinada."

#: application/templates/base.html:52
msgid "Website login lnformation"
msgstr "Informações de login do site"

#: application/templates/base.html:55
msgid "Submit"
msgstr "Enviar"

#: application/templates/base.html:56
msgid ""
"If any field is left blank, the server will clear the saved login "
"information."
msgstr ""
"Se algum campo for deixado em branco, o servidor limpará as informações de "
"login salvas."

#: application/templates/base.html:57
msgid "Cannot set the website login information, Error:"
msgstr "Não é possível configurar as informações de login do site, Erro:"

#: application/templates/base.html:58 application/templates/my.html:58
msgid "Upload custom recipe"
msgstr "Carregar receita personalizada"

#: application/templates/base.html:59
msgid "Congratulations"
msgstr "Parabéns"

#: application/templates/base.html:60
msgid "Thanks"
msgstr "Obrigado"

#: application/templates/base.html:61
msgid ""
"Your recipe has been uploaded, and it can be found in the Library section. "
"If you dont see it, please make sure to switch to the correct language."
msgstr ""
"Sua receita foi carregada e pode ser encontrada na seção Biblioteca. Se você"
" não a vê, certifique-se de mudar para o idioma correto."

#: application/templates/base.html:62
msgid "Your recipe have been deleted."
msgstr "Sua receita foi excluída."

#: application/templates/base.html:63
msgid "Kindleify Selection"
msgstr "Seleção Kindleify"

#: application/templates/base.html:65
msgid "Verified"
msgstr "Verificado"

#: application/templates/base.html:66 application/view/login.py:79
#: application/view/share.py:157
msgid "The username does not exist or password is wrong."
msgstr "O nome de usuário não existe ou a senha está errada."

#: application/templates/base.html:67
msgid "The file you chosen is not an acceptable type."
msgstr "O arquivo que você escolheu não é um tipo aceitável."

#: application/templates/base.html:68
msgid "The file have been uploaded successfully."
msgstr "O arquivo foi carregado com sucesso."

#: application/templates/base.html:69 application/templates/library.html:67
msgid "This feed has been successfully subscribed."
msgstr "Este feed foi assinado com sucesso."

#: application/templates/base.html:70
msgid "Thank you for your feedback, this feed will be reviewed soon."
msgstr "Obrigado pelo seu feedback, este feed será revisado em breve."

#: application/templates/base.html:71
msgid "Are you confirming to share the recipe ({0})?"
msgstr "Você confirma compartilhar a receita ({0})?"

#: application/templates/base.html:72
msgid "[All]"
msgstr "[Todos]"

#: application/templates/base.html:73
msgid "[By Time]"
msgstr "[Por Tempo]"

#: application/templates/base.html:74
msgid "[Random]"
msgstr "[Aleatório]"

#: application/templates/base.html:75
msgid "[Uncategoried]"
msgstr "[Sem Categoria]"

#: application/templates/base.html:76
msgid "There are no links found."
msgstr "Nenhum link encontrado."

#: application/templates/base.html:77
msgid "Invalid report"
msgstr "Relatório inválido"

#: application/templates/base.html:78
msgid "Are you confirming that this link is invalid or off the cloud?"
msgstr "Você confirma que este link é inválido ou está fora da nuvem?"

#: application/templates/base.html:79
msgid "Customize delivery time"
msgstr "Personalizar horário de entrega"

#: application/templates/base.html:80 application/templates/settings.html:72
msgid "Delivery days"
msgstr "Dias de entrega"

#: application/templates/base.html:81 application/templates/settings.html:74
msgid "Mon"
msgstr "Seg"

#: application/templates/base.html:82 application/templates/settings.html:76
msgid "Tue"
msgstr "Ter"

#: application/templates/base.html:83 application/templates/settings.html:78
msgid "Wed"
msgstr "Qua"

#: application/templates/base.html:84 application/templates/settings.html:80
msgid "Thu"
msgstr "Qui"

#: application/templates/base.html:85 application/templates/settings.html:82
msgid "Fri"
msgstr "Sex"

#: application/templates/base.html:86 application/templates/settings.html:84
msgid "Sat"
msgstr "Sáb"

#: application/templates/base.html:87 application/templates/settings.html:86
msgid "Sun"
msgstr "Dom"

#: application/templates/base.html:88
msgid "Delivery times"
msgstr "Horários de entrega"

#: application/templates/base.html:89
msgid ""
"The customized delivery time for the recipe has been successfully saved."
msgstr ""
"O horário de entrega personalizado para a receita foi salvo com sucesso."

#: application/templates/base.html:90
msgid "The account have been deleted."
msgstr "A conta foi deletada."

#: application/templates/base.html:91 application/view/share.py:147
msgid "The username or password is empty."
msgstr "O nome de usuário ou a senha estão vazios."

#: application/templates/base.html:92 application/view/admin.py:81
#: application/view/admin.py:165 application/view/admin.py:191
#: application/view/login.py:220 application/view/login.py:273
msgid "The two new passwords are dismatch."
msgstr "As duas novas senhas não coincidem."

#: application/templates/base.html:93
msgid "Password changed successfully."
msgstr "Senha alterada com sucesso."

#: application/templates/base.html:94
msgid "Account added successfully."
msgstr "Conta adicionada com sucesso."

#: application/templates/base.html:95 application/view/login.py:128
msgid "login required"
msgstr "login necessário"

#: application/templates/base.html:96
msgid "Upload cover files successfully."
msgstr "Arquivos de capa carregados com sucesso."

#: application/templates/base.html:97
msgid ""
"Total size of the files you selected exceeds 16MB. Please reduce the image "
"resolution or upload in batches."
msgstr ""
"O tamanho total dos arquivos selecionados excede 16 MB. Por favor, reduza a "
"resolução da imagem ou faça o upload em lotes."

#: application/templates/base.html:98
#: application/templates/book_translator.html:3
#: application/templates/book_translator.html:17
msgid "Bilingual Translator"
msgstr "Tradutor Bilíngue"

#: application/templates/base.html:99
#: application/templates/book_summarizer.html:3
#: application/templates/book_summarizer.html:14
msgid "AI Summarizer"
msgstr "Resumidor de IA"

#: application/templates/base.html:100
msgid "Upl"
msgstr "Upl"

#: application/templates/base.html:102
msgid "Log"
msgstr "Log"

#: application/templates/base.html:103
msgid "Emb"
msgstr "Emb"

#: application/templates/base.html:104
msgid "Tr"
msgstr "Tr"

#: application/templates/base.html:105
msgid "Tts"
msgstr "Tts"

#: application/templates/base.html:106
msgid "Ai"
msgstr "Ai"

#: application/templates/base.html:107
msgid ""
"The test email has been successfully sent to the following addresses. Please"
" check your inbox or spam folder to confirm its delivery. Depending on your "
"email server, there may be a slight delay."
msgstr ""
"O e-mail de teste foi enviado com sucesso para os seguintes endereços. "
"Verifique sua caixa de entrada ou pasta de spam para confirmar a entrega. "
"Dependendo do servidor de e-mail, pode haver um pequeno atraso."

#: application/templates/base.html:108
msgid "Processing..."
msgstr "Processando..."

#: application/templates/base.html:109
msgid "The configuration validation is correct."
msgstr "A validação da configuração está correta."

#: application/templates/base.html:110 application/templates/logs.html:23
#: application/templates/logs.html:72 application/templates/my.html:17
#: application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/settings.html:155
msgid "Title"
msgstr "Título"

#: application/templates/base.html:111
#: application/templates/book_audiolator.html:3
#: application/templates/book_audiolator.html:20
msgid "Text to Speech"
msgstr "Texto para Fala"

#: application/templates/base.html:112
msgid "Action"
msgstr "Ação"

#: application/templates/base.html:113
msgid "File"
msgstr "Arquivo"

#: application/templates/base.html:114
msgid "Upload Only"
msgstr "Somente Upload"

#: application/templates/base.html:115
msgid "Send"
msgstr "Enviar"

#: application/templates/base.html:116 application/templates/logs.html:54
msgid "There is nothing here."
msgstr "Não há nada aqui."

#: application/templates/base.html:117
msgid "Please select a single item."
msgstr "Por favor, selecione um único item."

#: application/templates/base.html:118 application/templates/reader.html:191
msgid "Please select at least one item."
msgstr "Por favor, selecione pelo menos um item."

#: application/templates/base.html:119 application/view/admin.py:77
#: application/view/admin.py:152 application/view/admin.py:189
#: application/view/adv.py:459 application/view/extension.py:34
#: application/view/extension.py:63 application/view/inbound_email.py:470
#: application/view/inbound_email.py:478 application/view/inbound_email.py:493
#: application/view/inbound_email.py:514 application/view/login.py:216
#: application/view/login.py:245 application/view/reader.py:109
#: application/view/reader.py:126 application/view/share.py:37
msgid "Some parameters are missing or wrong."
msgstr "Alguns parâmetros estão faltando ou estão errados."

#: application/templates/base.html:120
msgid "The email has been sent."
msgstr "O e-mail foi enviado."

#: application/templates/base.html:121 application/templates/webmail.html:29
msgid "From"
msgstr "De"

#: application/templates/base.html:122 application/templates/logs.html:25
#: application/templates/logs.html:74 application/templates/webmail.html:30
msgid "To"
msgstr "Para"

#: application/templates/base.html:123 application/templates/webmail.html:31
msgid "Subject"
msgstr "Assunto"

#: application/templates/base.html:124 application/templates/logs.html:22
#: application/templates/logs.html:71 application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/webmail.html:32
msgid "Time"
msgstr "Hora"

#: application/templates/base.html:125 application/templates/logs.html:24
#: application/templates/logs.html:73 application/templates/webmail.html:33
msgid "Size"
msgstr "Tamanho"

#: application/templates/base.html:126
msgid "Date type"
msgstr "Tipo de data"

#: application/templates/base.html:128
msgid "Date"
msgstr "Data"

#: application/templates/base.html:129
msgid "This setting is prioritized."
msgstr "Esta configuração tem prioridade."

#: application/templates/base.html:130
msgid "Combine multiple values with commas."
msgstr "Combine múltiplos valores com vírgulas."

#: application/templates/base.html:131
msgid "Put dictionary in dict folder"
msgstr "Coloque o dicionário na pasta dict"

#: application/templates/base.html:156 application/templates/home.html:16
msgid "Logout"
msgstr "Sair"

#: application/templates/base.html:158 application/templates/home.html:21
#: application/templates/login.html:3 application/templates/login.html:22
#: application/templates/login.html:33
msgid "Login"
msgstr "Entrar"

#: application/templates/base.html:160 application/templates/signup.html:3
#: application/templates/signup.html:19 application/templates/signup.html:43
msgid "Signup"
msgstr "Cadastrar"

#: application/templates/base.html:189 application/templates/home.html:15
#: application/templates/my.html:3
msgid "Feeds"
msgstr "Feeds"

#: application/templates/base.html:190 application/templates/settings.html:3
msgid "Settings"
msgstr "Opções"

#: application/templates/base.html:191 application/templates/logs.html:3
msgid "Logs"
msgstr "Logs"

#: application/templates/base.html:193
msgid "Advanced"
msgstr "Avançado"

#: application/templates/base.html:194 application/templates/library.html:3
msgid "Shared"
msgstr "Distribuído"

#: application/templates/base.html:195 application/templates/reader.html:6
msgid "Reader"
msgstr "Leitor"

#: application/templates/book_audiolator.html:22
#: application/templates/book_summarizer.html:16
#: application/templates/book_translator.html:19
msgid "State"
msgstr "Estado"

#: application/templates/book_audiolator.html:24
msgid "Send Ebook and Audio"
msgstr "Enviar Ebook e Áudio"

#: application/templates/book_audiolator.html:25
msgid "Send Audio only"
msgstr "Enviar somente Áudio"

#: application/templates/book_audiolator.html:26
msgid "Disable TTS"
msgstr "Desativar TTS"

#: application/templates/book_audiolator.html:30
msgid "Send Audio To"
msgstr "Enviar áudio para"

#: application/templates/book_audiolator.html:31
msgid "Empty to use Kindle_email"
msgstr "Deixe em branco para usar Kindle_email"

#: application/templates/book_audiolator.html:35
msgid "TTS Engine"
msgstr "Motor TTS"

#: application/templates/book_audiolator.html:41
#: application/templates/book_summarizer.html:35
#: application/templates/book_translator.html:32
msgid "Api Host"
msgstr "Hospedeiro da API"

#: application/templates/book_audiolator.html:42
#: application/templates/book_summarizer.html:36
#: application/templates/book_summarizer.html:75
msgid "Leave empty to use default"
msgstr "Deixe em branco para usar o padrão"

#: application/templates/book_audiolator.html:46
msgid "Region"
msgstr "Região"

#: application/templates/book_audiolator.html:53
#: application/templates/book_summarizer.html:39
#: application/templates/book_translator.html:36
msgid "Api Key"
msgstr "Api Key"

#: application/templates/book_audiolator.html:66
msgid "Voice name"
msgstr "Nome da voz"

#: application/templates/book_audiolator.html:73
msgid "Voice speed"
msgstr "Velocidade da voz"

#: application/templates/book_audiolator.html:75
msgid "Extra slow"
msgstr "Extra devagar"

#: application/templates/book_audiolator.html:76
msgid "Slow"
msgstr "Devagar"

#: application/templates/book_audiolator.html:77
#: application/templates/book_audiolator.html:87
#: application/templates/book_audiolator.html:97
msgid "Medium"
msgstr "Médio"

#: application/templates/book_audiolator.html:78
msgid "Fast"
msgstr "Rápido"

#: application/templates/book_audiolator.html:79
msgid "Extra fast"
msgstr "Extra rápido"

#: application/templates/book_audiolator.html:83
msgid "Voice pitch"
msgstr "Tonalidade da voz"

#: application/templates/book_audiolator.html:85
msgid "Extra low"
msgstr "Extra baixo"

#: application/templates/book_audiolator.html:86
msgid "Low"
msgstr "Baixo"

#: application/templates/book_audiolator.html:88
msgid "High"
msgstr "Alto"

#: application/templates/book_audiolator.html:89
msgid "Extra high"
msgstr "Extra alto"

#: application/templates/book_audiolator.html:93
msgid "Voice volume"
msgstr "Volume da voz"

#: application/templates/book_audiolator.html:95
msgid "Extra soft"
msgstr "Extra suave"

#: application/templates/book_audiolator.html:96
msgid "Soft"
msgstr "Suave"

#: application/templates/book_audiolator.html:98
msgid "Loud"
msgstr "Alto"

#: application/templates/book_audiolator.html:99
msgid "Extra loud"
msgstr "Extra alto"

#: application/templates/book_audiolator.html:105
#: application/templates/book_summarizer.html:80
#: application/templates/book_translator.html:72
msgid "Apply to all subscribed recipes"
msgstr "Aplicar a todas as receitas assinadas"

#: application/templates/book_audiolator.html:110
#: application/templates/book_summarizer.html:85
#: application/templates/book_translator.html:77
msgid ""
"Note: Enabling this feature will significantly increase consumed CPU "
"instance hours."
msgstr ""
"Nota: Ativar este recurso aumentará significativamente as horas de instância"
" da CPU consumidas."

#: application/templates/book_audiolator.html:119
#: application/templates/book_summarizer.html:94
#: application/templates/book_translator.html:86
msgid "Test (Please save settings firstly)"
msgstr "Teste (Salve as configurações primeiro)"

#: application/templates/book_audiolator.html:121
#: application/templates/book_summarizer.html:96
#: application/templates/book_translator.html:88
msgid "Text"
msgstr "Texto"

#: application/templates/book_audiolator.html:127
msgid "Your browser does not support the audio element."
msgstr "Seu navegador não suporta o elemento de áudio."

#: application/templates/book_summarizer.html:29
msgid "Model"
msgstr "Model"

#: application/templates/book_summarizer.html:45
msgid "Auto"
msgstr "Automático"

#: application/templates/book_summarizer.html:56
msgid "Summary words"
msgstr "Palavras de resumo"

#: application/templates/book_summarizer.html:70
msgid "Summary style"
msgstr "Estilo de resumo"

#: application/templates/book_summarizer.html:74
msgid "Placeholders available:"
msgstr "Marcadores disponíveis:"

#: application/templates/book_summarizer.html:74
msgid "Custom prompt"
msgstr "Prompt personalizado"

#: application/templates/book_summarizer.html:100
msgid "Summary"
msgstr "Resumo"

#: application/templates/book_translator.html:37
msgid "One key per line"
msgstr "Uma chave por linha"

#: application/templates/book_translator.html:40
#: application/templates/word_lookup.html:51
msgid "Source language"
msgstr "Idioma de origem"

#: application/templates/book_translator.html:46
msgid "Target language"
msgstr "Idioma de destino"

#: application/templates/book_translator.html:52
msgid "Translation Position"
msgstr "Posição da tradução"

#: application/templates/book_translator.html:54
msgid "Below original"
msgstr "Abaixo do original"

#: application/templates/book_translator.html:55
msgid "Above original"
msgstr "Acima do original"

#: application/templates/book_translator.html:56
msgid "Left to original"
msgstr "À esquerda do original"

#: application/templates/book_translator.html:57
msgid "Right to original"
msgstr "À direita do original"

#: application/templates/book_translator.html:58
msgid "Translated text only"
msgstr "Somente texto traduzido"

#: application/templates/book_translator.html:62
msgid "Original text style"
msgstr "Estilo do texto original"

#: application/templates/book_translator.html:66
msgid "Translated text style"
msgstr "Estilo do texto traduzido"

#: application/templates/book_translator.html:92
msgid "Translation"
msgstr "Tradução"

#: application/templates/change_password.html:15
msgid "Old password"
msgstr "Senha antiga"

#: application/templates/change_password.html:19
#: application/templates/reset_password.html:31
#: application/templates/reset_password.html:32
msgid "New password"
msgstr "Nova senha"

#: application/templates/change_password.html:23
#: application/templates/reset_password.html:35
#: application/templates/reset_password.html:36
#: application/templates/signup.html:29
#: application/templates/user_account.html:23
msgid "Confirm password"
msgstr "Confirmar senha"

#: application/templates/change_password.html:31
msgid "Share key"
msgstr "Chave de compartilhamento"

#: application/templates/change_password.html:37
msgid "Confirm Change"
msgstr "Confirmar alteração"

#: application/templates/debug_cmd.html:3
msgid "Debug cmd"
msgstr "Comando de depuração"

#: application/templates/home.html:3
msgid "Home"
msgstr "Início"

#: application/templates/home.html:10 application/templates/login.html:18
msgid "You are in DEMO mode. Logging out will delete all data."
msgstr "Você está no modo DEMO. Sair excluirá todos os dados."

#: application/templates/home.html:12
msgid "Sharing Joyful News Every Step of the Way"
msgstr "Compartilhando boas notícias a cada passo"

#: application/templates/home.html:31
msgid "Inherited From Calibre"
msgstr "Herdado do Calibre"

#: application/templates/home.html:34
#, python-format
msgid ""
"Empowered by %(calibre)s, you can easily create e-books on a Python-"
"supported online platform and seamlessly transfer them to your e-reader or "
"other reading devices."
msgstr ""
"Potencializado por %(calibre)s, você pode criar e-books facilmente em uma "
"plataforma online compatível com Python e transferi-los para seu leitor "
"eletrônico ou outros dispositivos de leitura."

#: application/templates/home.html:42
msgid "Share Your Ideas"
msgstr "Compartilhe suas ideias"

#: application/templates/home.html:45
#, python-format
msgid ""
"With the open-source %(kindleear)s application, you can set up your own "
"server to deliver daily news feeds to your e-reader and effortlessly share "
"the service with friends."
msgstr ""
"Com o aplicativo de código aberto %(kindleear)s, você pode configurar seu "
"próprio servidor para enviar feeds de notícias diárias para o seu e-reader e"
" compartilhar o serviço com facilidade com amigos."

#: application/templates/library.html:50 application/templates/my.html:61
msgid "Search"
msgstr "Pesquisar"

#: application/templates/login.html:38 application/view/login.py:197
#: application/view/login.py:204
msgid ""
"The website does not allow registration. You can ask the owner for an "
"account."
msgstr ""
"O site não permite registro. Você pode pedir uma conta ao proprietário."

#: application/templates/logs.html:11
msgid "Only display last 20 logs"
msgstr "Exibir apenas os últimos 20 logs"

#: application/templates/logs.html:26 application/templates/logs.html:75
msgid "Status"
msgstr "Status"

#: application/templates/logs.html:58
msgid "Logs of other users"
msgstr "Logs de outros usuários"

#: application/templates/my.html:12 application/templates/settings.html:153
msgid "Custom RSS"
msgstr "RSS personalizado"

#: application/templates/my.html:23
msgid "Content Embedded"
msgstr "Conteúdo incorporado"

#: application/templates/my.html:27
msgid "Deliver Separately"
msgstr "Entregar separadamente"

#: application/templates/my.html:46
msgid "Subscribed"
msgstr "Inscrito"

#: application/templates/my.html:51
msgid "Library"
msgstr "Biblioteca"

#: application/templates/my.html:51
msgid "get more recipes"
msgstr "obter mais receitas"

#: application/templates/my.html:68
msgid "Subscription to selected recipe successful."
msgstr "Inscrição na receita selecionada realizada com sucesso."

#: application/templates/my.html:71
msgid "Bookmarklet"
msgstr "Bookmarklet"

#: application/templates/my.html:75
msgid "Send to Kindle"
msgstr "Enviar para o Kindle"

#: application/templates/my.html:79
msgid "Subscribe with KindleEar"
msgstr "Assinar com KindleEar"

#: application/templates/my.html:82
msgid "Drag and drop this link to your bookmarks"
msgstr "Arraste e solte este link nos seus favoritos"

#: application/templates/my.html:86
msgid "Chrome extension"
msgstr "Extensão do Chrome"

#: application/templates/my.html:87
msgid "Edge extension"
msgstr "Extensão do Edge"

#: application/templates/my.html:89
msgid "Browser extensions also available"
msgstr "Extensões para navegador também disponíveis"

#: application/templates/reader.html:57
msgid "Push current book"
msgstr "Enviar livro atual"

#: application/templates/reader.html:63
msgid "Push current article"
msgstr "Enviar artigo atual"

#: application/templates/reader.html:71
msgid "Delete selected books"
msgstr "Excluir livros selecionados"

#: application/templates/reader.html:77
msgid "Allow click links"
msgstr "Permitir clicar em links"

#: application/templates/reader.html:83
msgid "Top-left dict mode"
msgstr "Modo de dicionário no canto superior esquerdo"

#: application/templates/reader.html:89
msgid "Dark mode"
msgstr "Modo escuro"

#: application/templates/reader.html:95
msgid "eInk mode"
msgstr "Modo eInk"

#: application/templates/reader.html:101
msgid "Increase font size"
msgstr "Aumentar o tamanho da fonte"

#: application/templates/reader.html:107
msgid "Decrease font size"
msgstr "Diminuir o tamanho da fonte"

#: application/templates/reader.html:113
msgid "Visualize Touch Regions"
msgstr "Visualizar áreas de toque"

#: application/templates/reader.html:119
msgid "Help"
msgstr "Ajuda"

#: application/templates/reader.html:125
#: application/templates/reader_404.html:135
msgid "Menu"
msgstr "Menu"

#: application/templates/reader.html:139
msgid "Collapse all"
msgstr "Recolher tudo"

#: application/templates/reader.html:144
msgid "Expand all"
msgstr "Expandir tudo"

#: application/templates/reader.html:149
#: application/templates/reader_404.html:140
msgid "Prev"
msgstr "Anterior"

#: application/templates/reader.html:154
#: application/templates/reader_404.html:143
msgid "Next page"
msgstr "Próxima página"

#: application/templates/reader.html:192
msgid "Pushed successfully."
msgstr "Enviado com sucesso."

#: application/templates/reader.html:193
msgid "There are currently no books or articles being read."
msgstr "Atualmente, não há livros ou artigos sendo lidos."

#: application/templates/reset_password.html:3
#: application/templates/reset_password.html:41
msgid "Reset password"
msgstr "Redefinir senha"

#: application/templates/settings.html:14
msgid ""
"Your account will pause after {0}, please log in again before it expires."
msgstr ""
"Sua conta será pausada após {0}, por favor, faça login novamente antes de "
"expirar."

#: application/templates/settings.html:23
msgid "Base"
msgstr "Base"

#: application/templates/settings.html:25
msgid "Auto delivery"
msgstr "Entrega automática"

#: application/templates/settings.html:28
msgid "Recipes and custom RSS"
msgstr "Receitas e RSS personalizado"

#: application/templates/settings.html:29
msgid "Recipes only"
msgstr "Somente receitas"

#: application/templates/settings.html:30
msgid "Disable all"
msgstr "Desativar tudo"

#: application/templates/settings.html:34
msgid "Kindle E-mail"
msgstr "E-mail Kindle"

#: application/templates/settings.html:35
msgid "Seperated by comma"
msgstr "Separado por vírgula"

#: application/templates/settings.html:39
msgid "Delivery mode"
msgstr "Modo de entrega"

#: application/templates/settings.html:42
msgid "Email delivery & online reading"
msgstr "Entrega por e-mail e leitura online"

#: application/templates/settings.html:43
msgid "Email delivery"
msgstr "Entrega por e-mail"

#: application/templates/settings.html:44
msgid "Online reading"
msgstr "Leitura online"

#: application/templates/settings.html:48
msgid "Retention days for online books"
msgstr "Dias de retenção para livros online"

#: application/templates/settings.html:48
msgid "Web shelf"
msgstr "Prateleira web"

#: application/templates/settings.html:53
#: application/templates/settings.html:176
msgid "2 Days"
msgstr "2 Dias"

#: application/templates/settings.html:54
#: application/templates/settings.html:177
msgid "3 Days"
msgstr "3 Dias"

#: application/templates/settings.html:55
#: application/templates/settings.html:178
msgid "4 Days"
msgstr "4 Dias"

#: application/templates/settings.html:56
#: application/templates/settings.html:179
msgid "5 Days"
msgstr "5 Dias"

#: application/templates/settings.html:57
#: application/templates/settings.html:180
msgid "6 Days"
msgstr "6 Dias"

#: application/templates/settings.html:64
msgid "Time zone"
msgstr "Fuso horário"

#: application/templates/settings.html:89
msgid "Delivery time"
msgstr "Hora de entrega"

#: application/templates/settings.html:97
msgid "Book type"
msgstr "Tipo de livro"

#: application/templates/settings.html:104
msgid "Determines final file size"
msgstr "Determina o tamanho final do arquivo"

#: application/templates/settings.html:104
msgid "Device type"
msgstr "Tipo de dispositivo"

#: application/templates/settings.html:113
msgid "Title format"
msgstr "Formato do título"

#: application/templates/settings.html:116
msgid "Title Only"
msgstr "Somente título"

#: application/templates/settings.html:130
msgid "Remove hyperlinks"
msgstr "Remover hyperlinks"

#: application/templates/settings.html:133
msgid "Do not remove hyperlinks"
msgstr "Não remover hyperlinks"

#: application/templates/settings.html:134
msgid "Remove image links"
msgstr "Remover links de imagem"

#: application/templates/settings.html:135
msgid "Remove text links"
msgstr "Remover links de texto"

#: application/templates/settings.html:136
msgid "Remove all hyperlinks"
msgstr "Remover todos os hyperlinks"

#: application/templates/settings.html:140
msgid "Navbar"
msgstr "Barra de Navegação"

#: application/templates/settings.html:144
msgid "Top Center"
msgstr "Topo Central"

#: application/templates/settings.html:145
msgid "Top Left"
msgstr "Topo Esquerdo"

#: application/templates/settings.html:146
msgid "Bottom Center"
msgstr "Centro Inferior"

#: application/templates/settings.html:147
msgid "Bottom Left"
msgstr "Inferior Esquerdo"

#: application/templates/settings.html:159
msgid "Sets the lookup dictionary"
msgstr "Define o dicionário de pesquisa"

#: application/templates/settings.html:171
msgid "Oldest article"
msgstr "Artigo mais antigo"

#: application/templates/settings.html:185
msgid "Time format"
msgstr "Formato de hora"

#: application/templates/settings.html:198
msgid "Author format"
msgstr "Formato do autor"

#: application/templates/settings.html:215
msgid "Send Mail Service"
msgstr "Serviço de envio de e-mail"

#: application/templates/settings.html:217
msgid "Service"
msgstr "Serviço"

#: application/templates/settings.html:225
msgid "ApiKey"
msgstr "ApiKey"

#: application/templates/settings.html:229
msgid "SecretKey"
msgstr "SecretKey"

#: application/templates/settings.html:237
msgid "Port"
msgstr "Porta"

#: application/templates/settings.html:249
msgid "Save path"
msgstr "Caminho de salvamento"

#: application/templates/settings.html:257
#, python-format
msgid ""
"Important: Please activate your kindle firstly, then goto %(personal)s Page "
"and add %(sender)s to 'Approved Personal Document E-mail List'."
msgstr ""
"Importante: Por favor, ative seu kindle primeiro, depois vá para a página "
"%(personal)s e adicione %(sender)s à 'Lista de E-mails de Documentos "
"Pessoais Aprovados'."

#: application/templates/settings.html:257
msgid "Personal Document Settings"
msgstr "Configurações de Documentos Pessoais"

#: application/templates/settings.html:263
#, python-format
msgid ""
"You have not yet set up your email address. Please go to the %(admin)s page "
"to add your email address firstly."
msgstr ""
"Você ainda não configurou seu endereço de e-mail. Por favor, vá para a "
"página %(admin)s para adicionar seu endereço de e-mail primeiro."

#: application/templates/settings.html:271
msgid "Send Test Email"
msgstr "Enviar e-mail de teste"

#: application/templates/signup.html:38
msgid "Invitation code"
msgstr "Código de convite"

#: application/templates/user_account.html:3
msgid "User account"
msgstr "Conta de usuário"

#: application/templates/user_account.html:42
msgid "Never expire"
msgstr "Nunca expirar"

#: application/templates/webmail.html:3
msgid "Webmail"
msgstr "Webmail"

#: application/templates/webmail.html:17
msgid "Refresh"
msgstr "Atualizar"

#: application/templates/webmail.html:18
msgid "Read/Unread"
msgstr "Lido/Não lido"

#: application/templates/webmail.html:19
msgid "Reply"
msgstr "Responder"

#: application/templates/webmail.html:20
msgid "Forward"
msgstr "Encaminhar"

#: application/templates/webmail.html:21
msgid "Fwd as Attach"
msgstr "Encaminhar como anexo"

#: application/templates/word_lookup.html:71
msgid "Word"
msgstr "Palavra"

#: application/view/admin.py:48 application/view/adv.py:437
#: application/view/adv.py:528 application/view/settings.py:67
#: application/view/translator.py:88 application/view/translator.py:172
#: application/view/translator.py:254
msgid "Settings Saved!"
msgstr "Configurações salvas!"

#: application/view/admin.py:57 application/view/admin.py:64
#: application/view/admin.py:91
msgid "Add account"
msgstr "Adicionar conta"

#: application/view/admin.py:63 application/view/admin.py:103
#: application/view/admin.py:135
msgid "You do not have sufficient privileges."
msgstr "Você não tem privilégios suficientes."

#: application/view/admin.py:79 application/view/login.py:44
#: application/view/login.py:222
msgid "The username includes unsafe chars."
msgstr "O nome de usuário inclui caracteres inseguros."

#: application/view/admin.py:83 application/view/login.py:224
msgid "Already exist the username."
msgstr "O nome de usuário já existe."

#: application/view/admin.py:88
msgid "The password includes non-ascii chars."
msgstr "A senha inclui caracteres não-ASCII."

#: application/view/admin.py:107 application/view/admin.py:132
#: application/view/admin.py:163 application/view/extension.py:32
#: application/view/extension.py:61
msgid "The username '{}' does not exist."
msgstr "O nome de usuário '{}' não existe."

#: application/view/admin.py:123
msgid "The password will not be changed if the fields are empties."
msgstr "A senha não será alterada se os campos estiverem vazios."

#: application/view/admin.py:130 application/view/admin.py:184
msgid "Change"
msgstr "Alterar"

#: application/view/admin.py:181
msgid "Change success."
msgstr "Alteração bem-sucedida."

#: application/view/admin.py:194
msgid "The old password is wrong."
msgstr "A senha antiga está errada."

#: application/view/admin.py:196
msgid "Changes saved successfully."
msgstr "Alterações salvas com sucesso."

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108 application/view/adv.py:109
#: application/view/adv.py:110 application/view/adv.py:111
#: application/view/adv.py:112 application/view/adv.py:113
msgid "Append hyperlink '{}' to article"
msgstr "Adicionar hiperlink '{}' ao artigo"

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108
msgid "Save to {}"
msgstr "Salvar em {}"

#: application/view/adv.py:104
msgid "evernote"
msgstr "evernote"

#: application/view/adv.py:105
msgid "wiz"
msgstr "wiz"

#: application/view/adv.py:106
msgid "pocket"
msgstr "pocket"

#: application/view/adv.py:107
msgid "instapaper"
msgstr "instapaper"

#: application/view/adv.py:108
msgid "wallabag"
msgstr "wallabag"

#: application/view/adv.py:109 application/view/adv.py:110
#: application/view/adv.py:111 application/view/adv.py:112
msgid "Share on {}"
msgstr "Compartilhar em {}"

#: application/view/adv.py:109
msgid "weibo"
msgstr "weibo"

#: application/view/adv.py:110
msgid "facebook"
msgstr "facebook"

#: application/view/adv.py:112
msgid "tumblr"
msgstr "tumblr"

#: application/view/adv.py:113
msgid "Open in browser"
msgstr "Abrir no navegador"

#: application/view/adv.py:114
msgid "Append qrcode of url to article"
msgstr "Adicionar QR code da URL ao artigo"

#: application/view/adv.py:381 application/view/share.py:54
#: application/view/subscribe.py:250
msgid "Unknown command: {}"
msgstr "Comando desconhecido: {}"

#: application/view/adv.py:439 application/view/adv.py:530
msgid "The format is invalid."
msgstr "O formato é inválido."

#: application/view/adv.py:562
msgid "Authorization Error!<br/>{}"
msgstr "Erro de autorização!<br/>{}"

#: application/view/adv.py:583
msgid "Success authorized by Pocket!"
msgstr "Autorização bem-sucedida pelo Pocket!"

#: application/view/adv.py:589
msgid ""
"Failed to request authorization of Pocket!<hr/>See details "
"below:<br/><br/>{}"
msgstr ""
"Falha ao solicitar autorização do Pocket!<hr/>Veja os detalhes "
"abaixo:<br/><br/>{}"

#: application/view/adv.py:610
msgid "The Instapaper service encountered an error. Please try again later."
msgstr ""
"O serviço do Instapaper encontrou um erro. Por favor, tente novamente mais "
"tarde."

#: application/view/adv.py:623
msgid "Request type [{}] unsupported"
msgstr "Tipo de solicitação [{}] não suportado"

#: application/view/deliver.py:82 application/view/login.py:169
#: application/view/share.py:41
msgid "The username does not exist or the email is empty."
msgstr "O nome de usuário não existe ou o e-mail está vazio."

#: application/view/deliver.py:109
msgid "The following recipes has been added to the push queue."
msgstr "As seguintes receitas foram adicionadas à fila de envio."

#: application/view/deliver.py:112
msgid "There are no recipes to deliver."
msgstr "Não há receitas para enviar."

#: application/view/extension.py:69
msgid "The rules parameter is invalid."
msgstr "O parâmetro de regras é inválido."

#: application/view/library.py:32
msgid "Cannot fetch data from {}, status: {}"
msgstr "Não é possível obter dados de {}, status: {}"

#: application/view/library.py:48 application/view/subscribe.py:238
#: application/view/subscribe.py:380 application/view/subscribe.py:409
#: application/view/subscribe.py:416 application/view/translator.py:30
msgid "The recipe does not exist."
msgstr "A receita não existe."

#: application/view/login.py:27 application/view/login.py:76
msgid "Please use {}/{} to login at first time."
msgstr "Por favor, use {}/{} para fazer login pela primeira vez."

#: application/view/login.py:40
msgid "Username is empty."
msgstr "O nome de usuário está vazio."

#: application/view/login.py:42
msgid "The len of username reached the limit of 25 chars."
msgstr "O comprimento do nome de usuário atingiu o limite de 25 caracteres."

#: application/view/login.py:80
msgid "Forgot password?"
msgstr "Esqueceu a senha?"

#: application/view/login.py:148 application/view/login.py:275
msgid "The token is wrong or expired."
msgstr "O token está errado ou expirou."

#: application/view/login.py:151
msgid "Please input the correct username and email to reset password."
msgstr ""
"Por favor, insira o nome de usuário e e-mail corretos para redefinir a "
"senha."

#: application/view/login.py:153
msgid "The email of account '{name}' is {email}."
msgstr "O e-mail da conta '{name}' é {email}."

#: application/view/login.py:174
msgid "Reset password success, Please close this page and login again."
msgstr ""
"Sucesso ao redefinir a senha, por favor feche esta página e faça login "
"novamente."

#: application/view/login.py:177
msgid "The email you input is not associated with this account."
msgstr "O e-mail que você inseriu não está associado a esta conta."

#: application/view/login.py:186
msgid "The link to reset your password has been sent to your email."
msgstr "O link para redefinir sua senha foi enviado para o seu e-mail."

#: application/view/login.py:187
msgid "Please check your email inbox within 24 hours."
msgstr ""
"Por favor, verifique sua caixa de entrada de e-mail dentro de 24 horas."

#: application/view/login.py:218
msgid "The invitation code is invalid."
msgstr "O código de convite é inválido."

#: application/view/login.py:226
msgid ""
"Failed to create an account. Please contact the administrator for "
"assistance."
msgstr ""
"Falha ao criar uma conta. Por favor, entre em contato com o administrador "
"para assistência."

#: application/view/login.py:236
msgid "Successfully created account."
msgstr "Conta criada com sucesso."

#: application/view/login.py:247
msgid "Reset KindleEar password"
msgstr "Redefinir senha do KindleEar"

#: application/view/login.py:248
msgid "This is an automated email. Please do not reply to it."
msgstr "Este é um e-mail automatizado. Por favor, não responda a ele."

#: application/view/login.py:249
msgid "You can click the following link to reset your KindleEar password."
msgstr ""
"Você pode clicar no link abaixo para redefinir sua senha do KindleEar."

#: application/view/reader.py:88
msgid "The article is missing?"
msgstr "O artigo está ausente?"

#: application/view/reader.py:190 application/view/translator.py:121
#: application/view/translator.py:205 application/view/translator.py:287
msgid "The text is empty."
msgstr "O texto está vazio."

#: application/view/reader.py:239
msgid "No definitions found for '{}'."
msgstr "Nenhuma definição encontrada para '{}'."

#: application/view/reader.py:240
msgid "Did you mean?"
msgstr "Você quis dizer?"

#: application/view/reader.py:324 application/view/reader.py:331
msgid "Failed to push: {}"
msgstr "Falha ao enviar: {}"

#: application/view/reader.py:379
msgid "Failed to create ebook."
msgstr "Falha ao criar o ebook."

#: application/view/settings.py:131
msgid ""
"You have not yet set up your email address. Please go to the 'Account' page "
"to add your email address firstly."
msgstr ""
"Você ainda não configurou seu endereço de e-mail. Por favor, vá para a "
"página 'Conta' para adicionar seu e-mail primeiro."

#: application/view/settings.py:215
msgid "English"
msgstr "Inglês"

#: application/view/settings.py:216
msgid "Simplified Chinese"
msgstr "Chinês Simplificado"

#: application/view/settings.py:217
msgid "Traditional Chinese"
msgstr "Chinês Tradicional"

#: application/view/settings.py:218
msgid "French"
msgstr "Francês"

#: application/view/settings.py:219
msgid "Spanish"
msgstr "Espanhol"

#: application/view/settings.py:220
msgid "Portuguese"
msgstr "Português"

#: application/view/settings.py:221
msgid "German"
msgstr "Alemão"

#: application/view/settings.py:222
msgid "Italian"
msgstr "Italiano"

#: application/view/settings.py:223
msgid "Japanese"
msgstr "Japonês"

#: application/view/settings.py:224
msgid "Russian"
msgstr "Russo"

#: application/view/settings.py:225
msgid "Turkish"
msgstr "Turco"

#: application/view/settings.py:226
msgid "Korean"
msgstr "Coreano"

#: application/view/settings.py:227
msgid "Arabic"
msgstr "Árabe"

#: application/view/settings.py:228
msgid "Czech"
msgstr "Tcheco"

#: application/view/settings.py:229
msgid "Dutch"
msgstr "Neerlandês"

#: application/view/settings.py:230
msgid "Greek"
msgstr "Grego"

#: application/view/settings.py:231
msgid "Hindi"
msgstr "Hindi"

#: application/view/settings.py:232
msgid "Malaysian"
msgstr "Malaio"

#: application/view/settings.py:233
msgid "Bengali"
msgstr "Bengali"

#: application/view/settings.py:234
msgid "Persian"
msgstr "Persa"

#: application/view/settings.py:235
msgid "Urdu"
msgstr "Urdu"

#: application/view/settings.py:236
msgid "Swahili"
msgstr "Suaíli"

#: application/view/settings.py:237
msgid "Vietnamese"
msgstr "Vietnamita"

#: application/view/settings.py:238
msgid "Punjabi"
msgstr "Punjabi"

#: application/view/settings.py:239
msgid "Javanese"
msgstr "Javanês"

#: application/view/settings.py:240
msgid "Tagalog"
msgstr "Tagalo"

#: application/view/settings.py:241
msgid "Hausa"
msgstr "Hausa"

#: application/view/settings.py:242
msgid "Thai"
msgstr "Tailandês"

#: application/view/settings.py:243
msgid "Polish"
msgstr "Polonês"

#: application/view/settings.py:244
msgid "Romanian"
msgstr "Romeno"

#: application/view/settings.py:245
msgid "Hungarian"
msgstr "Húngaro"

#: application/view/settings.py:246
msgid "Swedish"
msgstr "Sueco"

#: application/view/settings.py:247
msgid "Hebrew"
msgstr "Hebraico"

#: application/view/settings.py:248
msgid "Norwegian"
msgstr "Norueguês"

#: application/view/settings.py:249
msgid "Finnish"
msgstr "Finlandês"

#: application/view/settings.py:250
msgid "Danish"
msgstr "Dinamarquês"

#: application/view/settings.py:251
msgid "Ukrainian"
msgstr "Ucraniano"

#: application/view/settings.py:252
msgid "Tamil"
msgstr "Tâmil"

#: application/view/settings.py:253
msgid "Marathi"
msgstr "Marata"

#: application/view/settings.py:254
msgid "Burmese"
msgstr "Birmanês"

#: application/view/settings.py:255
msgid "Amharic"
msgstr "Amárico"

#: application/view/settings.py:256
msgid "Azerbaijani"
msgstr "Azerbaijano"

#: application/view/settings.py:257
msgid "Kazakh"
msgstr "Cazaque"

#: application/view/settings.py:258
msgid "Serbian"
msgstr "Sérvio"

#: application/view/settings.py:259
msgid "Croatian"
msgstr "Croata"

#: application/view/settings.py:260
msgid "Slovak"
msgstr "Eslovaco"

#: application/view/settings.py:261
msgid "Bulgarian"
msgstr "Búlgaro"

#: application/view/settings.py:262
msgid "Icelandic"
msgstr "Islandês"

#: application/view/settings.py:263
msgid "Lithuanian"
msgstr "Lituano"

#: application/view/settings.py:264
msgid "Latvian"
msgstr "Letão"

#: application/view/settings.py:265
msgid "Estonian"
msgstr "Estoniano"

#: application/view/settings.py:266
msgid "Macedonian"
msgstr "Macedônio"

#: application/view/settings.py:267
msgid "Albanian"
msgstr "Albanês"

#: application/view/settings.py:268
msgid "Galician"
msgstr "Galego"

#: application/view/settings.py:269
msgid "Welsh"
msgstr "Galês"

#: application/view/settings.py:270
msgid "Basque"
msgstr "Basco"

#: application/view/settings.py:271
msgid "Nepali"
msgstr "Nepalês"

#: application/view/share.py:60
msgid "There is no {} email yet."
msgstr "Ainda não há email {}."

#: application/view/share.py:108 application/view/share.py:133
#: application/view/share.py:155 application/view/share.py:177
msgid "Saved to your {} account."
msgstr "Salvo na sua conta {}."

#: application/view/share.py:111 application/view/share.py:129
#: application/view/share.py:158 application/view/share.py:179
msgid "Failed save to {}."
msgstr "Falha ao salvar em {}."

#: application/view/share.py:112 application/view/share.py:130
#: application/view/share.py:159 application/view/share.py:180
msgid "Reason :"
msgstr "Motivo :"

#: application/view/share.py:121
msgid "Unauthorized {} account!"
msgstr "Conta {} não autorizada!"

#: application/view/share.py:134
msgid "See details below:"
msgstr "Veja os detalhes abaixo:"

#: application/view/share.py:157
msgid "Unknown: {}"
msgstr "Desconhecido: {}"

#: application/view/subscribe.py:81 application/view/subscribe.py:167
msgid "Duplicated subscription!"
msgstr "Assinatura duplicada!"

#: application/view/subscribe.py:126
msgid "The Title or Url is empty."
msgstr "O título ou URL está vazio."

#: application/view/subscribe.py:139
msgid "Failed to fetch the recipe."
msgstr "Falha ao buscar a receita."

#: application/view/subscribe.py:153 application/view/subscribe.py:331
msgid "Failed to save the recipe. Error:"
msgstr "Falha ao salvar a receita. Erro:"

#: application/view/subscribe.py:195
msgid "The Rss does not exist."
msgstr "O Rss não existe."

#: application/view/subscribe.py:278
msgid "You can only delete the uploaded recipe."
msgstr "Você só pode excluir a receita carregada."

#: application/view/subscribe.py:283
msgid "The recipe have been subscribed, please unsubscribe it before delete."
msgstr "A receita foi assinada, por favor, desinscreva-se antes de excluir."

#: application/view/subscribe.py:304 application/view/translator.py:51
#: application/view/translator.py:104 application/view/translator.py:117
#: application/view/translator.py:140 application/view/translator.py:188
#: application/view/translator.py:201 application/view/translator.py:230
#: application/view/translator.py:270 application/view/translator.py:283
msgid "This recipe has not been subscribed to yet."
msgstr "Esta receita ainda não foi assinada."

#: application/view/subscribe.py:318
msgid "Can not read uploaded file, Error:"
msgstr "Não é possível ler o arquivo carregado, Erro:"

#: application/view/subscribe.py:326
msgid ""
"Failed to decode the recipe. Please ensure that your recipe is saved in "
"utf-8 encoding."
msgstr ""
"Falha ao decodificar a receita. Certifique-se de que sua receita está salva "
"no formato de codificação utf-8."

#: application/view/subscribe.py:349
msgid "Cannot find any subclass of BasicNewsRecipe."
msgstr "Não é possível encontrar nenhuma subclasse de BasicNewsRecipe."

#: application/view/subscribe.py:354
msgid "The recipe is already in the library."
msgstr "A receita já está na biblioteca."

#: application/view/subscribe.py:387
msgid "The login information for this recipe has been cleared."
msgstr "As informações de login para esta receita foram apagadas."

#: application/view/subscribe.py:391
msgid "The login information for this recipe has been saved."
msgstr "As informações de login para esta receita foram salvas."

#: application/view/translator.py:81 application/view/translator.py:165
msgid "The api key is required."
msgstr "A chave da API é necessária."
