# Chinese translations for KindleEar.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the KindleEar project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: KindleEar v3.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-10 19:49-0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: KindleEar <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.14.0\n"

#: application/templates/admin.html:3 application/templates/base.html:53
#: application/templates/base.html:192 application/templates/settings.html:263
msgid "Account"
msgstr "Compte"

#: application/templates/admin.html:19
msgid "Signup settings"
msgstr "Paramètres d'inscription"

#: application/templates/admin.html:19
#: application/templates/adv_calibre_options.html:18
#: application/templates/adv_proxy.html:18
msgid "Save"
msgstr "Enregistrer"

#: application/templates/admin.html:21
#: application/templates/user_account.html:31
msgid "Email service"
msgstr "Service de messagerie"

#: application/templates/admin.html:23
#: application/templates/user_account.html:34
msgid "Same as admin"
msgstr "Identique à l'administrateur"

#: application/templates/admin.html:24
#: application/templates/user_account.html:35
msgid "Independent"
msgstr "Indépendant"

#: application/templates/admin.html:28
msgid "Signup type"
msgstr "Type d'inscription"

#: application/templates/admin.html:30
msgid "Public"
msgstr "Public"

#: application/templates/admin.html:31
msgid "One time code"
msgstr "Code à usage unique"

#: application/templates/admin.html:32
msgid "Permanent code"
msgstr "Code permanent"

#: application/templates/admin.html:36
msgid "Invitation codes"
msgstr "Codes d'invitation"

#: application/templates/admin.html:37
msgid "one code per line"
msgstr "un code par ligne"

#: application/templates/admin.html:43
msgid "Accounts"
msgstr "Comptes"

#: application/templates/admin.html:43
#: application/templates/adv_inboundmail.html:61
#: application/templates/my.html:34 application/view/admin.py:57
#: application/view/admin.py:64 application/view/admin.py:91
msgid "Add"
msgstr "Ajouter"

#: application/templates/admin.html:54
#: application/templates/adv_archive.html:69
#: application/templates/home.html:19 application/templates/login.html:24
#: application/templates/logs.html:70
#: application/templates/reset_password.html:19
#: application/templates/reset_password.html:20
#: application/templates/settings.html:241
#: application/templates/signup.html:21
#: application/templates/user_account.html:15
msgid "Username"
msgstr "Nom d'utilisateur"

#: application/templates/admin.html:55
msgid "AutoSend"
msgstr "Envoi automatique"

#: application/templates/admin.html:56
#: application/templates/change_password.html:27
#: application/templates/reset_password.html:26
#: application/templates/signup.html:33
#: application/templates/user_account.html:27
msgid "Email"
msgstr "Email"

#: application/templates/admin.html:57
#: application/templates/user_account.html:39
msgid "Expiration"
msgstr "Expiration"

#: application/templates/admin.html:58
msgid "Operation"
msgstr "Opération"

#: application/templates/admin.html:65
msgid "Yes"
msgstr "Oui"

#: application/templates/admin.html:65
msgid "No"
msgstr "Non"

#: application/templates/admin.html:68
msgid "Never"
msgstr "Jamais"

#: application/templates/admin.html:70
#: application/templates/adv_inboundmail.html:26
#: application/templates/settings.html:58
#: application/templates/settings.html:181
#: application/templates/user_account.html:43
msgid "7 Days"
msgstr "7 jours"

#: application/templates/admin.html:72
#: application/templates/user_account.html:44
msgid "1 Month"
msgstr "1 mois"

#: application/templates/admin.html:74
#: application/templates/user_account.html:45
msgid "3 Months"
msgstr "3 mois"

#: application/templates/admin.html:76
#: application/templates/user_account.html:46
msgid "6 Months"
msgstr "6 mois"

#: application/templates/admin.html:78
#: application/templates/user_account.html:47
msgid "1 Year"
msgstr "1 an"

#: application/templates/admin.html:80
#: application/templates/user_account.html:48
msgid "2 Years"
msgstr "2 ans"

#: application/templates/admin.html:85
#: application/templates/change_password.html:3
#: application/templates/change_password.html:13 application/view/admin.py:129
#: application/view/admin.py:183
msgid "Edit account"
msgstr "Modifier le compte"

#: application/templates/admin.html:89
#: application/templates/adv_inboundmail.html:54
#: application/templates/adv_uploadcss.html:31
#: application/templates/base.html:25 application/templates/webmail.html:22
msgid "Delete"
msgstr "Supprimer"

#: application/templates/adv_archive.html:3
#: application/templates/adv_archive.html:14
#: application/templates/adv_base.html:57
#: application/templates/adv_base.html:61
msgid "Archive"
msgstr "Archiver"

#: application/templates/adv_archive.html:15
msgid "Append hyperlinks for archiving or sharing."
msgstr "Ajouter des liens hypertextes pour l'archivage ou le partage."

#: application/templates/adv_archive.html:40
msgid "Authorized"
msgstr "Autorisé"

#: application/templates/adv_archive.html:42
msgid "Authorize"
msgstr "Autoriser"

#: application/templates/adv_archive.html:53
msgid "Email or Username"
msgstr "Email ou nom d'utilisateur"

#: application/templates/adv_archive.html:56
#: application/templates/adv_archive.html:72
#: application/templates/base.html:54 application/templates/home.html:20
#: application/templates/login.html:28 application/templates/settings.html:245
#: application/templates/signup.html:25
#: application/templates/user_account.html:19
msgid "Password"
msgstr "Mot de passe"

#: application/templates/adv_archive.html:59
#: application/templates/adv_archive.html:75
#: application/templates/base.html:64
msgid "Verify"
msgstr "Vérifier"

#: application/templates/adv_archive.html:78
msgid "client_id"
msgstr "client_id"

#: application/templates/adv_archive.html:81
msgid "client_secret"
msgstr "client_secret"

#: application/templates/adv_archive.html:84
#: application/templates/settings.html:233
msgid "Host"
msgstr "Hôte"

#: application/templates/adv_archive.html:119
#: application/templates/adv_dict.html:79
#: application/templates/adv_inboundmail.html:34
#: application/templates/book_audiolator.html:113
#: application/templates/book_summarizer.html:88
#: application/templates/book_translator.html:80
#: application/templates/settings.html:269
msgid "Save settings"
msgstr "Enregistrer les paramètres"

#: application/templates/adv_base.html:39
#: application/templates/adv_base.html:43
#: application/templates/adv_delivernow.html:8
msgid "Deliver Now"
msgstr "Livrer maintenant"

#: application/templates/adv_base.html:48
#: application/templates/adv_base.html:52
#: application/templates/adv_inboundmail.html:3
#: application/templates/adv_inboundmail.html:9
#: application/templates/adv_inboundmail.html:14
msgid "Inbound Mail"
msgstr "Courrier entrant"

#: application/templates/adv_base.html:66
#: application/templates/adv_base.html:70
#: application/templates/adv_dict.html:3
#: application/templates/adv_dict.html:13
#: application/templates/reader.html:134
msgid "Dictionary"
msgstr "Dictionnaire"

#: application/templates/adv_base.html:75
#: application/templates/adv_base.html:83
#: application/templates/adv_proxy.html:3
#: application/templates/adv_proxy.html:12
msgid "Proxy"
msgstr "Proxy"

#: application/templates/adv_base.html:92
#: application/templates/adv_base.html:96
#: application/templates/adv_import.html:8
msgid "Import Feeds"
msgstr "Importer les flux"

#: application/templates/adv_base.html:101
#: application/templates/adv_base.html:105
msgid "Cover Image"
msgstr "Image de couverture"

#: application/templates/adv_base.html:110
#: application/templates/adv_base.html:114
#: application/templates/adv_uploadcss.html:3
msgid "Stylesheet"
msgstr "Feuille de style"

#: application/templates/adv_base.html:119
#: application/templates/adv_base.html:123
#: application/templates/adv_calibre_options.html:3
#: application/templates/adv_calibre_options.html:12
msgid "Calibre Options"
msgstr "Options Calibre"

#: application/templates/adv_calibre_options.html:13
msgid "Set the parameters for Calibre, in JSON dictionary format."
msgstr "Définir les paramètres pour Calibre, au format dictionnaire JSON."

#: application/templates/adv_delivernow.html:3
msgid "Deliver now"
msgstr "Livrer maintenant"

#: application/templates/adv_delivernow.html:9
msgid "Deliver selected recipes now."
msgstr "Livrer les recettes sélectionnées maintenant."

#: application/templates/adv_delivernow.html:12
msgid "There are no recipes subscribed"
msgstr "Aucune recette souscrite"

#: application/templates/adv_delivernow.html:17
#: application/templates/base.html:101
msgid "Sep"
msgstr "Sep"

#: application/templates/adv_delivernow.html:22
msgid "Select all"
msgstr "Tout sélectionner"

#: application/templates/adv_delivernow.html:23
msgid "Select none"
msgstr "Sélectionner aucun"

#: application/templates/adv_delivernow.html:28
msgid "Deliver"
msgstr "Livrer"

#: application/templates/adv_dict.html:15
msgid "Set up dictionaries for online reading."
msgstr "Configurer les dictionnaires pour la lecture en ligne."

#: application/templates/adv_dict.html:18
#: application/templates/adv_dict.html:40
#: application/templates/adv_dict.html:62
msgid "Book language"
msgstr "Langue du livre"

#: application/templates/adv_dict.html:27
#: application/templates/adv_dict.html:49
#: application/templates/adv_dict.html:66
#: application/templates/book_summarizer.html:23
#: application/templates/book_translator.html:26
#: application/templates/word_lookup.html:59
msgid "Engine"
msgstr "Moteur"

#: application/templates/adv_dict.html:33
#: application/templates/adv_dict.html:55
#: application/templates/adv_dict.html:72
#: application/templates/word_lookup.html:65
msgid "Database"
msgstr "Base de données"

#: application/templates/adv_dict.html:63
msgid "Other languages"
msgstr "Autres langues"

#: application/templates/adv_dict.html:81
#: application/templates/word_lookup.html:3
#: application/templates/word_lookup.html:79
msgid "Word lookup"
msgstr "Recherche de mots"

#: application/templates/adv_dict.html:86 application/view/reader.py:29
#: application/view/reader.py:86
msgid "Online reading feature has not been activated yet."
msgstr "La fonctionnalité de lecture en ligne n'a pas encore été activée."

#: application/templates/adv_import.html:3
#: application/templates/adv_import.html:19
msgid "Import"
msgstr "Importer"

#: application/templates/adv_import.html:9
msgid "Import custom rss from an OPML file."
msgstr "Importer un RSS personnalisé depuis un fichier OPML."

#: application/templates/adv_import.html:15
msgid "Import as fulltext rss by default"
msgstr "Importer par défaut en RSS texte complet"

#: application/templates/adv_import.html:20
msgid "Download"
msgstr "Télécharger"

#: application/templates/adv_inboundmail.html:11
msgid ""
"To enable the inbound email feature, you also need to configure the "
"whitelist."
msgstr ""
"Pour activer la fonctionnalité de messagerie entrant, vous devez également "
"configurer la liste blanche."

#: application/templates/adv_inboundmail.html:16
#: application/templates/adv_uploadcover.html:15
#: application/templates/book_summarizer.html:19
#: application/templates/book_translator.html:22
#: application/templates/settings.html:143
msgid "Disable"
msgstr "Désactiver"

#: application/templates/adv_inboundmail.html:17
msgid "Forward Only"
msgstr "Transférer uniquement"

#: application/templates/adv_inboundmail.html:18
msgid "Save Only"
msgstr "Enregistrer uniquement"

#: application/templates/adv_inboundmail.html:19
msgid "Save and Forward"
msgstr "Enregistrer et transférer"

#: application/templates/adv_inboundmail.html:23
msgid "Email Retention"
msgstr "Conservation des emails"

#: application/templates/adv_inboundmail.html:25
#: application/templates/settings.html:52
#: application/templates/settings.html:175
msgid "1 Day"
msgstr "1 jour"

#: application/templates/adv_inboundmail.html:27
#: application/templates/settings.html:59
msgid "30 Days"
msgstr "30 jours"

#: application/templates/adv_inboundmail.html:28
#: application/templates/settings.html:51
#: application/templates/settings.html:174
msgid "No limit"
msgstr "Aucune limite"

#: application/templates/adv_inboundmail.html:36
msgid "Open webmail"
msgstr "Ouvrir le webmail"

#: application/templates/adv_inboundmail.html:44
msgid "White List"
msgstr "Liste blanche"

#: application/templates/adv_inboundmail.html:46
#, python-format
msgid ""
"Emails sent to %(name)sxxx@%(mailHost)s will be forwarded to your kindle "
"email."
msgstr ""
"Les emails envoyés à %(name)sxxx@%(mailHost)s seront transférés vers votre "
"adresse email Kindle."

#: application/templates/adv_inboundmail.html:47
msgid "Example"
msgstr "Exemple"

#: application/templates/adv_inboundmail.html:59
msgid "Please input mail address"
msgstr "Veuillez saisir l'adresse email"

#: application/templates/adv_proxy.html:13
msgid "Supports"
msgstr "Prend en charge"

#: application/templates/adv_proxy.html:24
#: application/templates/adv_proxy.html:29
#: application/templates/book_audiolator.html:132
#: application/templates/book_summarizer.html:105
#: application/templates/book_translator.html:97
msgid "Test"
msgstr "Tester"

#: application/templates/adv_uploadcover.html:3
msgid "Cover image"
msgstr "Image de couverture"

#: application/templates/adv_uploadcover.html:9
msgid "Upload cover image"
msgstr "Télécharger l'image de couverture"

#: application/templates/adv_uploadcover.html:10
msgid ""
"Upload cover images from local with an aspect ratio of approximately 0.625."
msgstr ""
"Télécharger des images de couverture depuis votre appareil avec un rapport "
"d'aspect d'environ 0.625."

#: application/templates/adv_uploadcover.html:13
msgid "Include cover"
msgstr "Inclure la couverture"

#: application/templates/adv_uploadcover.html:16
#: application/templates/book_summarizer.html:18
#: application/templates/book_translator.html:21
msgid "Enable"
msgstr "Activer"

#: application/templates/adv_uploadcover.html:20
msgid "Rule for cover"
msgstr "Règle pour la couverture"

#: application/templates/adv_uploadcover.html:22
msgid "Random"
msgstr "Aléatoire"

#: application/templates/adv_uploadcover.html:23
#: application/templates/base.html:127
msgid "Weekday"
msgstr "Jour de la semaine"

#: application/templates/adv_uploadcover.html:49
msgid "Upload/Update"
msgstr "Télécharger/Mise à jour"

#: application/templates/adv_uploadcss.html:22
msgid "Upload stylesheet"
msgstr "Télécharger la feuille de style"

#: application/templates/adv_uploadcss.html:23
msgid "Upload a stylesheet from local (accept utf-8 only)."
msgstr ""
"Téléchargez une feuille de style depuis votre appareil (seul le format UTF-8"
" est accepté)."

#: application/templates/adv_uploadcss.html:30
msgid "Upload"
msgstr "Télécharger"

#: application/templates/autoback.html:3
msgid "Auto back"
msgstr "Retour automatique"

#: application/templates/autoback.html:28
msgid "Auto back to previous page after 5 seconds"
msgstr "Retour automatique à la page précédente après 5 secondes"

#: application/templates/autoback.html:29
#: application/templates/tipsback.html:15
msgid "Click to back"
msgstr "Cliquez pour revenir"

#: application/templates/base.html:24 application/templates/reader.html:189
msgid "Confirm Deletion"
msgstr "Confirmer la suppression"

#: application/templates/base.html:26
msgid "Delete (Ctrl for no confirm)"
msgstr "Supprimer (Ctrl pour ne pas confirmer)"

#: application/templates/base.html:27
msgid "View Source Code"
msgstr "Voir le code source"

#: application/templates/base.html:28
msgid "Subscribe (Deliver Separately)"
msgstr "S'abonner (Livraison séparée)"

#: application/templates/base.html:29
msgid "Subscribe"
msgstr "S'abonner"

#: application/templates/base.html:30
msgid "Cannot add this custom rss, Error:"
msgstr "Impossible d'ajouter ce RSS personnalisé, erreur :"

#: application/templates/base.html:31
msgid "Cannot delete this feed, Error:"
msgstr "Impossible de supprimer ce flux, erreur :"

#: application/templates/base.html:32
msgid "Fulltext"
msgstr "Texte complet"

#: application/templates/base.html:33 application/templates/base.html:43
msgid "Share"
msgstr "Partager"

#: application/templates/base.html:34 application/templates/reader.html:190
msgid "Are you sure to delete?"
msgstr "Êtes-vous sûr de vouloir supprimer ?"

#: application/templates/base.html:35
msgid "Report to the server that this feed is invalid."
msgstr "Signaler au serveur que ce flux est invalide."

#: application/templates/base.html:36
msgid "Are you sure to REMOVE ALL CUSTOM RSS?"
msgstr "Êtes-vous sûr de vouloir SUPPRIMER TOUS LES RSS PERSONNALISÉS ?"

#: application/templates/base.html:37
msgid "Share links, share happiness"
msgstr "Partager des liens, partager du bonheur"

#: application/templates/base.html:38
msgid "Category"
msgstr "Catégorie"

#: application/templates/base.html:39
#: application/templates/book_audiolator.html:58
#: application/templates/book_summarizer.html:43
#: application/templates/settings.html:159
msgid "Language"
msgstr "Langue"

#: application/templates/base.html:40
msgid ""
"Please write a category in text field if the one you wish is not in the "
"list."
msgstr ""
"Veuillez écrire une catégorie dans le champ de texte si celle que vous "
"souhaitez n'est pas dans la liste."

#: application/templates/base.html:41
msgid "Ok"
msgstr "D'accord"

#: application/templates/base.html:42
msgid "Cancel"
msgstr "Annuler"

#: application/templates/base.html:44
msgid "Language code invalid"
msgstr "Code de langue invalide"

#: application/templates/base.html:45
msgid "Thank you for sharing."
msgstr "Merci de partager."

#: application/templates/base.html:46 application/templates/reader.html:159
msgid "Close"
msgstr "Fermer"

#: application/templates/base.html:47
msgid "Unsubscribe"
msgstr "Se désabonner"

#: application/templates/base.html:48
msgid "Cannot subscribe this recipe, Error:"
msgstr "Impossible de s'abonner à cette recette, erreur :"

#: application/templates/base.html:49
msgid "Are you sure to Unsubscribe ({0})?"
msgstr "Êtes-vous sûr de vous désabonner ({0}) ?"

#: application/templates/base.html:50
msgid "Cannot unsubscribe this recipe, Error:"
msgstr "Impossible de se désabonner de cette recette, Erreur :"

#: application/templates/base.html:51
msgid "The recipe is already subscribed."
msgstr "La recette est déjà abonnée."

#: application/templates/base.html:52
msgid "Website login lnformation"
msgstr "Informations de connexion au site"

#: application/templates/base.html:55
msgid "Submit"
msgstr "Soumettre"

#: application/templates/base.html:56
msgid ""
"If any field is left blank, the server will clear the saved login "
"information."
msgstr ""
"Si un champ est laissé vide, le serveur effacera les informations de "
"connexion enregistrées."

#: application/templates/base.html:57
msgid "Cannot set the website login information, Error:"
msgstr "Impossible de définir les informations de connexion au site, Erreur :"

#: application/templates/base.html:58 application/templates/my.html:58
msgid "Upload custom recipe"
msgstr "Télécharger une recette personnalisée"

#: application/templates/base.html:59
msgid "Congratulations"
msgstr "Félicitations"

#: application/templates/base.html:60
msgid "Thanks"
msgstr "Merci"

#: application/templates/base.html:61
msgid ""
"Your recipe has been uploaded, and it can be found in the Library section. "
"If you dont see it, please make sure to switch to the correct language."
msgstr ""
"Votre recette a été téléchargée et peut être trouvée dans la section "
"Bibliothèque. Si vous ne la voyez pas, assurez-vous de passer à la langue "
"correcte."

#: application/templates/base.html:62
msgid "Your recipe have been deleted."
msgstr "Votre recette a été supprimée."

#: application/templates/base.html:63
msgid "Kindleify Selection"
msgstr "Sélection Kindleify"

#: application/templates/base.html:65
msgid "Verified"
msgstr "Vérifié"

#: application/templates/base.html:66 application/view/login.py:79
#: application/view/share.py:157
msgid "The username does not exist or password is wrong."
msgstr "Le nom d'utilisateur n'existe pas ou le mot de passe est incorrect."

#: application/templates/base.html:67
msgid "The file you chosen is not an acceptable type."
msgstr "Le fichier que vous avez choisi n'est pas un type acceptable."

#: application/templates/base.html:68
msgid "The file have been uploaded successfully."
msgstr "Le fichier a été téléchargé avec succès."

#: application/templates/base.html:69 application/templates/library.html:67
msgid "This feed has been successfully subscribed."
msgstr "Ce flux a été abonné avec succès."

#: application/templates/base.html:70
msgid "Thank you for your feedback, this feed will be reviewed soon."
msgstr "Merci pour vos commentaires, ce flux sera examiné bientôt."

#: application/templates/base.html:71
msgid "Are you confirming to share the recipe ({0})?"
msgstr "Confirmez-vous partager la recette ({0}) ?"

#: application/templates/base.html:72
msgid "[All]"
msgstr "[Tous]"

#: application/templates/base.html:73
msgid "[By Time]"
msgstr "[Par temps]"

#: application/templates/base.html:74
msgid "[Random]"
msgstr "[Aléatoire]"

#: application/templates/base.html:75
msgid "[Uncategoried]"
msgstr "[Non catégorisé]"

#: application/templates/base.html:76
msgid "There are no links found."
msgstr "Aucun lien trouvé."

#: application/templates/base.html:77
msgid "Invalid report"
msgstr "Rapport invalide"

#: application/templates/base.html:78
msgid "Are you confirming that this link is invalid or off the cloud?"
msgstr "Confirmez-vous que ce lien est invalide ou hors du cloud ?"

#: application/templates/base.html:79
msgid "Customize delivery time"
msgstr "Personnaliser l'heure de livraison"

#: application/templates/base.html:80 application/templates/settings.html:72
msgid "Delivery days"
msgstr "Jours de livraison"

#: application/templates/base.html:81 application/templates/settings.html:74
msgid "Mon"
msgstr "Lun"

#: application/templates/base.html:82 application/templates/settings.html:76
msgid "Tue"
msgstr "Mar"

#: application/templates/base.html:83 application/templates/settings.html:78
msgid "Wed"
msgstr "Mer"

#: application/templates/base.html:84 application/templates/settings.html:80
msgid "Thu"
msgstr "Jeu"

#: application/templates/base.html:85 application/templates/settings.html:82
msgid "Fri"
msgstr "Ven"

#: application/templates/base.html:86 application/templates/settings.html:84
msgid "Sat"
msgstr "Sam"

#: application/templates/base.html:87 application/templates/settings.html:86
msgid "Sun"
msgstr "Dim"

#: application/templates/base.html:88
msgid "Delivery times"
msgstr "Heures de livraison"

#: application/templates/base.html:89
msgid ""
"The customized delivery time for the recipe has been successfully saved."
msgstr ""
"L'heure de livraison personnalisée pour la recette a été enregistrée avec "
"succès."

#: application/templates/base.html:90
msgid "The account have been deleted."
msgstr "Le compte a été supprimé."

#: application/templates/base.html:91 application/view/share.py:147
msgid "The username or password is empty."
msgstr "Le nom d'utilisateur ou le mot de passe est vide."

#: application/templates/base.html:92 application/view/admin.py:81
#: application/view/admin.py:165 application/view/admin.py:191
#: application/view/login.py:220 application/view/login.py:273
msgid "The two new passwords are dismatch."
msgstr "Les deux nouveaux mots de passe ne correspondent pas."

#: application/templates/base.html:93
msgid "Password changed successfully."
msgstr "Mot de passe changé avec succès."

#: application/templates/base.html:94
msgid "Account added successfully."
msgstr "Compte ajouté avec succès."

#: application/templates/base.html:95 application/view/login.py:128
msgid "login required"
msgstr "connexion requise"

#: application/templates/base.html:96
msgid "Upload cover files successfully."
msgstr "Fichiers de couverture téléchargés avec succès."

#: application/templates/base.html:97
msgid ""
"Total size of the files you selected exceeds 16MB. Please reduce the image "
"resolution or upload in batches."
msgstr ""
"La taille totale des fichiers que vous avez sélectionnés dépasse 16 Mo. "
"Veuillez réduire la résolution de l'image ou télécharger en plusieurs fois."

#: application/templates/base.html:98
#: application/templates/book_translator.html:3
#: application/templates/book_translator.html:17
msgid "Bilingual Translator"
msgstr "Traducteur bilingue"

#: application/templates/base.html:99
#: application/templates/book_summarizer.html:3
#: application/templates/book_summarizer.html:14
msgid "AI Summarizer"
msgstr "Résumé AI"

#: application/templates/base.html:100
msgid "Upl"
msgstr "Upl"

#: application/templates/base.html:102
msgid "Log"
msgstr "Log"

#: application/templates/base.html:103
msgid "Emb"
msgstr "Emb"

#: application/templates/base.html:104
msgid "Tr"
msgstr "Tr"

#: application/templates/base.html:105
msgid "Tts"
msgstr "Tts"

#: application/templates/base.html:106
msgid "Ai"
msgstr "Ai"

#: application/templates/base.html:107
msgid ""
"The test email has been successfully sent to the following addresses. Please"
" check your inbox or spam folder to confirm its delivery. Depending on your "
"email server, there may be a slight delay."
msgstr ""
"L'email de test a été envoyé avec succès aux adresses suivantes. Veuillez "
"vérifier votre boîte de réception ou votre dossier spam pour confirmer sa "
"livraison. Selon votre serveur de messagerie, il peut y avoir un léger "
"délai."

#: application/templates/base.html:108
msgid "Processing..."
msgstr "Traitement en cours..."

#: application/templates/base.html:109
msgid "The configuration validation is correct."
msgstr "La validation de la configuration est correcte."

#: application/templates/base.html:110 application/templates/logs.html:23
#: application/templates/logs.html:72 application/templates/my.html:17
#: application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/settings.html:155
msgid "Title"
msgstr "Titre"

#: application/templates/base.html:111
#: application/templates/book_audiolator.html:3
#: application/templates/book_audiolator.html:20
msgid "Text to Speech"
msgstr "Texte en parole"

#: application/templates/base.html:112
msgid "Action"
msgstr "Action"

#: application/templates/base.html:113
msgid "File"
msgstr "Fichier"

#: application/templates/base.html:114
msgid "Upload Only"
msgstr "Téléchargement uniquement"

#: application/templates/base.html:115
msgid "Send"
msgstr "Envoyer"

#: application/templates/base.html:116 application/templates/logs.html:54
msgid "There is nothing here."
msgstr "Il n'y a rien ici."

#: application/templates/base.html:117
msgid "Please select a single item."
msgstr "Veuillez sélectionner un seul élément."

#: application/templates/base.html:118 application/templates/reader.html:191
msgid "Please select at least one item."
msgstr "Veuillez sélectionner au moins un élément."

#: application/templates/base.html:119 application/view/admin.py:77
#: application/view/admin.py:152 application/view/admin.py:189
#: application/view/adv.py:459 application/view/extension.py:34
#: application/view/extension.py:63 application/view/inbound_email.py:470
#: application/view/inbound_email.py:478 application/view/inbound_email.py:493
#: application/view/inbound_email.py:514 application/view/login.py:216
#: application/view/login.py:245 application/view/reader.py:109
#: application/view/reader.py:126 application/view/share.py:37
msgid "Some parameters are missing or wrong."
msgstr "Certains paramètres sont manquants ou incorrects."

#: application/templates/base.html:120
msgid "The email has been sent."
msgstr "L'email a été envoyé."

#: application/templates/base.html:121 application/templates/webmail.html:29
msgid "From"
msgstr "De"

#: application/templates/base.html:122 application/templates/logs.html:25
#: application/templates/logs.html:74 application/templates/webmail.html:30
msgid "To"
msgstr "À"

#: application/templates/base.html:123 application/templates/webmail.html:31
msgid "Subject"
msgstr "Sujet"

#: application/templates/base.html:124 application/templates/logs.html:22
#: application/templates/logs.html:71 application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/webmail.html:32
msgid "Time"
msgstr "Temps"

#: application/templates/base.html:125 application/templates/logs.html:24
#: application/templates/logs.html:73 application/templates/webmail.html:33
msgid "Size"
msgstr "Taille"

#: application/templates/base.html:126
msgid "Date type"
msgstr "Type de date"

#: application/templates/base.html:128
msgid "Date"
msgstr "Date"

#: application/templates/base.html:129
msgid "This setting is prioritized."
msgstr "Ce paramètre est prioritaire."

#: application/templates/base.html:130
msgid "Combine multiple values with commas."
msgstr "Combinez plusieurs valeurs avec des virgules."

#: application/templates/base.html:131
msgid "Put dictionary in dict folder"
msgstr "Placez le dictionnaire dans le dossier dict"

#: application/templates/base.html:156 application/templates/home.html:16
msgid "Logout"
msgstr "Quitter"

#: application/templates/base.html:158 application/templates/home.html:21
#: application/templates/login.html:3 application/templates/login.html:22
#: application/templates/login.html:33
msgid "Login"
msgstr "Connexion"

#: application/templates/base.html:160 application/templates/signup.html:3
#: application/templates/signup.html:19 application/templates/signup.html:43
msgid "Signup"
msgstr "S'inscrire"

#: application/templates/base.html:189 application/templates/home.html:15
#: application/templates/my.html:3
msgid "Feeds"
msgstr "Flux"

#: application/templates/base.html:190 application/templates/settings.html:3
msgid "Settings"
msgstr "Options"

#: application/templates/base.html:191 application/templates/logs.html:3
msgid "Logs"
msgstr "Logs"

#: application/templates/base.html:193
msgid "Advanced"
msgstr "Avancé"

#: application/templates/base.html:194 application/templates/library.html:3
msgid "Shared"
msgstr "Partagé"

#: application/templates/base.html:195 application/templates/reader.html:6
msgid "Reader"
msgstr "Lecteur"

#: application/templates/book_audiolator.html:22
#: application/templates/book_summarizer.html:16
#: application/templates/book_translator.html:19
msgid "State"
msgstr "État"

#: application/templates/book_audiolator.html:24
msgid "Send Ebook and Audio"
msgstr "Envoyer Ebook et Audio"

#: application/templates/book_audiolator.html:25
msgid "Send Audio only"
msgstr "Envoyer uniquement l'audio"

#: application/templates/book_audiolator.html:26
msgid "Disable TTS"
msgstr "Désactiver la synthèse vocale"

#: application/templates/book_audiolator.html:30
msgid "Send Audio To"
msgstr "Envoyer l'audio à"

#: application/templates/book_audiolator.html:31
msgid "Empty to use Kindle_email"
msgstr "Laissez vide pour utiliser Kindle_email"

#: application/templates/book_audiolator.html:35
msgid "TTS Engine"
msgstr "Moteur de synthèse vocale"

#: application/templates/book_audiolator.html:41
#: application/templates/book_summarizer.html:35
#: application/templates/book_translator.html:32
msgid "Api Host"
msgstr "Hôte API"

#: application/templates/book_audiolator.html:42
#: application/templates/book_summarizer.html:36
#: application/templates/book_summarizer.html:75
msgid "Leave empty to use default"
msgstr "Laissez vide pour utiliser la valeur par défaut"

#: application/templates/book_audiolator.html:46
msgid "Region"
msgstr "Région"

#: application/templates/book_audiolator.html:53
#: application/templates/book_summarizer.html:39
#: application/templates/book_translator.html:36
msgid "Api Key"
msgstr "Api Key"

#: application/templates/book_audiolator.html:66
msgid "Voice name"
msgstr "Nom de la voix"

#: application/templates/book_audiolator.html:73
msgid "Voice speed"
msgstr "Vitesse de la voix"

#: application/templates/book_audiolator.html:75
msgid "Extra slow"
msgstr "Très lent"

#: application/templates/book_audiolator.html:76
msgid "Slow"
msgstr "Lent"

#: application/templates/book_audiolator.html:77
#: application/templates/book_audiolator.html:87
#: application/templates/book_audiolator.html:97
msgid "Medium"
msgstr "Moyenne"

#: application/templates/book_audiolator.html:78
msgid "Fast"
msgstr "Rapide"

#: application/templates/book_audiolator.html:79
msgid "Extra fast"
msgstr "Très rapide"

#: application/templates/book_audiolator.html:83
msgid "Voice pitch"
msgstr "Hauteur de la voix"

#: application/templates/book_audiolator.html:85
msgid "Extra low"
msgstr "Très bas"

#: application/templates/book_audiolator.html:86
msgid "Low"
msgstr "Bas"

#: application/templates/book_audiolator.html:88
msgid "High"
msgstr "Haut"

#: application/templates/book_audiolator.html:89
msgid "Extra high"
msgstr "Très haut"

#: application/templates/book_audiolator.html:93
msgid "Voice volume"
msgstr "Volume de la voix"

#: application/templates/book_audiolator.html:95
msgid "Extra soft"
msgstr "Très doux"

#: application/templates/book_audiolator.html:96
msgid "Soft"
msgstr "Doux"

#: application/templates/book_audiolator.html:98
msgid "Loud"
msgstr "Fort"

#: application/templates/book_audiolator.html:99
msgid "Extra loud"
msgstr "Très fort"

#: application/templates/book_audiolator.html:105
#: application/templates/book_summarizer.html:80
#: application/templates/book_translator.html:72
msgid "Apply to all subscribed recipes"
msgstr "Appliquer à toutes les recettes abonnées"

#: application/templates/book_audiolator.html:110
#: application/templates/book_summarizer.html:85
#: application/templates/book_translator.html:77
msgid ""
"Note: Enabling this feature will significantly increase consumed CPU "
"instance hours."
msgstr ""
"Remarque : activer cette fonctionnalité augmentera considérablement les "
"heures de CPU consommées."

#: application/templates/book_audiolator.html:119
#: application/templates/book_summarizer.html:94
#: application/templates/book_translator.html:86
msgid "Test (Please save settings firstly)"
msgstr "Tester (Veuillez d'abord enregistrer les paramètres)"

#: application/templates/book_audiolator.html:121
#: application/templates/book_summarizer.html:96
#: application/templates/book_translator.html:88
msgid "Text"
msgstr "Texte"

#: application/templates/book_audiolator.html:127
msgid "Your browser does not support the audio element."
msgstr "Votre navigateur ne prend pas en charge l'élément audio."

#: application/templates/book_summarizer.html:29
msgid "Model"
msgstr "Model"

#: application/templates/book_summarizer.html:45
msgid "Auto"
msgstr "Automatique"

#: application/templates/book_summarizer.html:56
msgid "Summary words"
msgstr "Mots du résumé"

#: application/templates/book_summarizer.html:70
msgid "Summary style"
msgstr "Style de résumé"

#: application/templates/book_summarizer.html:74
msgid "Placeholders available:"
msgstr "Espaces réservés disponibles :"

#: application/templates/book_summarizer.html:74
msgid "Custom prompt"
msgstr "Invite personnalisée"

#: application/templates/book_summarizer.html:100
msgid "Summary"
msgstr "Résumé"

#: application/templates/book_translator.html:37
msgid "One key per line"
msgstr "Une touche par ligne"

#: application/templates/book_translator.html:40
#: application/templates/word_lookup.html:51
msgid "Source language"
msgstr "Langue source"

#: application/templates/book_translator.html:46
msgid "Target language"
msgstr "Langue cible"

#: application/templates/book_translator.html:52
msgid "Translation Position"
msgstr "Position de la traduction"

#: application/templates/book_translator.html:54
msgid "Below original"
msgstr "Sous l'original"

#: application/templates/book_translator.html:55
msgid "Above original"
msgstr "Au-dessus de l'original"

#: application/templates/book_translator.html:56
msgid "Left to original"
msgstr "À gauche de l'original"

#: application/templates/book_translator.html:57
msgid "Right to original"
msgstr "À droite de l'original"

#: application/templates/book_translator.html:58
msgid "Translated text only"
msgstr "Texte traduit uniquement"

#: application/templates/book_translator.html:62
msgid "Original text style"
msgstr "Style du texte original"

#: application/templates/book_translator.html:66
msgid "Translated text style"
msgstr "Style du texte traduit"

#: application/templates/book_translator.html:92
msgid "Translation"
msgstr "Traduction"

#: application/templates/change_password.html:15
msgid "Old password"
msgstr "Ancien mot de passe"

#: application/templates/change_password.html:19
#: application/templates/reset_password.html:31
#: application/templates/reset_password.html:32
msgid "New password"
msgstr "Nouveau mot de passe"

#: application/templates/change_password.html:23
#: application/templates/reset_password.html:35
#: application/templates/reset_password.html:36
#: application/templates/signup.html:29
#: application/templates/user_account.html:23
msgid "Confirm password"
msgstr "Confirmer le mot de passe"

#: application/templates/change_password.html:31
msgid "Share key"
msgstr "Clé de partage"

#: application/templates/change_password.html:37
msgid "Confirm Change"
msgstr "Confirmer le changement"

#: application/templates/debug_cmd.html:3
msgid "Debug cmd"
msgstr "Commande de débogage"

#: application/templates/home.html:3
msgid "Home"
msgstr "Accueil"

#: application/templates/home.html:10 application/templates/login.html:18
msgid "You are in DEMO mode. Logging out will delete all data."
msgstr "Vous êtes en mode DEMO. Se déconnecter supprimera toutes les données."

#: application/templates/home.html:12
msgid "Sharing Joyful News Every Step of the Way"
msgstr "Partager des nouvelles joyeuses à chaque étape"

#: application/templates/home.html:31
msgid "Inherited From Calibre"
msgstr "Hérité de Calibre"

#: application/templates/home.html:34
#, python-format
msgid ""
"Empowered by %(calibre)s, you can easily create e-books on a Python-"
"supported online platform and seamlessly transfer them to your e-reader or "
"other reading devices."
msgstr ""
"Grâce à %(calibre)s, vous pouvez facilement créer des livres électroniques "
"sur une plateforme en ligne compatible Python et les transférer sans effort "
"sur votre liseuse ou d'autres appareils de lecture."

#: application/templates/home.html:42
msgid "Share Your Ideas"
msgstr "Partagez vos idées"

#: application/templates/home.html:45
#, python-format
msgid ""
"With the open-source %(kindleear)s application, you can set up your own "
"server to deliver daily news feeds to your e-reader and effortlessly share "
"the service with friends."
msgstr ""
"Avec l'application open-source %(kindleear)s, vous pouvez configurer votre "
"propre serveur pour fournir des flux d'actualités quotidiens à votre liseuse"
" et partager facilement le service avec vos amis."

#: application/templates/library.html:50 application/templates/my.html:61
msgid "Search"
msgstr "Rechercher"

#: application/templates/login.html:38 application/view/login.py:197
#: application/view/login.py:204
msgid ""
"The website does not allow registration. You can ask the owner for an "
"account."
msgstr ""
"Le site web ne permet pas l'inscription. Vous pouvez demander un compte au "
"propriétaire."

#: application/templates/logs.html:11
msgid "Only display last 20 logs"
msgstr "Afficher uniquement les 20 derniers journaux"

#: application/templates/logs.html:26 application/templates/logs.html:75
msgid "Status"
msgstr "Statut"

#: application/templates/logs.html:58
msgid "Logs of other users"
msgstr "Journaux des autres utilisateurs"

#: application/templates/my.html:12 application/templates/settings.html:153
msgid "Custom RSS"
msgstr "RSS personnalisé"

#: application/templates/my.html:23
msgid "Content Embedded"
msgstr "Contenu intégré"

#: application/templates/my.html:27
msgid "Deliver Separately"
msgstr "Livrer séparément"

#: application/templates/my.html:46
msgid "Subscribed"
msgstr "Abonné"

#: application/templates/my.html:51
msgid "Library"
msgstr "Bibliothèque"

#: application/templates/my.html:51
msgid "get more recipes"
msgstr "obtenir plus de recettes"

#: application/templates/my.html:68
msgid "Subscription to selected recipe successful."
msgstr "Abonnement à la recette sélectionnée réussi."

#: application/templates/my.html:71
msgid "Bookmarklet"
msgstr "Bookmarklet"

#: application/templates/my.html:75
msgid "Send to Kindle"
msgstr "Envoyer à Kindle"

#: application/templates/my.html:79
msgid "Subscribe with KindleEar"
msgstr "S'abonner avec KindleEar"

#: application/templates/my.html:82
msgid "Drag and drop this link to your bookmarks"
msgstr "Faites glisser ce lien vers vos favoris"

#: application/templates/my.html:86
msgid "Chrome extension"
msgstr "Extension Chrome"

#: application/templates/my.html:87
msgid "Edge extension"
msgstr "Extension Edge"

#: application/templates/my.html:89
msgid "Browser extensions also available"
msgstr "Extensions de navigateur également disponibles"

#: application/templates/reader.html:57
msgid "Push current book"
msgstr "Envoyer le livre actuel"

#: application/templates/reader.html:63
msgid "Push current article"
msgstr "Envoyer l'article actuel"

#: application/templates/reader.html:71
msgid "Delete selected books"
msgstr "Supprimer les livres sélectionnés"

#: application/templates/reader.html:77
msgid "Allow click links"
msgstr "Autoriser les liens cliquables"

#: application/templates/reader.html:83
msgid "Top-left dict mode"
msgstr "Mode dictionnaire en haut à gauche"

#: application/templates/reader.html:89
msgid "Dark mode"
msgstr "Mode sombre"

#: application/templates/reader.html:95
msgid "eInk mode"
msgstr "Mode eInk"

#: application/templates/reader.html:101
msgid "Increase font size"
msgstr "Augmenter la taille de la police"

#: application/templates/reader.html:107
msgid "Decrease font size"
msgstr "Réduire la taille de la police"

#: application/templates/reader.html:113
msgid "Visualize Touch Regions"
msgstr "Visualiser les régions tactiles"

#: application/templates/reader.html:119
msgid "Help"
msgstr "Aide"

#: application/templates/reader.html:125
#: application/templates/reader_404.html:135
msgid "Menu"
msgstr "Menu"

#: application/templates/reader.html:139
msgid "Collapse all"
msgstr "Tout réduire"

#: application/templates/reader.html:144
msgid "Expand all"
msgstr "Tout développer"

#: application/templates/reader.html:149
#: application/templates/reader_404.html:140
msgid "Prev"
msgstr "Précédent"

#: application/templates/reader.html:154
#: application/templates/reader_404.html:143
msgid "Next page"
msgstr "Page suivante"

#: application/templates/reader.html:192
msgid "Pushed successfully."
msgstr "Envoyé avec succès."

#: application/templates/reader.html:193
msgid "There are currently no books or articles being read."
msgstr "Aucun livre ni article n'est actuellement en cours de lecture."

#: application/templates/reset_password.html:3
#: application/templates/reset_password.html:41
msgid "Reset password"
msgstr "Réinitialiser le mot de passe"

#: application/templates/settings.html:14
msgid ""
"Your account will pause after {0}, please log in again before it expires."
msgstr ""
"Votre compte sera mis en pause après {0}, veuillez vous reconnecter avant "
"son expiration."

#: application/templates/settings.html:23
msgid "Base"
msgstr "Base"

#: application/templates/settings.html:25
msgid "Auto delivery"
msgstr "Livraison automatique"

#: application/templates/settings.html:28
msgid "Recipes and custom RSS"
msgstr "Recettes et RSS personnalisé"

#: application/templates/settings.html:29
msgid "Recipes only"
msgstr "Recettes uniquement"

#: application/templates/settings.html:30
msgid "Disable all"
msgstr "Désactiver tout"

#: application/templates/settings.html:34
msgid "Kindle E-mail"
msgstr "E-mail Kindle"

#: application/templates/settings.html:35
msgid "Seperated by comma"
msgstr "Séparé par des virgules"

#: application/templates/settings.html:39
msgid "Delivery mode"
msgstr "Mode de livraison"

#: application/templates/settings.html:42
msgid "Email delivery & online reading"
msgstr "Livraison par e-mail et lecture en ligne"

#: application/templates/settings.html:43
msgid "Email delivery"
msgstr "Livraison par e-mail"

#: application/templates/settings.html:44
msgid "Online reading"
msgstr "Lecture en ligne"

#: application/templates/settings.html:48
msgid "Retention days for online books"
msgstr "Jours de rétention pour les livres en ligne"

#: application/templates/settings.html:48
msgid "Web shelf"
msgstr "Étagère web"

#: application/templates/settings.html:53
#: application/templates/settings.html:176
msgid "2 Days"
msgstr "2 jours"

#: application/templates/settings.html:54
#: application/templates/settings.html:177
msgid "3 Days"
msgstr "3 jours"

#: application/templates/settings.html:55
#: application/templates/settings.html:178
msgid "4 Days"
msgstr "4 jours"

#: application/templates/settings.html:56
#: application/templates/settings.html:179
msgid "5 Days"
msgstr "5 jours"

#: application/templates/settings.html:57
#: application/templates/settings.html:180
msgid "6 Days"
msgstr "6 jours"

#: application/templates/settings.html:64
msgid "Time zone"
msgstr "Fuseau horaire"

#: application/templates/settings.html:89
msgid "Delivery time"
msgstr "Heure de livraison"

#: application/templates/settings.html:97
msgid "Book type"
msgstr "Type de livre"

#: application/templates/settings.html:104
msgid "Determines final file size"
msgstr "Détermine la taille finale du fichier"

#: application/templates/settings.html:104
msgid "Device type"
msgstr "Type d'appareil"

#: application/templates/settings.html:113
msgid "Title format"
msgstr "Format du titre"

#: application/templates/settings.html:116
msgid "Title Only"
msgstr "Titre uniquement"

#: application/templates/settings.html:130
msgid "Remove hyperlinks"
msgstr "Supprimer les liens hypertextes"

#: application/templates/settings.html:133
msgid "Do not remove hyperlinks"
msgstr "Ne pas supprimer les liens hypertextes"

#: application/templates/settings.html:134
msgid "Remove image links"
msgstr "Supprimer les liens d'images"

#: application/templates/settings.html:135
msgid "Remove text links"
msgstr "Supprimer les liens textuels"

#: application/templates/settings.html:136
msgid "Remove all hyperlinks"
msgstr "Supprimer tous les liens hypertextes"

#: application/templates/settings.html:140
msgid "Navbar"
msgstr "Barre de navigation"

#: application/templates/settings.html:144
msgid "Top Center"
msgstr "Haut centre"

#: application/templates/settings.html:145
msgid "Top Left"
msgstr "Haut gauche"

#: application/templates/settings.html:146
msgid "Bottom Center"
msgstr "Bas centre"

#: application/templates/settings.html:147
msgid "Bottom Left"
msgstr "Bas gauche"

#: application/templates/settings.html:159
msgid "Sets the lookup dictionary"
msgstr "Définit le dictionnaire de recherche"

#: application/templates/settings.html:171
msgid "Oldest article"
msgstr "Article le plus ancien"

#: application/templates/settings.html:185
msgid "Time format"
msgstr "Format de l'heure"

#: application/templates/settings.html:198
msgid "Author format"
msgstr "Format de l'auteur"

#: application/templates/settings.html:215
msgid "Send Mail Service"
msgstr "Service d'envoi de mail"

#: application/templates/settings.html:217
msgid "Service"
msgstr "Service"

#: application/templates/settings.html:225
msgid "ApiKey"
msgstr "ApiKey"

#: application/templates/settings.html:229
msgid "SecretKey"
msgstr "SecretKey"

#: application/templates/settings.html:237
msgid "Port"
msgstr "Port"

#: application/templates/settings.html:249
msgid "Save path"
msgstr "Chemin de sauvegarde"

#: application/templates/settings.html:257
#, python-format
msgid ""
"Important: Please activate your kindle firstly, then goto %(personal)s Page "
"and add %(sender)s to 'Approved Personal Document E-mail List'."
msgstr ""
"Important : Veuillez d'abord activer votre Kindle, puis allez sur la page "
"%(personal)s et ajoutez %(sender)s à la liste des e-mails de documents "
"personnels approuvés."

#: application/templates/settings.html:257
msgid "Personal Document Settings"
msgstr "Paramètres des documents personnels"

#: application/templates/settings.html:263
#, python-format
msgid ""
"You have not yet set up your email address. Please go to the %(admin)s page "
"to add your email address firstly."
msgstr ""
"Vous n'avez pas encore configuré votre adresse e-mail. Veuillez d'abord "
"aller sur la page %(admin)s pour ajouter votre adresse e-mail."

#: application/templates/settings.html:271
msgid "Send Test Email"
msgstr "Envoyer un e-mail de test"

#: application/templates/signup.html:38
msgid "Invitation code"
msgstr "Code d'invitation"

#: application/templates/user_account.html:3
msgid "User account"
msgstr "Compte utilisateur"

#: application/templates/user_account.html:42
msgid "Never expire"
msgstr "Jamais expirer"

#: application/templates/webmail.html:3
msgid "Webmail"
msgstr "Webmail"

#: application/templates/webmail.html:17
msgid "Refresh"
msgstr "Actualiser"

#: application/templates/webmail.html:18
msgid "Read/Unread"
msgstr "Lu/Non lu"

#: application/templates/webmail.html:19
msgid "Reply"
msgstr "Répondre"

#: application/templates/webmail.html:20
msgid "Forward"
msgstr "Transférer"

#: application/templates/webmail.html:21
msgid "Fwd as Attach"
msgstr "Transférer en pièce jointe"

#: application/templates/word_lookup.html:71
msgid "Word"
msgstr "Mot"

#: application/view/admin.py:48 application/view/adv.py:437
#: application/view/adv.py:528 application/view/settings.py:67
#: application/view/translator.py:88 application/view/translator.py:172
#: application/view/translator.py:254
msgid "Settings Saved!"
msgstr "Paramètres enregistrés !"

#: application/view/admin.py:57 application/view/admin.py:64
#: application/view/admin.py:91
msgid "Add account"
msgstr "Ajouter un compte"

#: application/view/admin.py:63 application/view/admin.py:103
#: application/view/admin.py:135
msgid "You do not have sufficient privileges."
msgstr "Vous n'avez pas suffisamment de privilèges."

#: application/view/admin.py:79 application/view/login.py:44
#: application/view/login.py:222
msgid "The username includes unsafe chars."
msgstr "Le nom d'utilisateur inclut des caractères non sécurisés."

#: application/view/admin.py:83 application/view/login.py:224
msgid "Already exist the username."
msgstr "Le nom d'utilisateur existe déjà."

#: application/view/admin.py:88
msgid "The password includes non-ascii chars."
msgstr "Le mot de passe inclut des caractères non ASCII."

#: application/view/admin.py:107 application/view/admin.py:132
#: application/view/admin.py:163 application/view/extension.py:32
#: application/view/extension.py:61
msgid "The username '{}' does not exist."
msgstr "Le nom d'utilisateur '{}' n'existe pas."

#: application/view/admin.py:123
msgid "The password will not be changed if the fields are empties."
msgstr "Le mot de passe ne sera pas modifié si les champs sont vides."

#: application/view/admin.py:130 application/view/admin.py:184
msgid "Change"
msgstr "Changer"

#: application/view/admin.py:181
msgid "Change success."
msgstr "Changement réussi."

#: application/view/admin.py:194
msgid "The old password is wrong."
msgstr "L'ancien mot de passe est incorrect."

#: application/view/admin.py:196
msgid "Changes saved successfully."
msgstr "Modifications enregistrées avec succès."

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108 application/view/adv.py:109
#: application/view/adv.py:110 application/view/adv.py:111
#: application/view/adv.py:112 application/view/adv.py:113
msgid "Append hyperlink '{}' to article"
msgstr "Ajouter le lien hypertexte '{}' à l'article"

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108
msgid "Save to {}"
msgstr "Enregistrer dans {}"

#: application/view/adv.py:104
msgid "evernote"
msgstr "evernote"

#: application/view/adv.py:105
msgid "wiz"
msgstr "wiz"

#: application/view/adv.py:106
msgid "pocket"
msgstr "pocket"

#: application/view/adv.py:107
msgid "instapaper"
msgstr "instapaper"

#: application/view/adv.py:108
msgid "wallabag"
msgstr "wallabag"

#: application/view/adv.py:109 application/view/adv.py:110
#: application/view/adv.py:111 application/view/adv.py:112
msgid "Share on {}"
msgstr "Partager sur {}"

#: application/view/adv.py:109
msgid "weibo"
msgstr "weibo"

#: application/view/adv.py:110
msgid "facebook"
msgstr "facebook"

#: application/view/adv.py:112
msgid "tumblr"
msgstr "tumblr"

#: application/view/adv.py:113
msgid "Open in browser"
msgstr "Ouvrir dans le navigateur"

#: application/view/adv.py:114
msgid "Append qrcode of url to article"
msgstr "Ajouter le QR code de l'URL à l'article"

#: application/view/adv.py:381 application/view/share.py:54
#: application/view/subscribe.py:250
msgid "Unknown command: {}"
msgstr "Commande inconnue : {}"

#: application/view/adv.py:439 application/view/adv.py:530
msgid "The format is invalid."
msgstr "Le format est invalide."

#: application/view/adv.py:562
msgid "Authorization Error!<br/>{}"
msgstr "Erreur d'autorisation !<br/>{}"

#: application/view/adv.py:583
msgid "Success authorized by Pocket!"
msgstr "Autorisation réussie par Pocket !"

#: application/view/adv.py:589
msgid ""
"Failed to request authorization of Pocket!<hr/>See details "
"below:<br/><br/>{}"
msgstr ""
"Échec de la demande d'autorisation de Pocket !<hr/>Voir les détails ci-"
"dessous :<br/><br/>{}"

#: application/view/adv.py:610
msgid "The Instapaper service encountered an error. Please try again later."
msgstr ""
"Le service Instapaper a rencontré une erreur. Veuillez réessayer plus tard."

#: application/view/adv.py:623
msgid "Request type [{}] unsupported"
msgstr "Type de demande [{}] non pris en charge"

#: application/view/deliver.py:82 application/view/login.py:169
#: application/view/share.py:41
msgid "The username does not exist or the email is empty."
msgstr "Le nom d'utilisateur n'existe pas ou l'e-mail est vide."

#: application/view/deliver.py:109
msgid "The following recipes has been added to the push queue."
msgstr ""
"Les recettes suivantes ont été ajoutées à la file d'attente de livraison."

#: application/view/deliver.py:112
msgid "There are no recipes to deliver."
msgstr "Il n'y a pas de recettes à livrer."

#: application/view/extension.py:69
msgid "The rules parameter is invalid."
msgstr "Le paramètre des règles est invalide."

#: application/view/library.py:32
msgid "Cannot fetch data from {}, status: {}"
msgstr "Impossible de récupérer les données de {}, statut : {}"

#: application/view/library.py:48 application/view/subscribe.py:238
#: application/view/subscribe.py:380 application/view/subscribe.py:409
#: application/view/subscribe.py:416 application/view/translator.py:30
msgid "The recipe does not exist."
msgstr "La recette n'existe pas."

#: application/view/login.py:27 application/view/login.py:76
msgid "Please use {}/{} to login at first time."
msgstr "Veuillez utiliser {}/{} pour vous connecter la première fois."

#: application/view/login.py:40
msgid "Username is empty."
msgstr "Le nom d'utilisateur est vide."

#: application/view/login.py:42
msgid "The len of username reached the limit of 25 chars."
msgstr ""
"La longueur du nom d'utilisateur a atteint la limite de 25 caractères."

#: application/view/login.py:80
msgid "Forgot password?"
msgstr "Mot de passe oublié ?"

#: application/view/login.py:148 application/view/login.py:275
msgid "The token is wrong or expired."
msgstr "Le jeton est incorrect ou expiré."

#: application/view/login.py:151
msgid "Please input the correct username and email to reset password."
msgstr ""
"Veuillez entrer le nom d'utilisateur et l'e-mail corrects pour réinitialiser"
" le mot de passe."

#: application/view/login.py:153
msgid "The email of account '{name}' is {email}."
msgstr "L'email du compte '{name}' est {email}."

#: application/view/login.py:174
msgid "Reset password success, Please close this page and login again."
msgstr ""
"Réinitialisation du mot de passe réussie. Veuillez fermer cette page et vous"
" reconnecter."

#: application/view/login.py:177
msgid "The email you input is not associated with this account."
msgstr "L'email que vous avez saisi n'est pas associé à ce compte."

#: application/view/login.py:186
msgid "The link to reset your password has been sent to your email."
msgstr ""
"Le lien pour réinitialiser votre mot de passe a été envoyé à votre email."

#: application/view/login.py:187
msgid "Please check your email inbox within 24 hours."
msgstr "Veuillez vérifier votre boîte de réception dans les 24 heures."

#: application/view/login.py:218
msgid "The invitation code is invalid."
msgstr "Le code d'invitation est invalide."

#: application/view/login.py:226
msgid ""
"Failed to create an account. Please contact the administrator for "
"assistance."
msgstr ""
"Échec de la création du compte. Veuillez contacter l'administrateur pour "
"obtenir de l'aide."

#: application/view/login.py:236
msgid "Successfully created account."
msgstr "Compte créé avec succès."

#: application/view/login.py:247
msgid "Reset KindleEar password"
msgstr "Réinitialiser le mot de passe KindleEar"

#: application/view/login.py:248
msgid "This is an automated email. Please do not reply to it."
msgstr "Ceci est un email automatisé. Merci de ne pas y répondre."

#: application/view/login.py:249
msgid "You can click the following link to reset your KindleEar password."
msgstr ""
"Vous pouvez cliquer sur le lien suivant pour réinitialiser votre mot de "
"passe KindleEar."

#: application/view/reader.py:88
msgid "The article is missing?"
msgstr "L'article est manquant ?"

#: application/view/reader.py:190 application/view/translator.py:121
#: application/view/translator.py:205 application/view/translator.py:287
msgid "The text is empty."
msgstr "Le texte est vide."

#: application/view/reader.py:239
msgid "No definitions found for '{}'."
msgstr "Aucune définition trouvée pour '{}'."

#: application/view/reader.py:240
msgid "Did you mean?"
msgstr "Vouliez-vous dire ?"

#: application/view/reader.py:324 application/view/reader.py:331
msgid "Failed to push: {}"
msgstr "Échec de l'envoi : {}"

#: application/view/reader.py:379
msgid "Failed to create ebook."
msgstr "Échec de la création de l'ebook."

#: application/view/settings.py:131
msgid ""
"You have not yet set up your email address. Please go to the 'Account' page "
"to add your email address firstly."
msgstr ""
"Vous n'avez pas encore configuré votre adresse email. Veuillez aller à la "
"page 'Compte' pour ajouter votre adresse email."

#: application/view/settings.py:215
msgid "English"
msgstr "Anglais"

#: application/view/settings.py:216
msgid "Simplified Chinese"
msgstr "Chinois simplifié"

#: application/view/settings.py:217
msgid "Traditional Chinese"
msgstr "Chinois traditionnel"

#: application/view/settings.py:218
msgid "French"
msgstr "Français"

#: application/view/settings.py:219
msgid "Spanish"
msgstr "Espagnol"

#: application/view/settings.py:220
msgid "Portuguese"
msgstr "Portugais"

#: application/view/settings.py:221
msgid "German"
msgstr "Allemand"

#: application/view/settings.py:222
msgid "Italian"
msgstr "Italien"

#: application/view/settings.py:223
msgid "Japanese"
msgstr "Japonais"

#: application/view/settings.py:224
msgid "Russian"
msgstr "Russe"

#: application/view/settings.py:225
msgid "Turkish"
msgstr "Turc"

#: application/view/settings.py:226
msgid "Korean"
msgstr "Coréen"

#: application/view/settings.py:227
msgid "Arabic"
msgstr "Arabe"

#: application/view/settings.py:228
msgid "Czech"
msgstr "Tchèque"

#: application/view/settings.py:229
msgid "Dutch"
msgstr "Néerlandais"

#: application/view/settings.py:230
msgid "Greek"
msgstr "Grec"

#: application/view/settings.py:231
msgid "Hindi"
msgstr "Hindi"

#: application/view/settings.py:232
msgid "Malaysian"
msgstr "Malais"

#: application/view/settings.py:233
msgid "Bengali"
msgstr "Bengali"

#: application/view/settings.py:234
msgid "Persian"
msgstr "Persan"

#: application/view/settings.py:235
msgid "Urdu"
msgstr "Ourdou"

#: application/view/settings.py:236
msgid "Swahili"
msgstr "Swahili"

#: application/view/settings.py:237
msgid "Vietnamese"
msgstr "Vietnamien"

#: application/view/settings.py:238
msgid "Punjabi"
msgstr "Pendjabi"

#: application/view/settings.py:239
msgid "Javanese"
msgstr "Javanais"

#: application/view/settings.py:240
msgid "Tagalog"
msgstr "Tagalog"

#: application/view/settings.py:241
msgid "Hausa"
msgstr "Hausa"

#: application/view/settings.py:242
msgid "Thai"
msgstr "Thaïlandais"

#: application/view/settings.py:243
msgid "Polish"
msgstr "Polonais"

#: application/view/settings.py:244
msgid "Romanian"
msgstr "Roumain"

#: application/view/settings.py:245
msgid "Hungarian"
msgstr "Hongrois"

#: application/view/settings.py:246
msgid "Swedish"
msgstr "Suédois"

#: application/view/settings.py:247
msgid "Hebrew"
msgstr "Hébreu"

#: application/view/settings.py:248
msgid "Norwegian"
msgstr "Norvégien"

#: application/view/settings.py:249
msgid "Finnish"
msgstr "Finlandais"

#: application/view/settings.py:250
msgid "Danish"
msgstr "Danois"

#: application/view/settings.py:251
msgid "Ukrainian"
msgstr "Ukrainien"

#: application/view/settings.py:252
msgid "Tamil"
msgstr "Tamoul"

#: application/view/settings.py:253
msgid "Marathi"
msgstr "Marathe"

#: application/view/settings.py:254
msgid "Burmese"
msgstr "Birmans"

#: application/view/settings.py:255
msgid "Amharic"
msgstr "Amharique"

#: application/view/settings.py:256
msgid "Azerbaijani"
msgstr "Azerbaïdjanais"

#: application/view/settings.py:257
msgid "Kazakh"
msgstr "Kazakh"

#: application/view/settings.py:258
msgid "Serbian"
msgstr "Serbe"

#: application/view/settings.py:259
msgid "Croatian"
msgstr "Croate"

#: application/view/settings.py:260
msgid "Slovak"
msgstr "Slovaque"

#: application/view/settings.py:261
msgid "Bulgarian"
msgstr "Bulgare"

#: application/view/settings.py:262
msgid "Icelandic"
msgstr "Islandais"

#: application/view/settings.py:263
msgid "Lithuanian"
msgstr "Lituanien"

#: application/view/settings.py:264
msgid "Latvian"
msgstr "Letton"

#: application/view/settings.py:265
msgid "Estonian"
msgstr "Estonien"

#: application/view/settings.py:266
msgid "Macedonian"
msgstr "Macédonien"

#: application/view/settings.py:267
msgid "Albanian"
msgstr "Albanais"

#: application/view/settings.py:268
msgid "Galician"
msgstr "Galicien"

#: application/view/settings.py:269
msgid "Welsh"
msgstr "Gallois"

#: application/view/settings.py:270
msgid "Basque"
msgstr "Basque"

#: application/view/settings.py:271
msgid "Nepali"
msgstr "Népalais"

#: application/view/share.py:60
msgid "There is no {} email yet."
msgstr "Il n'y a pas encore d'email {}."

#: application/view/share.py:108 application/view/share.py:133
#: application/view/share.py:155 application/view/share.py:177
msgid "Saved to your {} account."
msgstr "Enregistré dans votre compte {}."

#: application/view/share.py:111 application/view/share.py:129
#: application/view/share.py:158 application/view/share.py:179
msgid "Failed save to {}."
msgstr "Échec de l'enregistrement dans {}."

#: application/view/share.py:112 application/view/share.py:130
#: application/view/share.py:159 application/view/share.py:180
msgid "Reason :"
msgstr "Raison :"

#: application/view/share.py:121
msgid "Unauthorized {} account!"
msgstr "Compte {} non autorisé !"

#: application/view/share.py:134
msgid "See details below:"
msgstr "Voir les détails ci-dessous :"

#: application/view/share.py:157
msgid "Unknown: {}"
msgstr "Inconnu : {}"

#: application/view/subscribe.py:81 application/view/subscribe.py:167
msgid "Duplicated subscription!"
msgstr "Abonnement dupliqué !"

#: application/view/subscribe.py:126
msgid "The Title or Url is empty."
msgstr "Le titre ou l'URL est vide."

#: application/view/subscribe.py:139
msgid "Failed to fetch the recipe."
msgstr "Échec de la récupération de la recette."

#: application/view/subscribe.py:153 application/view/subscribe.py:331
msgid "Failed to save the recipe. Error:"
msgstr "Échec de l'enregistrement de la recette. Erreur :"

#: application/view/subscribe.py:195
msgid "The Rss does not exist."
msgstr "Le flux RSS n'existe pas."

#: application/view/subscribe.py:278
msgid "You can only delete the uploaded recipe."
msgstr "Vous ne pouvez supprimer que la recette téléchargée."

#: application/view/subscribe.py:283
msgid "The recipe have been subscribed, please unsubscribe it before delete."
msgstr ""
"La recette a été abonnée, veuillez vous désabonner avant de la supprimer."

#: application/view/subscribe.py:304 application/view/translator.py:51
#: application/view/translator.py:104 application/view/translator.py:117
#: application/view/translator.py:140 application/view/translator.py:188
#: application/view/translator.py:201 application/view/translator.py:230
#: application/view/translator.py:270 application/view/translator.py:283
msgid "This recipe has not been subscribed to yet."
msgstr "Cette recette n'a pas encore été abonnée."

#: application/view/subscribe.py:318
msgid "Can not read uploaded file, Error:"
msgstr "Impossible de lire le fichier téléchargé, Erreur :"

#: application/view/subscribe.py:326
msgid ""
"Failed to decode the recipe. Please ensure that your recipe is saved in "
"utf-8 encoding."
msgstr ""
"Échec du décodage de la recette. Veuillez vous assurer que votre recette est"
" enregistrée en codage UTF-8."

#: application/view/subscribe.py:349
msgid "Cannot find any subclass of BasicNewsRecipe."
msgstr "Impossible de trouver une sous-classe de BasicNewsRecipe."

#: application/view/subscribe.py:354
msgid "The recipe is already in the library."
msgstr "La recette est déjà dans la bibliothèque."

#: application/view/subscribe.py:387
msgid "The login information for this recipe has been cleared."
msgstr "Les informations de connexion pour cette recette ont été effacées."

#: application/view/subscribe.py:391
msgid "The login information for this recipe has been saved."
msgstr ""
"Les informations de connexion pour cette recette ont été enregistrées."

#: application/view/translator.py:81 application/view/translator.py:165
msgid "The api key is required."
msgstr "La clé API est requise."
