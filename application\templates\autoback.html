{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Auto back") }} - KindleEar</title>
{% endblock -%}

{% block js -%}
<script type="text/javascript">
function gobackafter(secs) {
  if(--secs > 0) {
   setTimeout("gobackafter(" + secs + ");", 1000);
  } else {
    history.go(-1);
  }
}
gobackafter(5);
</script>
{% endblock -%}

{% block menubar -%}
{% endblock -%}

{% block content -%}
<div class="main">
  {% if tips -%}
  <div class="notice-box">
    <p>{{tips|safe}}</p>
    <hr/>
    <p>{{_("Auto back to previous page after 5 seconds")}}...</p>
    <a href="javascript:history.go(-1);">{{_("Click to back")}}</a>
  </div>
  {% endif -%}
</div>
{% endblock -%}
