# Turkish (Türk<PERSON>ye) translations for PROJECT.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-10 19:49-0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: tr <<EMAIL>>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.14.0\n"

#: application/templates/admin.html:3 application/templates/base.html:53
#: application/templates/base.html:192 application/templates/settings.html:263
msgid "Account"
msgstr "Yönetim"

#: application/templates/admin.html:19
msgid "Signup settings"
msgstr "Kayıt Ayarları"

#: application/templates/admin.html:19
#: application/templates/adv_calibre_options.html:18
#: application/templates/adv_proxy.html:18
msgid "Save"
msgstr "Kaydet"

#: application/templates/admin.html:21
#: application/templates/user_account.html:31
msgid "Email service"
msgstr "E-posta servisi"

#: application/templates/admin.html:23
#: application/templates/user_account.html:34
msgid "Same as admin"
msgstr "Admin ile aynı"

#: application/templates/admin.html:24
#: application/templates/user_account.html:35
msgid "Independent"
msgstr "Bağımsız"

#: application/templates/admin.html:28
msgid "Signup type"
msgstr "Kayıt Türü"

#: application/templates/admin.html:30
msgid "Public"
msgstr "Kamu"

#: application/templates/admin.html:31
msgid "One time code"
msgstr "Tek kullanımlık kod"

#: application/templates/admin.html:32
msgid "Permanent code"
msgstr "Kalıcı kod"

#: application/templates/admin.html:36
msgid "Invitation codes"
msgstr "Davetiye kodları"

#: application/templates/admin.html:37
msgid "one code per line"
msgstr "Her satırda bir kod"

#: application/templates/admin.html:43
msgid "Accounts"
msgstr "Hesaplar"

#: application/templates/admin.html:43
#: application/templates/adv_inboundmail.html:61
#: application/templates/my.html:34 application/view/admin.py:57
#: application/view/admin.py:64 application/view/admin.py:91
msgid "Add"
msgstr "Ekle"

#: application/templates/admin.html:54
#: application/templates/adv_archive.html:69
#: application/templates/home.html:19 application/templates/login.html:24
#: application/templates/logs.html:70
#: application/templates/reset_password.html:19
#: application/templates/reset_password.html:20
#: application/templates/settings.html:241
#: application/templates/signup.html:21
#: application/templates/user_account.html:15
msgid "Username"
msgstr "Kullanıcı adı"

#: application/templates/admin.html:55
msgid "AutoSend"
msgstr "Aktif"

#: application/templates/admin.html:56
#: application/templates/change_password.html:27
#: application/templates/reset_password.html:26
#: application/templates/signup.html:33
#: application/templates/user_account.html:27
msgid "Email"
msgstr "E-posta"

#: application/templates/admin.html:57
#: application/templates/user_account.html:39
msgid "Expiration"
msgstr "Son"

#: application/templates/admin.html:58
msgid "Operation"
msgstr "İşlemler"

#: application/templates/admin.html:65
msgid "Yes"
msgstr "Evet"

#: application/templates/admin.html:65
msgid "No"
msgstr "Hayir"

#: application/templates/admin.html:68
msgid "Never"
msgstr "Asla"

#: application/templates/admin.html:70
#: application/templates/adv_inboundmail.html:26
#: application/templates/settings.html:58
#: application/templates/settings.html:181
#: application/templates/user_account.html:43
msgid "7 Days"
msgstr "7 Günlük"

#: application/templates/admin.html:72
#: application/templates/user_account.html:44
msgid "1 Month"
msgstr "1 ay"

#: application/templates/admin.html:74
#: application/templates/user_account.html:45
msgid "3 Months"
msgstr "3 ay"

#: application/templates/admin.html:76
#: application/templates/user_account.html:46
msgid "6 Months"
msgstr "6 ay"

#: application/templates/admin.html:78
#: application/templates/user_account.html:47
msgid "1 Year"
msgstr "1 yıl"

#: application/templates/admin.html:80
#: application/templates/user_account.html:48
msgid "2 Years"
msgstr "2 yıl"

#: application/templates/admin.html:85
#: application/templates/change_password.html:3
#: application/templates/change_password.html:13 application/view/admin.py:129
#: application/view/admin.py:183
msgid "Edit account"
msgstr "Hesabı Düzenle"

#: application/templates/admin.html:89
#: application/templates/adv_inboundmail.html:54
#: application/templates/adv_uploadcss.html:31
#: application/templates/base.html:25 application/templates/webmail.html:22
msgid "Delete"
msgstr "Sil"

#: application/templates/adv_archive.html:3
#: application/templates/adv_archive.html:14
#: application/templates/adv_base.html:57
#: application/templates/adv_base.html:61
msgid "Archive"
msgstr "Arşiv"

#: application/templates/adv_archive.html:15
msgid "Append hyperlinks for archiving or sharing."
msgstr "Paylaşım linklerini makaleye ekle."

#: application/templates/adv_archive.html:40
msgid "Authorized"
msgstr "Yetkili"

#: application/templates/adv_archive.html:42
msgid "Authorize"
msgstr "Yetki vermek"

#: application/templates/adv_archive.html:53
msgid "Email or Username"
msgstr "Kullanıcı adı"

#: application/templates/adv_archive.html:56
#: application/templates/adv_archive.html:72
#: application/templates/base.html:54 application/templates/home.html:20
#: application/templates/login.html:28 application/templates/settings.html:245
#: application/templates/signup.html:25
#: application/templates/user_account.html:19
msgid "Password"
msgstr "Şifre"

#: application/templates/adv_archive.html:59
#: application/templates/adv_archive.html:75
#: application/templates/base.html:64
msgid "Verify"
msgstr "Doğrulamak"

#: application/templates/adv_archive.html:78
msgid "client_id"
msgstr "client_id"

#: application/templates/adv_archive.html:81
msgid "client_secret"
msgstr "client_secret"

#: application/templates/adv_archive.html:84
#: application/templates/settings.html:233
msgid "Host"
msgstr "Ana bilgisayar"

#: application/templates/adv_archive.html:119
#: application/templates/adv_dict.html:79
#: application/templates/adv_inboundmail.html:34
#: application/templates/book_audiolator.html:113
#: application/templates/book_summarizer.html:88
#: application/templates/book_translator.html:80
#: application/templates/settings.html:269
msgid "Save settings"
msgstr "Ayarları kaydet"

#: application/templates/adv_base.html:39
#: application/templates/adv_base.html:43
#: application/templates/adv_delivernow.html:8
msgid "Deliver Now"
msgstr "Şimdi gönder"

#: application/templates/adv_base.html:48
#: application/templates/adv_base.html:52
#: application/templates/adv_inboundmail.html:3
#: application/templates/adv_inboundmail.html:9
#: application/templates/adv_inboundmail.html:14
msgid "Inbound Mail"
msgstr "Gelen Posta"

#: application/templates/adv_base.html:66
#: application/templates/adv_base.html:70
#: application/templates/adv_dict.html:3
#: application/templates/adv_dict.html:13
#: application/templates/reader.html:134
msgid "Dictionary"
msgstr "Sözlük"

#: application/templates/adv_base.html:75
#: application/templates/adv_base.html:83
#: application/templates/adv_proxy.html:3
#: application/templates/adv_proxy.html:12
msgid "Proxy"
msgstr "Proxy"

#: application/templates/adv_base.html:92
#: application/templates/adv_base.html:96
#: application/templates/adv_import.html:8
msgid "Import Feeds"
msgstr "feedleri içeri aktar"

#: application/templates/adv_base.html:101
#: application/templates/adv_base.html:105
msgid "Cover Image"
msgstr "Kapak resmi"

#: application/templates/adv_base.html:110
#: application/templates/adv_base.html:114
#: application/templates/adv_uploadcss.html:3
msgid "Stylesheet"
msgstr "Stylesheet"

#: application/templates/adv_base.html:119
#: application/templates/adv_base.html:123
#: application/templates/adv_calibre_options.html:3
#: application/templates/adv_calibre_options.html:12
msgid "Calibre Options"
msgstr "Calibre seçenekleri"

#: application/templates/adv_calibre_options.html:13
msgid "Set the parameters for Calibre, in JSON dictionary format."
msgstr "Calibre için parametreleri JSON sözlük formatında ayarlayın."

#: application/templates/adv_delivernow.html:3
msgid "Deliver now"
msgstr "Şimdi gönder"

#: application/templates/adv_delivernow.html:9
msgid "Deliver selected recipes now."
msgstr "Seçilen tarifleri şimdi teslim edin."

#: application/templates/adv_delivernow.html:12
msgid "There are no recipes subscribed"
msgstr "Abone olunan tarif yok"

#: application/templates/adv_delivernow.html:17
#: application/templates/base.html:101
msgid "Sep"
msgstr "Sep"

#: application/templates/adv_delivernow.html:22
msgid "Select all"
msgstr "Hepsini seç"

#: application/templates/adv_delivernow.html:23
msgid "Select none"
msgstr "Hiçbirini seçme"

#: application/templates/adv_delivernow.html:28
msgid "Deliver"
msgstr "Şimdi gönder"

#: application/templates/adv_dict.html:15
msgid "Set up dictionaries for online reading."
msgstr "Online okuma için sözlükleri kurun."

#: application/templates/adv_dict.html:18
#: application/templates/adv_dict.html:40
#: application/templates/adv_dict.html:62
msgid "Book language"
msgstr "Kitap dili"

#: application/templates/adv_dict.html:27
#: application/templates/adv_dict.html:49
#: application/templates/adv_dict.html:66
#: application/templates/book_summarizer.html:23
#: application/templates/book_translator.html:26
#: application/templates/word_lookup.html:59
msgid "Engine"
msgstr "Motor"

#: application/templates/adv_dict.html:33
#: application/templates/adv_dict.html:55
#: application/templates/adv_dict.html:72
#: application/templates/word_lookup.html:65
msgid "Database"
msgstr "Veritabanı"

#: application/templates/adv_dict.html:63
msgid "Other languages"
msgstr "Diğer diller"

#: application/templates/adv_dict.html:81
#: application/templates/word_lookup.html:3
#: application/templates/word_lookup.html:79
msgid "Word lookup"
msgstr "Kelime arama"

#: application/templates/adv_dict.html:86 application/view/reader.py:29
#: application/view/reader.py:86
msgid "Online reading feature has not been activated yet."
msgstr "Çevrimiçi okuma özelliği henüz etkinleştirilmedi."

#: application/templates/adv_import.html:3
#: application/templates/adv_import.html:19
msgid "Import"
msgstr "İçeri aktar"

#: application/templates/adv_import.html:9
msgid "Import custom rss from an OPML file."
msgstr "Bir OPML dosyasındaki rss'leri içeri aktar."

#: application/templates/adv_import.html:15
msgid "Import as fulltext rss by default"
msgstr "Varsayılan olarak tam metin rss olarak içe aktar"

#: application/templates/adv_import.html:20
msgid "Download"
msgstr "İndir"

#: application/templates/adv_inboundmail.html:11
msgid ""
"To enable the inbound email feature, you also need to configure the "
"whitelist."
msgstr ""
"Gelen e-posta özelliğini etkinleştirmek için, beyaz liste ayarlarını da "
"yapılandırmanız gerekmektedir."

#: application/templates/adv_inboundmail.html:16
#: application/templates/adv_uploadcover.html:15
#: application/templates/book_summarizer.html:19
#: application/templates/book_translator.html:22
#: application/templates/settings.html:143
msgid "Disable"
msgstr "Devre dışı bırak"

#: application/templates/adv_inboundmail.html:17
msgid "Forward Only"
msgstr "Sadece Yönlendir"

#: application/templates/adv_inboundmail.html:18
msgid "Save Only"
msgstr "Sadece Kaydet"

#: application/templates/adv_inboundmail.html:19
msgid "Save and Forward"
msgstr "Kaydet ve Yönlendir"

#: application/templates/adv_inboundmail.html:23
msgid "Email Retention"
msgstr "E-posta Saklama"

#: application/templates/adv_inboundmail.html:25
#: application/templates/settings.html:52
#: application/templates/settings.html:175
msgid "1 Day"
msgstr "1 Günlük"

#: application/templates/adv_inboundmail.html:27
#: application/templates/settings.html:59
msgid "30 Days"
msgstr "30 Gün"

#: application/templates/adv_inboundmail.html:28
#: application/templates/settings.html:51
#: application/templates/settings.html:174
msgid "No limit"
msgstr "Sınırlama yok."

#: application/templates/adv_inboundmail.html:36
msgid "Open webmail"
msgstr "Webmail'i Aç"

#: application/templates/adv_inboundmail.html:44
msgid "White List"
msgstr "Beyaz liste"

#: application/templates/adv_inboundmail.html:46
#, python-format
msgid ""
"Emails sent to %(name)sxxx@%(mailHost)s will be forwarded to your kindle "
"email."
msgstr ""
"%(name)sxxx@%(mailHost)s adresine gönderilen emailler admin hesabına bağlı "
"Kindle'a gönderilecek."

#: application/templates/adv_inboundmail.html:47
msgid "Example"
msgstr "Örnek"

#: application/templates/adv_inboundmail.html:59
msgid "Please input mail address"
msgstr "Lütfen e-posta adresini giriniz"

#: application/templates/adv_proxy.html:13
msgid "Supports"
msgstr "Destekler"

#: application/templates/adv_proxy.html:24
#: application/templates/adv_proxy.html:29
#: application/templates/book_audiolator.html:132
#: application/templates/book_summarizer.html:105
#: application/templates/book_translator.html:97
msgid "Test"
msgstr "Test"

#: application/templates/adv_uploadcover.html:3
msgid "Cover image"
msgstr "Kapak resmi"

#: application/templates/adv_uploadcover.html:9
msgid "Upload cover image"
msgstr "Kapak resmi yükle"

#: application/templates/adv_uploadcover.html:10
msgid ""
"Upload cover images from local with an aspect ratio of approximately 0.625."
msgstr ""
"Yaklaşık 0.625 en boy oranına sahip kapak resimlerini yerel olarak yükleyin."

#: application/templates/adv_uploadcover.html:13
msgid "Include cover"
msgstr "Kapak Dahil"

#: application/templates/adv_uploadcover.html:16
#: application/templates/book_summarizer.html:18
#: application/templates/book_translator.html:21
msgid "Enable"
msgstr "Etkinleştir"

#: application/templates/adv_uploadcover.html:20
msgid "Rule for cover"
msgstr "Kapak Kuralı"

#: application/templates/adv_uploadcover.html:22
msgid "Random"
msgstr "Rastgele"

#: application/templates/adv_uploadcover.html:23
#: application/templates/base.html:127
msgid "Weekday"
msgstr "Hafta içi gün"

#: application/templates/adv_uploadcover.html:49
msgid "Upload/Update"
msgstr "Yükle/Güncelle"

#: application/templates/adv_uploadcss.html:22
msgid "Upload stylesheet"
msgstr "Stil sayfası yükle"

#: application/templates/adv_uploadcss.html:23
msgid "Upload a stylesheet from local (accept utf-8 only)."
msgstr "Yerel olarak bir stil sayfası yükle (sadece utf-8 kabul edilir)."

#: application/templates/adv_uploadcss.html:30
msgid "Upload"
msgstr "Yükleme"

#: application/templates/autoback.html:3
msgid "Auto back"
msgstr "Geri dönmek için tıklayınız"

#: application/templates/autoback.html:28
msgid "Auto back to previous page after 5 seconds"
msgstr "5 saniye sonra otomatik olarak önceki sayfaya döneceksiniz."

#: application/templates/autoback.html:29
#: application/templates/tipsback.html:15
msgid "Click to back"
msgstr "Geri dönmek için tıklayınız"

#: application/templates/base.html:24 application/templates/reader.html:189
msgid "Confirm Deletion"
msgstr "Silmeyi Onayla"

#: application/templates/base.html:26
msgid "Delete (Ctrl for no confirm)"
msgstr "Sil (Onay için Ctrl basın)"

#: application/templates/base.html:27
msgid "View Source Code"
msgstr "Kaynak kodunu görüntüle"

#: application/templates/base.html:28
msgid "Subscribe (Deliver Separately)"
msgstr "Abone ol (Ayrı teslim et)"

#: application/templates/base.html:29
msgid "Subscribe"
msgstr "Abone ol"

#: application/templates/base.html:30
msgid "Cannot add this custom rss, Error:"
msgstr "Bu özel rss eklenemiyor, Hata:"

#: application/templates/base.html:31
msgid "Cannot delete this feed, Error:"
msgstr "Bu yayını silemiyorum, Hata:"

#: application/templates/base.html:32
msgid "Fulltext"
msgstr "Tam Metin"

#: application/templates/base.html:33 application/templates/base.html:43
msgid "Share"
msgstr "Paylaş"

#: application/templates/base.html:34 application/templates/reader.html:190
msgid "Are you sure to delete?"
msgstr "Silmek istediğinizden emin misiniz?"

#: application/templates/base.html:35
msgid "Report to the server that this feed is invalid."
msgstr "Sunucuya bu beslemenin geçersiz olduğunu bildirin."

#: application/templates/base.html:36
msgid "Are you sure to REMOVE ALL CUSTOM RSS?"
msgstr "TÜM ÖZEL RSS'LERİ KALDIRMAK İSTEDİĞİNİZE EMİN MİSİNİZ?"

#: application/templates/base.html:37
msgid "Share links, share happiness"
msgstr "Bağlantıları paylaş, mutluluk paylaş"

#: application/templates/base.html:38
msgid "Category"
msgstr "Kategori"

#: application/templates/base.html:39
#: application/templates/book_audiolator.html:58
#: application/templates/book_summarizer.html:43
#: application/templates/settings.html:159
msgid "Language"
msgstr "Dil"

#: application/templates/base.html:40
msgid ""
"Please write a category in text field if the one you wish is not in the "
"list."
msgstr ""
"Dileğiniz kategori listede yoksa lütfen metin alanına bir kategori yazın."

#: application/templates/base.html:41
msgid "Ok"
msgstr "Tamam"

#: application/templates/base.html:42
msgid "Cancel"
msgstr "İptal"

#: application/templates/base.html:44
msgid "Language code invalid"
msgstr "Dil kodu geçersiz"

#: application/templates/base.html:45
msgid "Thank you for sharing."
msgstr "Paylaştığınız için teşekkür ederiz."

#: application/templates/base.html:46 application/templates/reader.html:159
msgid "Close"
msgstr "Kapat"

#: application/templates/base.html:47
msgid "Unsubscribe"
msgstr "Abonelikten çık"

#: application/templates/base.html:48
msgid "Cannot subscribe this recipe, Error:"
msgstr "Bu tarife abone olunamıyor, Hata:"

#: application/templates/base.html:49
msgid "Are you sure to Unsubscribe ({0})?"
msgstr "({0}) aboneliği iptal etmek istediğinizden emin misiniz?"

#: application/templates/base.html:50
msgid "Cannot unsubscribe this recipe, Error:"
msgstr "Bu tarife abonelik iptal edilemiyor, Hata:"

#: application/templates/base.html:51
msgid "The recipe is already subscribed."
msgstr "Tarif zaten abone edilmiş."

#: application/templates/base.html:52
msgid "Website login lnformation"
msgstr "Abonelik bilgisi"

#: application/templates/base.html:55
msgid "Submit"
msgstr "Gönder"

#: application/templates/base.html:56
msgid ""
"If any field is left blank, the server will clear the saved login "
"information."
msgstr ""
"Herhangi bir alan boş bırakılırsa, sunucu kayıtlı giriş bilgilerini "
"silecektir."

#: application/templates/base.html:57
msgid "Cannot set the website login information, Error:"
msgstr "Abonelik bilgisi ayarlanamıyor, Hata:"

#: application/templates/base.html:58 application/templates/my.html:58
msgid "Upload custom recipe"
msgstr "Özel tarifi yükle"

#: application/templates/base.html:59
msgid "Congratulations"
msgstr "Tebrikler"

#: application/templates/base.html:60
msgid "Thanks"
msgstr "Teşekkürler"

#: application/templates/base.html:61
msgid ""
"Your recipe has been uploaded, and it can be found in the Library section. "
"If you dont see it, please make sure to switch to the correct language."
msgstr ""
"Tarifiniz yüklendi ve Kütüphane bölümünde bulunabilir. Görmüyorsanız, lütfen"
" doğru dile geçtiğinizden emin olun."

#: application/templates/base.html:62
msgid "Your recipe have been deleted."
msgstr "Tarifiniz silindi."

#: application/templates/base.html:63
msgid "Kindleify Selection"
msgstr "Kindle'laştırma Seçimi"

#: application/templates/base.html:65
msgid "Verified"
msgstr "Doğrulanmış"

#: application/templates/base.html:66 application/view/login.py:79
#: application/view/share.py:157
msgid "The username does not exist or password is wrong."
msgstr "Kullanıcı adı mevcut değil veya şifre yanlış."

#: application/templates/base.html:67
msgid "The file you chosen is not an acceptable type."
msgstr "Seçtiğiniz dosya kabul edilebilir bir türde değil."

#: application/templates/base.html:68
msgid "The file have been uploaded successfully."
msgstr "Dosya başarıyla yüklendi."

#: application/templates/base.html:69 application/templates/library.html:67
msgid "This feed has been successfully subscribed."
msgstr "Bu besleme başarıyla abone edildi."

#: application/templates/base.html:70
msgid "Thank you for your feedback, this feed will be reviewed soon."
msgstr ""
"Geri bildiriminiz için teşekkür ederiz, bu besleme yakında incelenecek."

#: application/templates/base.html:71
msgid "Are you confirming to share the recipe ({0})?"
msgstr "Tarifi paylaşmayı onaylıyor musunuz ({0})?"

#: application/templates/base.html:72
msgid "[All]"
msgstr "[Tümü]"

#: application/templates/base.html:73
msgid "[By Time]"
msgstr "[Zamana Göre]"

#: application/templates/base.html:74
msgid "[Random]"
msgstr "[Rastgele]"

#: application/templates/base.html:75
msgid "[Uncategoried]"
msgstr "[Kategorisiz]"

#: application/templates/base.html:76
msgid "There are no links found."
msgstr "Seçilen dilde bağlantı bulunamadı."

#: application/templates/base.html:77
msgid "Invalid report"
msgstr "Geçersiz rapor"

#: application/templates/base.html:78
msgid "Are you confirming that this link is invalid or off the cloud?"
msgstr "Bu bağlantının geçersiz veya bulutta olmadığını onaylıyor musunuz?"

#: application/templates/base.html:79
msgid "Customize delivery time"
msgstr "Teslimat zamanını özelleştir"

#: application/templates/base.html:80 application/templates/settings.html:72
msgid "Delivery days"
msgstr "Teslimat günleri"

#: application/templates/base.html:81 application/templates/settings.html:74
msgid "Mon"
msgstr "Pzt"

#: application/templates/base.html:82 application/templates/settings.html:76
msgid "Tue"
msgstr "Sal"

#: application/templates/base.html:83 application/templates/settings.html:78
msgid "Wed"
msgstr "Çar"

#: application/templates/base.html:84 application/templates/settings.html:80
msgid "Thu"
msgstr "Prş"

#: application/templates/base.html:85 application/templates/settings.html:82
msgid "Fri"
msgstr "Cum"

#: application/templates/base.html:86 application/templates/settings.html:84
msgid "Sat"
msgstr "Cts"

#: application/templates/base.html:87 application/templates/settings.html:86
msgid "Sun"
msgstr "Paz"

#: application/templates/base.html:88
msgid "Delivery times"
msgstr "Teslimat saatleri"

#: application/templates/base.html:89
msgid ""
"The customized delivery time for the recipe has been successfully saved."
msgstr "Tarif için özel teslimat zamanı başarıyla kaydedildi."

#: application/templates/base.html:90
msgid "The account have been deleted."
msgstr "Hesap silindi."

#: application/templates/base.html:91 application/view/share.py:147
msgid "The username or password is empty."
msgstr "Kullanıcı adı veya şifre boş."

#: application/templates/base.html:92 application/view/admin.py:81
#: application/view/admin.py:165 application/view/admin.py:191
#: application/view/login.py:220 application/view/login.py:273
msgid "The two new passwords are dismatch."
msgstr "İki yeni şifre uyuşmuyor."

#: application/templates/base.html:93
msgid "Password changed successfully."
msgstr "Şifre başarıyla değiştirildi."

#: application/templates/base.html:94
msgid "Account added successfully."
msgstr "Hesap başarıyla eklendi."

#: application/templates/base.html:95 application/view/login.py:128
msgid "login required"
msgstr "Giriş yapılması gerekiyor"

#: application/templates/base.html:96
msgid "Upload cover files successfully."
msgstr "Kapak dosyaları başarıyla yüklendi."

#: application/templates/base.html:97
msgid ""
"Total size of the files you selected exceeds 16MB. Please reduce the image "
"resolution or upload in batches."
msgstr ""
"Seçtiğiniz dosyaların toplam boyutu 16 MB yi aşıyor. Lütfen görüntü "
"çözünürlüğünü azaltın veya toplu olarak yükleyin."

#: application/templates/base.html:98
#: application/templates/book_translator.html:3
#: application/templates/book_translator.html:17
msgid "Bilingual Translator"
msgstr "Çift Dilli Çevirmen"

#: application/templates/base.html:99
#: application/templates/book_summarizer.html:3
#: application/templates/book_summarizer.html:14
msgid "AI Summarizer"
msgstr "Yapay Zeka Özetleyici"

#: application/templates/base.html:100
msgid "Upl"
msgstr "Upl"

#: application/templates/base.html:102
msgid "Log"
msgstr "Log"

#: application/templates/base.html:103
msgid "Emb"
msgstr "Emb"

#: application/templates/base.html:104
msgid "Tr"
msgstr "Tr"

#: application/templates/base.html:105
msgid "Tts"
msgstr "Tts"

#: application/templates/base.html:106
msgid "Ai"
msgstr "Ai"

#: application/templates/base.html:107
msgid ""
"The test email has been successfully sent to the following addresses. Please"
" check your inbox or spam folder to confirm its delivery. Depending on your "
"email server, there may be a slight delay."
msgstr ""
"Test e-postası başarıyla adresine gönderildi. Teslimatını onaylamak için "
"gelen kutunuzu veya spam klasörünüzü kontrol edin. E-posta sunucunuza bağlı "
"olarak hafif bir gecikme olabilir."

#: application/templates/base.html:108
msgid "Processing..."
msgstr "İşleniyor..."

#: application/templates/base.html:109
msgid "The configuration validation is correct."
msgstr "Konfigürasyon doğrulaması doğru."

#: application/templates/base.html:110 application/templates/logs.html:23
#: application/templates/logs.html:72 application/templates/my.html:17
#: application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/settings.html:155
msgid "Title"
msgstr "Başlık"

#: application/templates/base.html:111
#: application/templates/book_audiolator.html:3
#: application/templates/book_audiolator.html:20
msgid "Text to Speech"
msgstr "Metin Okuma"

#: application/templates/base.html:112
msgid "Action"
msgstr "Eylem"

#: application/templates/base.html:113
msgid "File"
msgstr "Dosya"

#: application/templates/base.html:114
msgid "Upload Only"
msgstr "Yalnızca Yükle"

#: application/templates/base.html:115
msgid "Send"
msgstr "Gönder"

#: application/templates/base.html:116 application/templates/logs.html:54
msgid "There is nothing here."
msgstr "Burada hiçbir şey yok."

#: application/templates/base.html:117
msgid "Please select a single item."
msgstr "Lütfen tek bir öğe seçin."

#: application/templates/base.html:118 application/templates/reader.html:191
msgid "Please select at least one item."
msgstr "Lütfen en az bir öğe seçin."

#: application/templates/base.html:119 application/view/admin.py:77
#: application/view/admin.py:152 application/view/admin.py:189
#: application/view/adv.py:459 application/view/extension.py:34
#: application/view/extension.py:63 application/view/inbound_email.py:470
#: application/view/inbound_email.py:478 application/view/inbound_email.py:493
#: application/view/inbound_email.py:514 application/view/login.py:216
#: application/view/login.py:245 application/view/reader.py:109
#: application/view/reader.py:126 application/view/share.py:37
msgid "Some parameters are missing or wrong."
msgstr "Bazı parametreler eksik veya yanlış."

#: application/templates/base.html:120
msgid "The email has been sent."
msgstr "E-posta gönderildi."

#: application/templates/base.html:121 application/templates/webmail.html:29
msgid "From"
msgstr "Gönderen"

#: application/templates/base.html:122 application/templates/logs.html:25
#: application/templates/logs.html:74 application/templates/webmail.html:30
msgid "To"
msgstr "Kime"

#: application/templates/base.html:123 application/templates/webmail.html:31
msgid "Subject"
msgstr "Konu"

#: application/templates/base.html:124 application/templates/logs.html:22
#: application/templates/logs.html:71 application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/webmail.html:32
msgid "Time"
msgstr "Zaman"

#: application/templates/base.html:125 application/templates/logs.html:24
#: application/templates/logs.html:73 application/templates/webmail.html:33
msgid "Size"
msgstr "Boyut"

#: application/templates/base.html:126
msgid "Date type"
msgstr "Tarih türü"

#: application/templates/base.html:128
msgid "Date"
msgstr "Tarih"

#: application/templates/base.html:129
msgid "This setting is prioritized."
msgstr "Bu ayar önceliklidir."

#: application/templates/base.html:130
msgid "Combine multiple values with commas."
msgstr "Birden fazla değeri virgülle birleştirin."

#: application/templates/base.html:131
msgid "Put dictionary in dict folder"
msgstr "Sözlüğü dict klasörüne koyun"

#: application/templates/base.html:156 application/templates/home.html:16
msgid "Logout"
msgstr "Çıkış"

#: application/templates/base.html:158 application/templates/home.html:21
#: application/templates/login.html:3 application/templates/login.html:22
#: application/templates/login.html:33
msgid "Login"
msgstr "Giriş"

#: application/templates/base.html:160 application/templates/signup.html:3
#: application/templates/signup.html:19 application/templates/signup.html:43
msgid "Signup"
msgstr "Kaydol"

#: application/templates/base.html:189 application/templates/home.html:15
#: application/templates/my.html:3
msgid "Feeds"
msgstr "RSSler"

#: application/templates/base.html:190 application/templates/settings.html:3
msgid "Settings"
msgstr "Ayarlar"

#: application/templates/base.html:191 application/templates/logs.html:3
msgid "Logs"
msgstr "Kayıtlar"

#: application/templates/base.html:193
msgid "Advanced"
msgstr "Gelişmiş"

#: application/templates/base.html:194 application/templates/library.html:3
msgid "Shared"
msgstr "Paylaşılan"

#: application/templates/base.html:195 application/templates/reader.html:6
msgid "Reader"
msgstr "Okuyucu"

#: application/templates/book_audiolator.html:22
#: application/templates/book_summarizer.html:16
#: application/templates/book_translator.html:19
msgid "State"
msgstr "Durum"

#: application/templates/book_audiolator.html:24
msgid "Send Ebook and Audio"
msgstr "E-kitap ve Ses Gönder"

#: application/templates/book_audiolator.html:25
msgid "Send Audio only"
msgstr "Sadece Ses Gönder"

#: application/templates/book_audiolator.html:26
msgid "Disable TTS"
msgstr "TTS'yi Devre Dışı Bırak"

#: application/templates/book_audiolator.html:30
msgid "Send Audio To"
msgstr "Sesi Gönder"

#: application/templates/book_audiolator.html:31
msgid "Empty to use Kindle_email"
msgstr "Kindle_email kullanmak için boş bırakın"

#: application/templates/book_audiolator.html:35
msgid "TTS Engine"
msgstr "TTS Motoru"

#: application/templates/book_audiolator.html:41
#: application/templates/book_summarizer.html:35
#: application/templates/book_translator.html:32
msgid "Api Host"
msgstr "Api Host"

#: application/templates/book_audiolator.html:42
#: application/templates/book_summarizer.html:36
#: application/templates/book_summarizer.html:75
msgid "Leave empty to use default"
msgstr "Boş bırakın, varsayılan değeri kullanın"

#: application/templates/book_audiolator.html:46
msgid "Region"
msgstr "Bölge"

#: application/templates/book_audiolator.html:53
#: application/templates/book_summarizer.html:39
#: application/templates/book_translator.html:36
msgid "Api Key"
msgstr "Api Key"

#: application/templates/book_audiolator.html:66
msgid "Voice name"
msgstr "Ses adı"

#: application/templates/book_audiolator.html:73
msgid "Voice speed"
msgstr "Ses hızı"

#: application/templates/book_audiolator.html:75
msgid "Extra slow"
msgstr "Ekstra yavaş"

#: application/templates/book_audiolator.html:76
msgid "Slow"
msgstr "Yavaş"

#: application/templates/book_audiolator.html:77
#: application/templates/book_audiolator.html:87
#: application/templates/book_audiolator.html:97
msgid "Medium"
msgstr "Orta"

#: application/templates/book_audiolator.html:78
msgid "Fast"
msgstr "Hızlı"

#: application/templates/book_audiolator.html:79
msgid "Extra fast"
msgstr "Ekstra hızlı"

#: application/templates/book_audiolator.html:83
msgid "Voice pitch"
msgstr "Ses tonu"

#: application/templates/book_audiolator.html:85
msgid "Extra low"
msgstr "Ekstra düşük"

#: application/templates/book_audiolator.html:86
msgid "Low"
msgstr "Düşük"

#: application/templates/book_audiolator.html:88
msgid "High"
msgstr "Yüksek"

#: application/templates/book_audiolator.html:89
msgid "Extra high"
msgstr "Ekstra yüksek"

#: application/templates/book_audiolator.html:93
msgid "Voice volume"
msgstr "Ses hacmi"

#: application/templates/book_audiolator.html:95
msgid "Extra soft"
msgstr "Ekstra yumuşak"

#: application/templates/book_audiolator.html:96
msgid "Soft"
msgstr "Yumuşak"

#: application/templates/book_audiolator.html:98
msgid "Loud"
msgstr "Yüksek sesli"

#: application/templates/book_audiolator.html:99
msgid "Extra loud"
msgstr "Ekstra yüksek sesli"

#: application/templates/book_audiolator.html:105
#: application/templates/book_summarizer.html:80
#: application/templates/book_translator.html:72
msgid "Apply to all subscribed recipes"
msgstr "Tüm abone olunan tarifelere uygula"

#: application/templates/book_audiolator.html:110
#: application/templates/book_summarizer.html:85
#: application/templates/book_translator.html:77
msgid ""
"Note: Enabling this feature will significantly increase consumed CPU "
"instance hours."
msgstr ""
"Not: Bu özelliği etkinleştirmek, kullanılan CPU örnek saatlerini önemli "
"ölçüde artırır."

#: application/templates/book_audiolator.html:119
#: application/templates/book_summarizer.html:94
#: application/templates/book_translator.html:86
msgid "Test (Please save settings firstly)"
msgstr "Test (Lütfen önce ayarları kaydedin)"

#: application/templates/book_audiolator.html:121
#: application/templates/book_summarizer.html:96
#: application/templates/book_translator.html:88
msgid "Text"
msgstr "Metin"

#: application/templates/book_audiolator.html:127
msgid "Your browser does not support the audio element."
msgstr "Tarayıcınız ses öğesini desteklemiyor."

#: application/templates/book_summarizer.html:29
msgid "Model"
msgstr "Model"

#: application/templates/book_summarizer.html:45
msgid "Auto"
msgstr "Otomatik"

#: application/templates/book_summarizer.html:56
msgid "Summary words"
msgstr "Özet kelimeleri"

#: application/templates/book_summarizer.html:70
msgid "Summary style"
msgstr "Özet Tarzı"

#: application/templates/book_summarizer.html:74
msgid "Placeholders available:"
msgstr "Mevcut yer tutucular:"

#: application/templates/book_summarizer.html:74
msgid "Custom prompt"
msgstr "Özel prompt"

#: application/templates/book_summarizer.html:100
msgid "Summary"
msgstr "Özet"

#: application/templates/book_translator.html:37
msgid "One key per line"
msgstr "Satır başına bir tuş"

#: application/templates/book_translator.html:40
#: application/templates/word_lookup.html:51
msgid "Source language"
msgstr "Kaynak dil"

#: application/templates/book_translator.html:46
msgid "Target language"
msgstr "Hedef dil"

#: application/templates/book_translator.html:52
msgid "Translation Position"
msgstr "Çeviri Konumu"

#: application/templates/book_translator.html:54
msgid "Below original"
msgstr "Orjinalin altında"

#: application/templates/book_translator.html:55
msgid "Above original"
msgstr "Orjinalin üstünde"

#: application/templates/book_translator.html:56
msgid "Left to original"
msgstr "Orjinalin solunda"

#: application/templates/book_translator.html:57
msgid "Right to original"
msgstr "Orjinalin sağından"

#: application/templates/book_translator.html:58
msgid "Translated text only"
msgstr "Sadece çevrilmiş metin"

#: application/templates/book_translator.html:62
msgid "Original text style"
msgstr "Orijinal metin stili"

#: application/templates/book_translator.html:66
msgid "Translated text style"
msgstr "Çevrilmiş metin stili"

#: application/templates/book_translator.html:92
msgid "Translation"
msgstr "Çeviri"

#: application/templates/change_password.html:15
msgid "Old password"
msgstr "Eski şifre"

#: application/templates/change_password.html:19
#: application/templates/reset_password.html:31
#: application/templates/reset_password.html:32
msgid "New password"
msgstr "Yeni şifre"

#: application/templates/change_password.html:23
#: application/templates/reset_password.html:35
#: application/templates/reset_password.html:36
#: application/templates/signup.html:29
#: application/templates/user_account.html:23
msgid "Confirm password"
msgstr "Şifreyi onayla"

#: application/templates/change_password.html:31
msgid "Share key"
msgstr "Paylaşma anahtarı"

#: application/templates/change_password.html:37
msgid "Confirm Change"
msgstr "Değişikliği Onayla"

#: application/templates/debug_cmd.html:3
msgid "Debug cmd"
msgstr "Hata ayıklama komutu"

#: application/templates/home.html:3
msgid "Home"
msgstr "Ana Sayfa"

#: application/templates/home.html:10 application/templates/login.html:18
msgid "You are in DEMO mode. Logging out will delete all data."
msgstr "DEMO modundasınız. Oturumu kapattığınızda tüm veriler silinecektir."

#: application/templates/home.html:12
msgid "Sharing Joyful News Every Step of the Way"
msgstr "Her Adımda Mutlu Haberler Paylaşma"

#: application/templates/home.html:31
msgid "Inherited From Calibre"
msgstr "Calibre Kodları Kullanıldı."

#: application/templates/home.html:34
#, python-format
msgid ""
"Empowered by %(calibre)s, you can easily create e-books on a Python-"
"supported online platform and seamlessly transfer them to your e-reader or "
"other reading devices."
msgstr ""
"%(calibre)s tarafından güçlendirilmiş olarak, Python destekli bir çevrimiçi "
"platformda kolayca e-kitaplar oluşturabilir ve bunları e-okuyucunuza veya "
"diğer okuma cihazlarına sorunsuz bir şekilde aktarabilirsiniz."

#: application/templates/home.html:42
msgid "Share Your Ideas"
msgstr "Fikrinizi Paylaşın"

#: application/templates/home.html:45
#, python-format
msgid ""
"With the open-source %(kindleear)s application, you can set up your own "
"server to deliver daily news feeds to your e-reader and effortlessly share "
"the service with friends."
msgstr ""
"Açık kaynaklı %(kindleear)s uygulaması ile kendi sunucunuzu kurarak günlük "
"haber akışlarınızı e-okuyucunuza teslim edebilir ve hizmeti dostlarınızla "
"kolayca paylaşabilirsiniz."

#: application/templates/library.html:50 application/templates/my.html:61
msgid "Search"
msgstr "Ara"

#: application/templates/login.html:38 application/view/login.py:197
#: application/view/login.py:204
msgid ""
"The website does not allow registration. You can ask the owner for an "
"account."
msgstr ""
"Yeni bir hesap açmak için bir site yönetici ile iletişime geçmeniz "
"gerekmektedir."

#: application/templates/logs.html:11
msgid "Only display last 20 logs"
msgstr "Sadece son 20 işlem kaydını gösterir."

#: application/templates/logs.html:26 application/templates/logs.html:75
msgid "Status"
msgstr "Durum"

#: application/templates/logs.html:58
msgid "Logs of other users"
msgstr "Diğer kullanıcıların işlem kayıtları"

#: application/templates/my.html:12 application/templates/settings.html:153
msgid "Custom RSS"
msgstr "Özel RSS"

#: application/templates/my.html:23
msgid "Content Embedded"
msgstr "Gömülü içerik"

#: application/templates/my.html:27
msgid "Deliver Separately"
msgstr "Ayrı teslim et"

#: application/templates/my.html:46
msgid "Subscribed"
msgstr "Abone olundu"

#: application/templates/my.html:51
msgid "Library"
msgstr "Kütüphane"

#: application/templates/my.html:51
msgid "get more recipes"
msgstr "daha fazla tarif alın"

#: application/templates/my.html:68
msgid "Subscription to selected recipe successful."
msgstr "Seçilen tarife abonelik başarılı."

#: application/templates/my.html:71
msgid "Bookmarklet"
msgstr "Kitap modu"

#: application/templates/my.html:75
msgid "Send to Kindle"
msgstr "Kindle'e Gönder"

#: application/templates/my.html:79
msgid "Subscribe with KindleEar"
msgstr "KindleEar ile Abone Ol"

#: application/templates/my.html:82
msgid "Drag and drop this link to your bookmarks"
msgstr "Bu bağlantıyı yer imlerinize sürükleyip bırakın"

#: application/templates/my.html:86
msgid "Chrome extension"
msgstr "Chrome eklentisi"

#: application/templates/my.html:87
msgid "Edge extension"
msgstr "Edge eklentisi"

#: application/templates/my.html:89
msgid "Browser extensions also available"
msgstr "Tarayıcı eklentileri de mevcut"

#: application/templates/reader.html:57
msgid "Push current book"
msgstr "Şu anki kitabı gönder"

#: application/templates/reader.html:63
msgid "Push current article"
msgstr "Şu anki makaleyi gönder"

#: application/templates/reader.html:71
msgid "Delete selected books"
msgstr "Seçili kitapları sil"

#: application/templates/reader.html:77
msgid "Allow click links"
msgstr "Bağlantıları tıklamaya izin ver"

#: application/templates/reader.html:83
msgid "Top-left dict mode"
msgstr "Üst sol sözlük modu"

#: application/templates/reader.html:89
msgid "Dark mode"
msgstr "Karanlık modu"

#: application/templates/reader.html:95
msgid "eInk mode"
msgstr "eInk modu"

#: application/templates/reader.html:101
msgid "Increase font size"
msgstr "Yazı tipi boyutunu artır"

#: application/templates/reader.html:107
msgid "Decrease font size"
msgstr "Yazı tipi boyutunu azalt"

#: application/templates/reader.html:113
msgid "Visualize Touch Regions"
msgstr "Dokunma bölgelerini görselleştir"

#: application/templates/reader.html:119
msgid "Help"
msgstr "Yardım"

#: application/templates/reader.html:125
#: application/templates/reader_404.html:135
msgid "Menu"
msgstr "Menü"

#: application/templates/reader.html:139
msgid "Collapse all"
msgstr "Tümünü daralt"

#: application/templates/reader.html:144
msgid "Expand all"
msgstr "Tümünü genişlet"

#: application/templates/reader.html:149
#: application/templates/reader_404.html:140
msgid "Prev"
msgstr "Önceki"

#: application/templates/reader.html:154
#: application/templates/reader_404.html:143
msgid "Next page"
msgstr "Sonraki sayfa"

#: application/templates/reader.html:192
msgid "Pushed successfully."
msgstr "Başarıyla itildi"

#: application/templates/reader.html:193
msgid "There are currently no books or articles being read."
msgstr "Şu anda okunan herhangi bir kitap veya makale bulunmamaktadır."

#: application/templates/reset_password.html:3
#: application/templates/reset_password.html:41
msgid "Reset password"
msgstr "Şifreyi sıfırla"

#: application/templates/settings.html:14
msgid ""
"Your account will pause after {0}, please log in again before it expires."
msgstr ""
"Bu hesabın gönderileri şu tarihte duraklatılacak: {0} duraklatılmaması için "
"bu tarihten önce giriş yapınız."

#: application/templates/settings.html:23
msgid "Base"
msgstr "Temel"

#: application/templates/settings.html:25
msgid "Auto delivery"
msgstr "Otomatik Teslimat"

#: application/templates/settings.html:28
msgid "Recipes and custom RSS"
msgstr "Tarifler ve özel RSS"

#: application/templates/settings.html:29
msgid "Recipes only"
msgstr "Sadece tarifler"

#: application/templates/settings.html:30
msgid "Disable all"
msgstr "Tümünü devre dışı bırak"

#: application/templates/settings.html:34
msgid "Kindle E-mail"
msgstr "Kindle E-mail"

#: application/templates/settings.html:35
msgid "Seperated by comma"
msgstr "Virgülle ayrılmış"

#: application/templates/settings.html:39
msgid "Delivery mode"
msgstr "Teslimat modu"

#: application/templates/settings.html:42
msgid "Email delivery & online reading"
msgstr "E-posta teslimatı ve çevrimiçi okuma"

#: application/templates/settings.html:43
msgid "Email delivery"
msgstr "E-posta teslimatı"

#: application/templates/settings.html:44
msgid "Online reading"
msgstr "Çevrimiçi okuma"

#: application/templates/settings.html:48
msgid "Retention days for online books"
msgstr "Çevrimiçi kitaplar için saklama günleri"

#: application/templates/settings.html:48
msgid "Web shelf"
msgstr "Web kitaplık"

#: application/templates/settings.html:53
#: application/templates/settings.html:176
msgid "2 Days"
msgstr "2 Günlük"

#: application/templates/settings.html:54
#: application/templates/settings.html:177
msgid "3 Days"
msgstr "3 Günlük"

#: application/templates/settings.html:55
#: application/templates/settings.html:178
msgid "4 Days"
msgstr "4 Günlük"

#: application/templates/settings.html:56
#: application/templates/settings.html:179
msgid "5 Days"
msgstr "5 Günlük"

#: application/templates/settings.html:57
#: application/templates/settings.html:180
msgid "6 Days"
msgstr "6 Günlük"

#: application/templates/settings.html:64
msgid "Time zone"
msgstr "Saat dilimi"

#: application/templates/settings.html:89
msgid "Delivery time"
msgstr "Teslimat zamanı"

#: application/templates/settings.html:97
msgid "Book type"
msgstr "Dosya tipi"

#: application/templates/settings.html:104
msgid "Determines final file size"
msgstr "Son dosya boyutunu belirler"

#: application/templates/settings.html:104
msgid "Device type"
msgstr "Cihaz tipi"

#: application/templates/settings.html:113
msgid "Title format"
msgstr "Başlık Formatı"

#: application/templates/settings.html:116
msgid "Title Only"
msgstr "Yalnızca Başlık"

#: application/templates/settings.html:130
msgid "Remove hyperlinks"
msgstr "Bağlantıları kaldır"

#: application/templates/settings.html:133
msgid "Do not remove hyperlinks"
msgstr "Bağlantıları kaldırma"

#: application/templates/settings.html:134
msgid "Remove image links"
msgstr "Resim bağlantılarını kaldır"

#: application/templates/settings.html:135
msgid "Remove text links"
msgstr "Metin bağlantılarını kaldır"

#: application/templates/settings.html:136
msgid "Remove all hyperlinks"
msgstr "Tüm bağlantıları kaldır"

#: application/templates/settings.html:140
msgid "Navbar"
msgstr "Navigasyon Çubuğu"

#: application/templates/settings.html:144
msgid "Top Center"
msgstr "Üst Orta"

#: application/templates/settings.html:145
msgid "Top Left"
msgstr "Üst Sol"

#: application/templates/settings.html:146
msgid "Bottom Center"
msgstr "Alt Orta"

#: application/templates/settings.html:147
msgid "Bottom Left"
msgstr "Alt Sol"

#: application/templates/settings.html:159
msgid "Sets the lookup dictionary"
msgstr "Sözlük aramasını ayarlar"

#: application/templates/settings.html:171
msgid "Oldest article"
msgstr "En eski içerik?"

#: application/templates/settings.html:185
msgid "Time format"
msgstr "Zaman formatı"

#: application/templates/settings.html:198
msgid "Author format"
msgstr "Başlık Formatı"

#: application/templates/settings.html:215
msgid "Send Mail Service"
msgstr "Posta servisi gönder"

#: application/templates/settings.html:217
msgid "Service"
msgstr "Servis"

#: application/templates/settings.html:225
msgid "ApiKey"
msgstr "ApiKey"

#: application/templates/settings.html:229
msgid "SecretKey"
msgstr "SecretKey"

#: application/templates/settings.html:237
msgid "Port"
msgstr "Port"

#: application/templates/settings.html:249
msgid "Save path"
msgstr "Kayıt yolu"

#: application/templates/settings.html:257
#, python-format
msgid ""
"Important: Please activate your kindle firstly, then goto %(personal)s Page "
"and add %(sender)s to 'Approved Personal Document E-mail List'."
msgstr ""
"Önemli: Lütfen önce Kindle'ınızı etkinleştirin, ardından %(personal)s "
"Sayfasına gidin ve %(sender)s'yi 'Onaylanmış Kişisel Belge E-posta "
"Listesi'ne ekleyin."

#: application/templates/settings.html:257
msgid "Personal Document Settings"
msgstr "Ayarları kaydet"

#: application/templates/settings.html:263
#, python-format
msgid ""
"You have not yet set up your email address. Please go to the %(admin)s page "
"to add your email address firstly."
msgstr ""
"Henüz e-posta adresinizi ayarlamadınız. Lütfen öncelikle e-posta adresinizi "
"eklemek için %(admin)s sayfasına gidin."

#: application/templates/settings.html:271
msgid "Send Test Email"
msgstr "Test E-postası Gönder"

#: application/templates/signup.html:38
msgid "Invitation code"
msgstr "Davetiye kodu"

#: application/templates/user_account.html:3
msgid "User account"
msgstr "Kullanıcı hesabı"

#: application/templates/user_account.html:42
msgid "Never expire"
msgstr "hiç sona ermeyen"

#: application/templates/webmail.html:3
msgid "Webmail"
msgstr "Webmail"

#: application/templates/webmail.html:17
msgid "Refresh"
msgstr "Yenile"

#: application/templates/webmail.html:18
msgid "Read/Unread"
msgstr "Okundu/Okunmadı"

#: application/templates/webmail.html:19
msgid "Reply"
msgstr "Yanıtla"

#: application/templates/webmail.html:20
msgid "Forward"
msgstr "İlet"

#: application/templates/webmail.html:21
msgid "Fwd as Attach"
msgstr "Ek olarak ilet"

#: application/templates/word_lookup.html:71
msgid "Word"
msgstr "Kelime"

#: application/view/admin.py:48 application/view/adv.py:437
#: application/view/adv.py:528 application/view/settings.py:67
#: application/view/translator.py:88 application/view/translator.py:172
#: application/view/translator.py:254
msgid "Settings Saved!"
msgstr "Ayarlar Kaydedildi!"

#: application/view/admin.py:57 application/view/admin.py:64
#: application/view/admin.py:91
msgid "Add account"
msgstr "Hesap ekle"

#: application/view/admin.py:63 application/view/admin.py:103
#: application/view/admin.py:135
msgid "You do not have sufficient privileges."
msgstr "Yeterli yetkiniz yok."

#: application/view/admin.py:79 application/view/login.py:44
#: application/view/login.py:222
msgid "The username includes unsafe chars."
msgstr "Kullanıcı adı güvensiz karakterler içeriyor."

#: application/view/admin.py:83 application/view/login.py:224
msgid "Already exist the username."
msgstr "Kullanıcı adı zaten var."

#: application/view/admin.py:88
msgid "The password includes non-ascii chars."
msgstr "Şifre ascii olmayan karakterler içeriyor."

#: application/view/admin.py:107 application/view/admin.py:132
#: application/view/admin.py:163 application/view/extension.py:32
#: application/view/extension.py:61
msgid "The username '{}' does not exist."
msgstr "'{}' kullanıcı adı mevcut değil."

#: application/view/admin.py:123
msgid "The password will not be changed if the fields are empties."
msgstr "Alanlar boş bırakılırsa şifre değiştirilmeyecek."

#: application/view/admin.py:130 application/view/admin.py:184
msgid "Change"
msgstr "Değiştir"

#: application/view/admin.py:181
msgid "Change success."
msgstr "Değişim başarılı."

#: application/view/admin.py:194
msgid "The old password is wrong."
msgstr "Eski şifre yanlış."

#: application/view/admin.py:196
msgid "Changes saved successfully."
msgstr "Değişiklikler başarıyla kaydedildi."

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108 application/view/adv.py:109
#: application/view/adv.py:110 application/view/adv.py:111
#: application/view/adv.py:112 application/view/adv.py:113
msgid "Append hyperlink '{}' to article"
msgstr "'{}' linkini makaleye ekle"

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108
msgid "Save to {}"
msgstr "{} kaydedildi"

#: application/view/adv.py:104
msgid "evernote"
msgstr "evernote"

#: application/view/adv.py:105
msgid "wiz"
msgstr "wiz"

#: application/view/adv.py:106
msgid "pocket"
msgstr "pocket"

#: application/view/adv.py:107
msgid "instapaper"
msgstr "instapaper"

#: application/view/adv.py:108
msgid "wallabag"
msgstr "wallabag"

#: application/view/adv.py:109 application/view/adv.py:110
#: application/view/adv.py:111 application/view/adv.py:112
msgid "Share on {}"
msgstr "{} üzerinde paylaş"

#: application/view/adv.py:109
msgid "weibo"
msgstr "weibo"

#: application/view/adv.py:110
msgid "facebook"
msgstr "facebook"

#: application/view/adv.py:112
msgid "tumblr"
msgstr "tumblr"

#: application/view/adv.py:113
msgid "Open in browser"
msgstr "Tarayıcıda aç"

#: application/view/adv.py:114
msgid "Append qrcode of url to article"
msgstr "Makaleye URL'nin QR kodunu ekle"

#: application/view/adv.py:381 application/view/share.py:54
#: application/view/subscribe.py:250
msgid "Unknown command: {}"
msgstr "Bilinmeyen komut: {}"

#: application/view/adv.py:439 application/view/adv.py:530
msgid "The format is invalid."
msgstr "Format geçersiz."

#: application/view/adv.py:562
msgid "Authorization Error!<br/>{}"
msgstr "Yetkilendirme Hatası!<br/>{}"

#: application/view/adv.py:583
msgid "Success authorized by Pocket!"
msgstr "Pocket tarafından yetkilendirilen başarı!"

#: application/view/adv.py:589
msgid ""
"Failed to request authorization of Pocket!<hr/>See details "
"below:<br/><br/>{}"
msgstr ""
"Pocket yetkilendirme isteği başarısız oldu!<hr/>Aşağıdaki ayrıntılara "
"bakın:<br/><br/>{}"

#: application/view/adv.py:610
msgid "The Instapaper service encountered an error. Please try again later."
msgstr ""
"Instapaper servisi bir hata ile karşılaştı. Lütfen daha sonra tekrar "
"deneyin."

#: application/view/adv.py:623
msgid "Request type [{}] unsupported"
msgstr "İstek türü [{}] desteklenmiyor"

#: application/view/deliver.py:82 application/view/login.py:169
#: application/view/share.py:41
msgid "The username does not exist or the email is empty."
msgstr "Kullanıcı adı mevcut değil veya e-posta boş."

#: application/view/deliver.py:109
msgid "The following recipes has been added to the push queue."
msgstr "Aşağıdaki tarifler itme kuyruğuna eklendi."

#: application/view/deliver.py:112
msgid "There are no recipes to deliver."
msgstr "Teslim edilecek tarif yok."

#: application/view/extension.py:69
msgid "The rules parameter is invalid."
msgstr "Kurallar parametresi geçersiz."

#: application/view/library.py:32
msgid "Cannot fetch data from {}, status: {}"
msgstr "{}, durumundan veri alınamıyor: {}"

#: application/view/library.py:48 application/view/subscribe.py:238
#: application/view/subscribe.py:380 application/view/subscribe.py:409
#: application/view/subscribe.py:416 application/view/translator.py:30
msgid "The recipe does not exist."
msgstr "Tarif mevcut değil."

#: application/view/login.py:27 application/view/login.py:76
msgid "Please use {}/{} to login at first time."
msgstr "İlk giriş için kullanıcı adı:'{}' ve şifre: '{}'"

#: application/view/login.py:40
msgid "Username is empty."
msgstr "Kullanıcı adı boş."

#: application/view/login.py:42
msgid "The len of username reached the limit of 25 chars."
msgstr "Kullanıcı adının uzunluğu 25 karakter sınırına ulaştı."

#: application/view/login.py:80
msgid "Forgot password?"
msgstr "Forgot password?"

#: application/view/login.py:148 application/view/login.py:275
msgid "The token is wrong or expired."
msgstr "Belirteç yanlış veya süresi dolmuş."

#: application/view/login.py:151
msgid "Please input the correct username and email to reset password."
msgstr ""
"Şifreyi sıfırlamak için lütfen doğru kullanıcı adı ve e-posta adresini "
"girin."

#: application/view/login.py:153
msgid "The email of account '{name}' is {email}."
msgstr "'{name}' hesabının e-postası {email}."

#: application/view/login.py:174
msgid "Reset password success, Please close this page and login again."
msgstr ""
"Şifre sıfırlama başarılı, Lütfen bu sayfayı kapatın ve yeniden giriş yapın."

#: application/view/login.py:177
msgid "The email you input is not associated with this account."
msgstr "Girdiğiniz e-posta bu hesapla ilişkilendirilmemiştir."

#: application/view/login.py:186
msgid "The link to reset your password has been sent to your email."
msgstr ""
"Şifrenizi sıfırlamak için gerekli bağlantı e-postanıza gönderilmiştir."

#: application/view/login.py:187
msgid "Please check your email inbox within 24 hours."
msgstr "Lütfen e-posta gelen kutunuzu 24 saat içinde kontrol edin."

#: application/view/login.py:218
msgid "The invitation code is invalid."
msgstr "Davetiye kodu geçersiz."

#: application/view/login.py:226
msgid ""
"Failed to create an account. Please contact the administrator for "
"assistance."
msgstr ""
"Bir hesap oluşturulamadı. Yardım için lütfen yöneticiyle iletişime geçin."

#: application/view/login.py:236
msgid "Successfully created account."
msgstr "Hesap başarıyla oluşturuldu."

#: application/view/login.py:247
msgid "Reset KindleEar password"
msgstr "KindleEar şifrenizi sıfırlama"

#: application/view/login.py:248
msgid "This is an automated email. Please do not reply to it."
msgstr "Bu otomatik bir e-postadır. Lütfen yanıt vermeyin."

#: application/view/login.py:249
msgid "You can click the following link to reset your KindleEar password."
msgstr ""
"KindleEar şifrenizi sıfırlamak için aşağıdaki bağlantıya tıklayabilirsiniz."

#: application/view/reader.py:88
msgid "The article is missing?"
msgstr "Makale eksik mi?"

#: application/view/reader.py:190 application/view/translator.py:121
#: application/view/translator.py:205 application/view/translator.py:287
msgid "The text is empty."
msgstr "Metin boş."

#: application/view/reader.py:239
msgid "No definitions found for '{}'."
msgstr "'{}' için tanım bulunamadı."

#: application/view/reader.py:240
msgid "Did you mean?"
msgstr "Demek istediniz mi?"

#: application/view/reader.py:324 application/view/reader.py:331
msgid "Failed to push: {}"
msgstr "İtilemedi: {}"

#: application/view/reader.py:379
msgid "Failed to create ebook."
msgstr "E-kitap oluşturulamadı."

#: application/view/settings.py:131
msgid ""
"You have not yet set up your email address. Please go to the 'Account' page "
"to add your email address firstly."
msgstr ""
"Email adresinizi henüz ayarlamadınız. Lütfen e-posta adresinizi eklemek için"
" 'Yönetim' sayfasına öncelikle gidin."

#: application/view/settings.py:215
msgid "English"
msgstr "İngilizce"

#: application/view/settings.py:216
msgid "Simplified Chinese"
msgstr "简体中文"

#: application/view/settings.py:217
msgid "Traditional Chinese"
msgstr "繁体中文"

#: application/view/settings.py:218
msgid "French"
msgstr "Fransızca"

#: application/view/settings.py:219
msgid "Spanish"
msgstr "İspanyolca"

#: application/view/settings.py:220
msgid "Portuguese"
msgstr "Portekizce"

#: application/view/settings.py:221
msgid "German"
msgstr "Almanca"

#: application/view/settings.py:222
msgid "Italian"
msgstr "İtalyanca"

#: application/view/settings.py:223
msgid "Japanese"
msgstr "Japonca"

#: application/view/settings.py:224
msgid "Russian"
msgstr "Rusça"

#: application/view/settings.py:225
msgid "Turkish"
msgstr "Türkçe"

#: application/view/settings.py:226
msgid "Korean"
msgstr "Koreli"

#: application/view/settings.py:227
msgid "Arabic"
msgstr "Arapça"

#: application/view/settings.py:228
msgid "Czech"
msgstr "Çek"

#: application/view/settings.py:229
msgid "Dutch"
msgstr "Flemenkçe"

#: application/view/settings.py:230
msgid "Greek"
msgstr "Yunan"

#: application/view/settings.py:231
msgid "Hindi"
msgstr "Hintçe"

#: application/view/settings.py:232
msgid "Malaysian"
msgstr "Malezyalı"

#: application/view/settings.py:233
msgid "Bengali"
msgstr "Bengal"

#: application/view/settings.py:234
msgid "Persian"
msgstr "Farsça"

#: application/view/settings.py:235
msgid "Urdu"
msgstr "Urduca"

#: application/view/settings.py:236
msgid "Swahili"
msgstr "Svahili"

#: application/view/settings.py:237
msgid "Vietnamese"
msgstr "Vietnam"

#: application/view/settings.py:238
msgid "Punjabi"
msgstr "Pencap"

#: application/view/settings.py:239
msgid "Javanese"
msgstr "Cava"

#: application/view/settings.py:240
msgid "Tagalog"
msgstr "Tagalog"

#: application/view/settings.py:241
msgid "Hausa"
msgstr "Hausa"

#: application/view/settings.py:242
msgid "Thai"
msgstr "Taylandca"

#: application/view/settings.py:243
msgid "Polish"
msgstr "Lehçe"

#: application/view/settings.py:244
msgid "Romanian"
msgstr "Rumen"

#: application/view/settings.py:245
msgid "Hungarian"
msgstr "Macarca"

#: application/view/settings.py:246
msgid "Swedish"
msgstr "İsveççe"

#: application/view/settings.py:247
msgid "Hebrew"
msgstr "İbranice"

#: application/view/settings.py:248
msgid "Norwegian"
msgstr "Norveççe"

#: application/view/settings.py:249
msgid "Finnish"
msgstr "Fince"

#: application/view/settings.py:250
msgid "Danish"
msgstr "Danca"

#: application/view/settings.py:251
msgid "Ukrainian"
msgstr "Ukraynaca"

#: application/view/settings.py:252
msgid "Tamil"
msgstr "Tamilce"

#: application/view/settings.py:253
msgid "Marathi"
msgstr "Marathi"

#: application/view/settings.py:254
msgid "Burmese"
msgstr "Burmaca"

#: application/view/settings.py:255
msgid "Amharic"
msgstr "Amharca"

#: application/view/settings.py:256
msgid "Azerbaijani"
msgstr "Azerbaycanca"

#: application/view/settings.py:257
msgid "Kazakh"
msgstr "Kazakça"

#: application/view/settings.py:258
msgid "Serbian"
msgstr "Српски"

#: application/view/settings.py:259
msgid "Croatian"
msgstr "Hrvatski"

#: application/view/settings.py:260
msgid "Slovak"
msgstr "Slovenčina"

#: application/view/settings.py:261
msgid "Bulgarian"
msgstr "Български"

#: application/view/settings.py:262
msgid "Icelandic"
msgstr "Íslenska"

#: application/view/settings.py:263
msgid "Lithuanian"
msgstr "Lietuvių"

#: application/view/settings.py:264
msgid "Latvian"
msgstr "Latviešu"

#: application/view/settings.py:265
msgid "Estonian"
msgstr "Eesti"

#: application/view/settings.py:266
msgid "Macedonian"
msgstr "Македонски"

#: application/view/settings.py:267
msgid "Albanian"
msgstr "Shqip"

#: application/view/settings.py:268
msgid "Galician"
msgstr "Galego"

#: application/view/settings.py:269
msgid "Welsh"
msgstr "Cymraeg"

#: application/view/settings.py:270
msgid "Basque"
msgstr "Euskara"

#: application/view/settings.py:271
msgid "Nepali"
msgstr "नेपाली"

#: application/view/share.py:60
msgid "There is no {} email yet."
msgstr "Henüz {} e-postası bulunmamaktadır."

#: application/view/share.py:108 application/view/share.py:133
#: application/view/share.py:155 application/view/share.py:177
msgid "Saved to your {} account."
msgstr "{} hesabınıza kaydedildi."

#: application/view/share.py:111 application/view/share.py:129
#: application/view/share.py:158 application/view/share.py:179
msgid "Failed save to {}."
msgstr "{}'e kaydetme işlemi başarısız oldu."

#: application/view/share.py:112 application/view/share.py:130
#: application/view/share.py:159 application/view/share.py:180
msgid "Reason :"
msgstr "Neden :"

#: application/view/share.py:121
msgid "Unauthorized {} account!"
msgstr "{} tarafından yetkilendirilen başarı!"

#: application/view/share.py:134
msgid "See details below:"
msgstr "Aşağıdaki ayrıntılara bakın:"

#: application/view/share.py:157
msgid "Unknown: {}"
msgstr "Bilinmeyen: {}"

#: application/view/subscribe.py:81 application/view/subscribe.py:167
msgid "Duplicated subscription!"
msgstr "Duplicated subscription!"

#: application/view/subscribe.py:126
msgid "The Title or Url is empty."
msgstr "Başlık veya URL boş."

#: application/view/subscribe.py:139
msgid "Failed to fetch the recipe."
msgstr "Tarif alınamadı."

#: application/view/subscribe.py:153 application/view/subscribe.py:331
msgid "Failed to save the recipe. Error:"
msgstr "Tarif kaydedilemedi. Hata:"

#: application/view/subscribe.py:195
msgid "The Rss does not exist."
msgstr "Rss mevcut değil."

#: application/view/subscribe.py:278
msgid "You can only delete the uploaded recipe."
msgstr "Yalnızca yüklenen tarifi silebilirsiniz."

#: application/view/subscribe.py:283
msgid "The recipe have been subscribed, please unsubscribe it before delete."
msgstr "Tarif abone olunmuş, silmeden önce aboneliği iptal edin."

#: application/view/subscribe.py:304 application/view/translator.py:51
#: application/view/translator.py:104 application/view/translator.py:117
#: application/view/translator.py:140 application/view/translator.py:188
#: application/view/translator.py:201 application/view/translator.py:230
#: application/view/translator.py:270 application/view/translator.py:283
msgid "This recipe has not been subscribed to yet."
msgstr "Bu tarife henüz abone olunmadı."

#: application/view/subscribe.py:318
msgid "Can not read uploaded file, Error:"
msgstr "Yüklenen dosya okunamıyor, Hata:"

#: application/view/subscribe.py:326
msgid ""
"Failed to decode the recipe. Please ensure that your recipe is saved in "
"utf-8 encoding."
msgstr ""
"Tarif çözümlenemedi. Lütfen tarifinizin utf-8 kodlamasında kaydedildiğinden "
"emin olun."

#: application/view/subscribe.py:349
msgid "Cannot find any subclass of BasicNewsRecipe."
msgstr "BasicNewsRecipe sınıfının hiçbir alt sınıfı bulunamıyor."

#: application/view/subscribe.py:354
msgid "The recipe is already in the library."
msgstr "Tarif zaten kütüphanede."

#: application/view/subscribe.py:387
msgid "The login information for this recipe has been cleared."
msgstr "Bu tarifin giriş bilgileri temizlendi."

#: application/view/subscribe.py:391
msgid "The login information for this recipe has been saved."
msgstr "Bu tarifin giriş bilgileri kaydedildi."

#: application/view/translator.py:81 application/view/translator.py:165
msgid "The api key is required."
msgstr "API anahtarı gereklidir."
