# Chinese translations for KindleEar.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the KindleEar project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: KindleEar v3.0.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-10 19:49-0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: KindleEar <<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"Generated-By: Babel 2.14.0\n"

#: application/templates/admin.html:3 application/templates/base.html:53
#: application/templates/base.html:192 application/templates/settings.html:263
msgid "Account"
msgstr "Account"

#: application/templates/admin.html:19
msgid "Signup settings"
msgstr "Impostazioni di registrazione"

#: application/templates/admin.html:19
#: application/templates/adv_calibre_options.html:18
#: application/templates/adv_proxy.html:18
msgid "Save"
msgstr "Salva"

#: application/templates/admin.html:21
#: application/templates/user_account.html:31
msgid "Email service"
msgstr "Servizio email"

#: application/templates/admin.html:23
#: application/templates/user_account.html:34
msgid "Same as admin"
msgstr "Uguale all'amministratore"

#: application/templates/admin.html:24
#: application/templates/user_account.html:35
msgid "Independent"
msgstr "Indipendente"

#: application/templates/admin.html:28
msgid "Signup type"
msgstr "Tipo di registrazione"

#: application/templates/admin.html:30
msgid "Public"
msgstr "Pubblico"

#: application/templates/admin.html:31
msgid "One time code"
msgstr "Codice usa e getta"

#: application/templates/admin.html:32
msgid "Permanent code"
msgstr "Codice permanente"

#: application/templates/admin.html:36
msgid "Invitation codes"
msgstr "Codici di invito"

#: application/templates/admin.html:37
msgid "one code per line"
msgstr "un codice per riga"

#: application/templates/admin.html:43
msgid "Accounts"
msgstr "Accounts"

#: application/templates/admin.html:43
#: application/templates/adv_inboundmail.html:61
#: application/templates/my.html:34 application/view/admin.py:57
#: application/view/admin.py:64 application/view/admin.py:91
msgid "Add"
msgstr "Aggiungi"

#: application/templates/admin.html:54
#: application/templates/adv_archive.html:69
#: application/templates/home.html:19 application/templates/login.html:24
#: application/templates/logs.html:70
#: application/templates/reset_password.html:19
#: application/templates/reset_password.html:20
#: application/templates/settings.html:241
#: application/templates/signup.html:21
#: application/templates/user_account.html:15
msgid "Username"
msgstr "Nome utente"

#: application/templates/admin.html:55
msgid "AutoSend"
msgstr "Invio automatico"

#: application/templates/admin.html:56
#: application/templates/change_password.html:27
#: application/templates/reset_password.html:26
#: application/templates/signup.html:33
#: application/templates/user_account.html:27
msgid "Email"
msgstr "Email"

#: application/templates/admin.html:57
#: application/templates/user_account.html:39
msgid "Expiration"
msgstr "Scadenza"

#: application/templates/admin.html:58
msgid "Operation"
msgstr "Operazione"

#: application/templates/admin.html:65
msgid "Yes"
msgstr "Sì"

#: application/templates/admin.html:65
msgid "No"
msgstr "No"

#: application/templates/admin.html:68
msgid "Never"
msgstr "Mai"

#: application/templates/admin.html:70
#: application/templates/adv_inboundmail.html:26
#: application/templates/settings.html:58
#: application/templates/settings.html:181
#: application/templates/user_account.html:43
msgid "7 Days"
msgstr "7 giorni"

#: application/templates/admin.html:72
#: application/templates/user_account.html:44
msgid "1 Month"
msgstr "1 mese"

#: application/templates/admin.html:74
#: application/templates/user_account.html:45
msgid "3 Months"
msgstr "3 mesi"

#: application/templates/admin.html:76
#: application/templates/user_account.html:46
msgid "6 Months"
msgstr "6 mesi"

#: application/templates/admin.html:78
#: application/templates/user_account.html:47
msgid "1 Year"
msgstr "1 anno"

#: application/templates/admin.html:80
#: application/templates/user_account.html:48
msgid "2 Years"
msgstr "2 anni"

#: application/templates/admin.html:85
#: application/templates/change_password.html:3
#: application/templates/change_password.html:13 application/view/admin.py:129
#: application/view/admin.py:183
msgid "Edit account"
msgstr "Modifica account"

#: application/templates/admin.html:89
#: application/templates/adv_inboundmail.html:54
#: application/templates/adv_uploadcss.html:31
#: application/templates/base.html:25 application/templates/webmail.html:22
msgid "Delete"
msgstr "Elimina"

#: application/templates/adv_archive.html:3
#: application/templates/adv_archive.html:14
#: application/templates/adv_base.html:57
#: application/templates/adv_base.html:61
msgid "Archive"
msgstr "Archivia"

#: application/templates/adv_archive.html:15
msgid "Append hyperlinks for archiving or sharing."
msgstr "Aggiungi collegamenti ipertestuali per archiviare o condividere."

#: application/templates/adv_archive.html:40
msgid "Authorized"
msgstr "Autorizzato"

#: application/templates/adv_archive.html:42
msgid "Authorize"
msgstr "Autorizza"

#: application/templates/adv_archive.html:53
msgid "Email or Username"
msgstr "Email o nome utente"

#: application/templates/adv_archive.html:56
#: application/templates/adv_archive.html:72
#: application/templates/base.html:54 application/templates/home.html:20
#: application/templates/login.html:28 application/templates/settings.html:245
#: application/templates/signup.html:25
#: application/templates/user_account.html:19
msgid "Password"
msgstr "Password"

#: application/templates/adv_archive.html:59
#: application/templates/adv_archive.html:75
#: application/templates/base.html:64
msgid "Verify"
msgstr "Verifica"

#: application/templates/adv_archive.html:78
msgid "client_id"
msgstr "client_id"

#: application/templates/adv_archive.html:81
msgid "client_secret"
msgstr "client_secret"

#: application/templates/adv_archive.html:84
#: application/templates/settings.html:233
msgid "Host"
msgstr "Host"

#: application/templates/adv_archive.html:119
#: application/templates/adv_dict.html:79
#: application/templates/adv_inboundmail.html:34
#: application/templates/book_audiolator.html:113
#: application/templates/book_summarizer.html:88
#: application/templates/book_translator.html:80
#: application/templates/settings.html:269
msgid "Save settings"
msgstr "Salva impostazioni"

#: application/templates/adv_base.html:39
#: application/templates/adv_base.html:43
#: application/templates/adv_delivernow.html:8
msgid "Deliver Now"
msgstr "Consegna ora"

#: application/templates/adv_base.html:48
#: application/templates/adv_base.html:52
#: application/templates/adv_inboundmail.html:3
#: application/templates/adv_inboundmail.html:9
#: application/templates/adv_inboundmail.html:14
msgid "Inbound Mail"
msgstr "Posta in arrivo"

#: application/templates/adv_base.html:66
#: application/templates/adv_base.html:70
#: application/templates/adv_dict.html:3
#: application/templates/adv_dict.html:13
#: application/templates/reader.html:134
msgid "Dictionary"
msgstr "Dizionario"

#: application/templates/adv_base.html:75
#: application/templates/adv_base.html:83
#: application/templates/adv_proxy.html:3
#: application/templates/adv_proxy.html:12
msgid "Proxy"
msgstr "Proxy"

#: application/templates/adv_base.html:92
#: application/templates/adv_base.html:96
#: application/templates/adv_import.html:8
msgid "Import Feeds"
msgstr "Importa feed"

#: application/templates/adv_base.html:101
#: application/templates/adv_base.html:105
msgid "Cover Image"
msgstr "Immagine di copertura"

#: application/templates/adv_base.html:110
#: application/templates/adv_base.html:114
#: application/templates/adv_uploadcss.html:3
msgid "Stylesheet"
msgstr "Foglio di stile"

#: application/templates/adv_base.html:119
#: application/templates/adv_base.html:123
#: application/templates/adv_calibre_options.html:3
#: application/templates/adv_calibre_options.html:12
msgid "Calibre Options"
msgstr "Opzioni Calibre"

#: application/templates/adv_calibre_options.html:13
msgid "Set the parameters for Calibre, in JSON dictionary format."
msgstr "Imposta i parametri per Calibre, in formato dizionario JSON."

#: application/templates/adv_delivernow.html:3
msgid "Deliver now"
msgstr "Consegna ora"

#: application/templates/adv_delivernow.html:9
msgid "Deliver selected recipes now."
msgstr "Consegna ora le ricette selezionate."

#: application/templates/adv_delivernow.html:12
msgid "There are no recipes subscribed"
msgstr "Non ci sono ricette sottoscritte"

#: application/templates/adv_delivernow.html:17
#: application/templates/base.html:101
msgid "Sep"
msgstr "Sep"

#: application/templates/adv_delivernow.html:22
msgid "Select all"
msgstr "Seleziona tutto"

#: application/templates/adv_delivernow.html:23
msgid "Select none"
msgstr "Seleziona nessuno"

#: application/templates/adv_delivernow.html:28
msgid "Deliver"
msgstr "Consegna"

#: application/templates/adv_dict.html:15
msgid "Set up dictionaries for online reading."
msgstr "Configura dizionari per la lettura online."

#: application/templates/adv_dict.html:18
#: application/templates/adv_dict.html:40
#: application/templates/adv_dict.html:62
msgid "Book language"
msgstr "Lingua del libro"

#: application/templates/adv_dict.html:27
#: application/templates/adv_dict.html:49
#: application/templates/adv_dict.html:66
#: application/templates/book_summarizer.html:23
#: application/templates/book_translator.html:26
#: application/templates/word_lookup.html:59
msgid "Engine"
msgstr "Motore"

#: application/templates/adv_dict.html:33
#: application/templates/adv_dict.html:55
#: application/templates/adv_dict.html:72
#: application/templates/word_lookup.html:65
msgid "Database"
msgstr "Database"

#: application/templates/adv_dict.html:63
msgid "Other languages"
msgstr "Altre lingue"

#: application/templates/adv_dict.html:81
#: application/templates/word_lookup.html:3
#: application/templates/word_lookup.html:79
msgid "Word lookup"
msgstr "Ricerca parole"

#: application/templates/adv_dict.html:86 application/view/reader.py:29
#: application/view/reader.py:86
msgid "Online reading feature has not been activated yet."
msgstr "La funzionalità di lettura online non è stata attivata."

#: application/templates/adv_import.html:3
#: application/templates/adv_import.html:19
msgid "Import"
msgstr "Importa"

#: application/templates/adv_import.html:9
msgid "Import custom rss from an OPML file."
msgstr "Importa rss personalizzato da un file OPML."

#: application/templates/adv_import.html:15
msgid "Import as fulltext rss by default"
msgstr "Importa come rss fulltext per impostazione predefinita"

#: application/templates/adv_import.html:20
msgid "Download"
msgstr "Scarica"

#: application/templates/adv_inboundmail.html:11
msgid ""
"To enable the inbound email feature, you also need to configure the "
"whitelist."
msgstr ""
"Per abilitare la funzione di posta in arrivo, è necessario configurare anche"
" la whitelist."

#: application/templates/adv_inboundmail.html:16
#: application/templates/adv_uploadcover.html:15
#: application/templates/book_summarizer.html:19
#: application/templates/book_translator.html:22
#: application/templates/settings.html:143
msgid "Disable"
msgstr "Disabilita"

#: application/templates/adv_inboundmail.html:17
msgid "Forward Only"
msgstr "Solo inoltro"

#: application/templates/adv_inboundmail.html:18
msgid "Save Only"
msgstr "Solo salvataggio"

#: application/templates/adv_inboundmail.html:19
msgid "Save and Forward"
msgstr "Salva e inoltra"

#: application/templates/adv_inboundmail.html:23
msgid "Email Retention"
msgstr "Conservazione email"

#: application/templates/adv_inboundmail.html:25
#: application/templates/settings.html:52
#: application/templates/settings.html:175
msgid "1 Day"
msgstr "1 giorno"

#: application/templates/adv_inboundmail.html:27
#: application/templates/settings.html:59
msgid "30 Days"
msgstr "30 giorni"

#: application/templates/adv_inboundmail.html:28
#: application/templates/settings.html:51
#: application/templates/settings.html:174
msgid "No limit"
msgstr "Nessun limite"

#: application/templates/adv_inboundmail.html:36
msgid "Open webmail"
msgstr "Apri webmail"

#: application/templates/adv_inboundmail.html:44
msgid "White List"
msgstr "Lista bianca"

#: application/templates/adv_inboundmail.html:46
#, python-format
msgid ""
"Emails sent to %(name)sxxx@%(mailHost)s will be forwarded to your kindle "
"email."
msgstr ""
"Le email inviate a %(name)sxxx@%(mailHost)s saranno inoltrate al tuo "
"indirizzo kindle."

#: application/templates/adv_inboundmail.html:47
msgid "Example"
msgstr "Esempio"

#: application/templates/adv_inboundmail.html:59
msgid "Please input mail address"
msgstr "Inserisci indirizzo email"

#: application/templates/adv_proxy.html:13
msgid "Supports"
msgstr "Supporta"

#: application/templates/adv_proxy.html:24
#: application/templates/adv_proxy.html:29
#: application/templates/book_audiolator.html:132
#: application/templates/book_summarizer.html:105
#: application/templates/book_translator.html:97
msgid "Test"
msgstr "Test"

#: application/templates/adv_uploadcover.html:3
msgid "Cover image"
msgstr "Immagine di copertura"

#: application/templates/adv_uploadcover.html:9
msgid "Upload cover image"
msgstr "Carica immagine di copertura"

#: application/templates/adv_uploadcover.html:10
msgid ""
"Upload cover images from local with an aspect ratio of approximately 0.625."
msgstr ""
"Carica immagini di copertura locali con un rapporto d'aspetto di circa "
"0,625."

#: application/templates/adv_uploadcover.html:13
msgid "Include cover"
msgstr "Includi copertura"

#: application/templates/adv_uploadcover.html:16
#: application/templates/book_summarizer.html:18
#: application/templates/book_translator.html:21
msgid "Enable"
msgstr "Abilita"

#: application/templates/adv_uploadcover.html:20
msgid "Rule for cover"
msgstr "Regola per copertura"

#: application/templates/adv_uploadcover.html:22
msgid "Random"
msgstr "Casuale"

#: application/templates/adv_uploadcover.html:23
#: application/templates/base.html:127
msgid "Weekday"
msgstr "Giorno della settimana"

#: application/templates/adv_uploadcover.html:49
msgid "Upload/Update"
msgstr "Carica/Aggiorna"

#: application/templates/adv_uploadcss.html:22
msgid "Upload stylesheet"
msgstr "Carica foglio di stile"

#: application/templates/adv_uploadcss.html:23
msgid "Upload a stylesheet from local (accept utf-8 only)."
msgstr "Carica un foglio di stile locale (accetta solo utf-8)."

#: application/templates/adv_uploadcss.html:30
msgid "Upload"
msgstr "Carica"

#: application/templates/autoback.html:3
msgid "Auto back"
msgstr "Ritorno automatico"

#: application/templates/autoback.html:28
msgid "Auto back to previous page after 5 seconds"
msgstr "Ritorno automatico alla pagina precedente dopo 5 secondi"

#: application/templates/autoback.html:29
#: application/templates/tipsback.html:15
msgid "Click to back"
msgstr "Clicca per tornare"

#: application/templates/base.html:24 application/templates/reader.html:189
msgid "Confirm Deletion"
msgstr "Conferma eliminazione"

#: application/templates/base.html:26
msgid "Delete (Ctrl for no confirm)"
msgstr "Elimina (Ctrl per non confermare)"

#: application/templates/base.html:27
msgid "View Source Code"
msgstr "Visualizza codice sorgente"

#: application/templates/base.html:28
msgid "Subscribe (Deliver Separately)"
msgstr "Abbonati (consegna separata)"

#: application/templates/base.html:29
msgid "Subscribe"
msgstr "Abbonati"

#: application/templates/base.html:30
msgid "Cannot add this custom rss, Error:"
msgstr "Impossibile aggiungere questo rss personalizzato, Errore:"

#: application/templates/base.html:31
msgid "Cannot delete this feed, Error:"
msgstr "Impossibile eliminare questo feed, Errore:"

#: application/templates/base.html:32
msgid "Fulltext"
msgstr "Testo completo"

#: application/templates/base.html:33 application/templates/base.html:43
msgid "Share"
msgstr "Condividi"

#: application/templates/base.html:34 application/templates/reader.html:190
msgid "Are you sure to delete?"
msgstr "Sei sicuro di voler eliminare?"

#: application/templates/base.html:35
msgid "Report to the server that this feed is invalid."
msgstr "Segnala al server che questo feed non è valido."

#: application/templates/base.html:36
msgid "Are you sure to REMOVE ALL CUSTOM RSS?"
msgstr "Sei sicuro di voler RIMUOVERE TUTTI I RSS PERSONALIZZATI?"

#: application/templates/base.html:37
msgid "Share links, share happiness"
msgstr "Condividi link, condividi felicità"

#: application/templates/base.html:38
msgid "Category"
msgstr "Categoria"

#: application/templates/base.html:39
#: application/templates/book_audiolator.html:58
#: application/templates/book_summarizer.html:43
#: application/templates/settings.html:159
msgid "Language"
msgstr "Lingua"

#: application/templates/base.html:40
msgid ""
"Please write a category in text field if the one you wish is not in the "
"list."
msgstr ""
"Scrivi una categoria nel campo di testo se quella che desideri non è nella "
"lista."

#: application/templates/base.html:41
msgid "Ok"
msgstr "Ok"

#: application/templates/base.html:42
msgid "Cancel"
msgstr "Annulla"

#: application/templates/base.html:44
msgid "Language code invalid"
msgstr "Codice lingua non valido"

#: application/templates/base.html:45
msgid "Thank you for sharing."
msgstr "Grazie per aver condiviso."

#: application/templates/base.html:46 application/templates/reader.html:159
msgid "Close"
msgstr "Chiudi"

#: application/templates/base.html:47
msgid "Unsubscribe"
msgstr "Annulla iscrizione"

#: application/templates/base.html:48
msgid "Cannot subscribe this recipe, Error:"
msgstr "Impossibile iscriversi a questa ricetta, Errore:"

#: application/templates/base.html:49
msgid "Are you sure to Unsubscribe ({0})?"
msgstr "Sei sicuro di voler annullare l'iscrizione ({0})?"

#: application/templates/base.html:50
msgid "Cannot unsubscribe this recipe, Error:"
msgstr "Impossibile annullare l'iscrizione a questa ricetta, Errore:"

#: application/templates/base.html:51
msgid "The recipe is already subscribed."
msgstr "La ricetta è già iscritta."

#: application/templates/base.html:52
msgid "Website login lnformation"
msgstr "Informazioni di accesso al sito web"

#: application/templates/base.html:55
msgid "Submit"
msgstr "Invia"

#: application/templates/base.html:56
msgid ""
"If any field is left blank, the server will clear the saved login "
"information."
msgstr ""
"Se un campo viene lasciato vuoto, il server cancellerà le informazioni di "
"accesso salvate."

#: application/templates/base.html:57
msgid "Cannot set the website login information, Error:"
msgstr "Impossibile impostare le informazioni di accesso al sito web, Errore:"

#: application/templates/base.html:58 application/templates/my.html:58
msgid "Upload custom recipe"
msgstr "Carica ricetta personalizzata"

#: application/templates/base.html:59
msgid "Congratulations"
msgstr "Congratulazioni"

#: application/templates/base.html:60
msgid "Thanks"
msgstr "Grazie"

#: application/templates/base.html:61
msgid ""
"Your recipe has been uploaded, and it can be found in the Library section. "
"If you dont see it, please make sure to switch to the correct language."
msgstr ""
"La tua ricetta è stata caricata e può essere trovata nella sezione "
"Biblioteca. Se non la vedi, assicurati di passare alla lingua corretta."

#: application/templates/base.html:62
msgid "Your recipe have been deleted."
msgstr "La tua ricetta è stata eliminata."

#: application/templates/base.html:63
msgid "Kindleify Selection"
msgstr "Selezione Kindleify"

#: application/templates/base.html:65
msgid "Verified"
msgstr "Verificato"

#: application/templates/base.html:66 application/view/login.py:79
#: application/view/share.py:157
msgid "The username does not exist or password is wrong."
msgstr "Il nome utente non esiste o la password è errata."

#: application/templates/base.html:67
msgid "The file you chosen is not an acceptable type."
msgstr "Il file che hai scelto non è un tipo accettabile."

#: application/templates/base.html:68
msgid "The file have been uploaded successfully."
msgstr "Il file è stato caricato con successo."

#: application/templates/base.html:69 application/templates/library.html:67
msgid "This feed has been successfully subscribed."
msgstr "Questo feed è stato iscritto con successo."

#: application/templates/base.html:70
msgid "Thank you for your feedback, this feed will be reviewed soon."
msgstr "Grazie per il tuo feedback, questo feed verrà esaminato presto."

#: application/templates/base.html:71
msgid "Are you confirming to share the recipe ({0})?"
msgstr "Confermi di condividere la ricetta ({0})?"

#: application/templates/base.html:72
msgid "[All]"
msgstr "[Tutti]"

#: application/templates/base.html:73
msgid "[By Time]"
msgstr "[Per tempo]"

#: application/templates/base.html:74
msgid "[Random]"
msgstr "[Casuale]"

#: application/templates/base.html:75
msgid "[Uncategoried]"
msgstr "[Non categorizzato]"

#: application/templates/base.html:76
msgid "There are no links found."
msgstr "Non sono stati trovati link."

#: application/templates/base.html:77
msgid "Invalid report"
msgstr "Rapporto non valido"

#: application/templates/base.html:78
msgid "Are you confirming that this link is invalid or off the cloud?"
msgstr "Confermi che questo link è invalido o non più disponibile?"

#: application/templates/base.html:79
msgid "Customize delivery time"
msgstr "Personalizza ora di consegna"

#: application/templates/base.html:80 application/templates/settings.html:72
msgid "Delivery days"
msgstr "Giorni di consegna"

#: application/templates/base.html:81 application/templates/settings.html:74
msgid "Mon"
msgstr "Lun"

#: application/templates/base.html:82 application/templates/settings.html:76
msgid "Tue"
msgstr "Mar"

#: application/templates/base.html:83 application/templates/settings.html:78
msgid "Wed"
msgstr "Mer"

#: application/templates/base.html:84 application/templates/settings.html:80
msgid "Thu"
msgstr "Gio"

#: application/templates/base.html:85 application/templates/settings.html:82
msgid "Fri"
msgstr "Ven"

#: application/templates/base.html:86 application/templates/settings.html:84
msgid "Sat"
msgstr "Sab"

#: application/templates/base.html:87 application/templates/settings.html:86
msgid "Sun"
msgstr "Dom"

#: application/templates/base.html:88
msgid "Delivery times"
msgstr "Orari di consegna"

#: application/templates/base.html:89
msgid ""
"The customized delivery time for the recipe has been successfully saved."
msgstr ""
"L'orario di consegna personalizzato per la ricetta è stato salvato con "
"successo."

#: application/templates/base.html:90
msgid "The account have been deleted."
msgstr "L'account è stato eliminato."

#: application/templates/base.html:91 application/view/share.py:147
msgid "The username or password is empty."
msgstr "Il nome utente o la password è vuoto."

#: application/templates/base.html:92 application/view/admin.py:81
#: application/view/admin.py:165 application/view/admin.py:191
#: application/view/login.py:220 application/view/login.py:273
msgid "The two new passwords are dismatch."
msgstr "Le due nuove password non corrispondono."

#: application/templates/base.html:93
msgid "Password changed successfully."
msgstr "Password cambiata con successo."

#: application/templates/base.html:94
msgid "Account added successfully."
msgstr "Account aggiunto con successo."

#: application/templates/base.html:95 application/view/login.py:128
msgid "login required"
msgstr "accesso richiesto"

#: application/templates/base.html:96
msgid "Upload cover files successfully."
msgstr "Caricamento file di copertura riuscito."

#: application/templates/base.html:97
msgid ""
"Total size of the files you selected exceeds 16MB. Please reduce the image "
"resolution or upload in batches."
msgstr ""
"La dimensione totale dei file selezionati supera i 16 MB. Riduci la "
"risoluzione dell'immagine o carica in lotti."

#: application/templates/base.html:98
#: application/templates/book_translator.html:3
#: application/templates/book_translator.html:17
msgid "Bilingual Translator"
msgstr "Traduttore bilingue"

#: application/templates/base.html:99
#: application/templates/book_summarizer.html:3
#: application/templates/book_summarizer.html:14
msgid "AI Summarizer"
msgstr "Riassuntore AI"

#: application/templates/base.html:100
msgid "Upl"
msgstr "Upl"

#: application/templates/base.html:102
msgid "Log"
msgstr "Log"

#: application/templates/base.html:103
msgid "Emb"
msgstr "Emb"

#: application/templates/base.html:104
msgid "Tr"
msgstr "Tr"

#: application/templates/base.html:105
msgid "Tts"
msgstr "Tts"

#: application/templates/base.html:106
msgid "Ai"
msgstr "Ai"

#: application/templates/base.html:107
msgid ""
"The test email has been successfully sent to the following addresses. Please"
" check your inbox or spam folder to confirm its delivery. Depending on your "
"email server, there may be a slight delay."
msgstr ""
"L'email di prova è stata inviata con successo agli indirizzi seguenti. "
"Controlla la tua casella di posta o la cartella spam per confermare la "
"consegna. A seconda del tuo server di posta, potrebbe esserci un piccolo "
"ritardo."

#: application/templates/base.html:108
msgid "Processing..."
msgstr "Elaborazione in corso..."

#: application/templates/base.html:109
msgid "The configuration validation is correct."
msgstr "La convalida della configurazione è corretta."

#: application/templates/base.html:110 application/templates/logs.html:23
#: application/templates/logs.html:72 application/templates/my.html:17
#: application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/settings.html:155
msgid "Title"
msgstr "Titolo"

#: application/templates/base.html:111
#: application/templates/book_audiolator.html:3
#: application/templates/book_audiolator.html:20
msgid "Text to Speech"
msgstr "Testo in voce"

#: application/templates/base.html:112
msgid "Action"
msgstr "Azione"

#: application/templates/base.html:113
msgid "File"
msgstr "File"

#: application/templates/base.html:114
msgid "Upload Only"
msgstr "Carica solo"

#: application/templates/base.html:115
msgid "Send"
msgstr "Invia"

#: application/templates/base.html:116 application/templates/logs.html:54
msgid "There is nothing here."
msgstr "Non c'è nulla qui."

#: application/templates/base.html:117
msgid "Please select a single item."
msgstr "Seleziona un solo elemento."

#: application/templates/base.html:118 application/templates/reader.html:191
msgid "Please select at least one item."
msgstr "Seleziona almeno un elemento."

#: application/templates/base.html:119 application/view/admin.py:77
#: application/view/admin.py:152 application/view/admin.py:189
#: application/view/adv.py:459 application/view/extension.py:34
#: application/view/extension.py:63 application/view/inbound_email.py:470
#: application/view/inbound_email.py:478 application/view/inbound_email.py:493
#: application/view/inbound_email.py:514 application/view/login.py:216
#: application/view/login.py:245 application/view/reader.py:109
#: application/view/reader.py:126 application/view/share.py:37
msgid "Some parameters are missing or wrong."
msgstr "Alcuni parametri sono mancanti o errati."

#: application/templates/base.html:120
msgid "The email has been sent."
msgstr "L'email è stata inviata."

#: application/templates/base.html:121 application/templates/webmail.html:29
msgid "From"
msgstr "Da"

#: application/templates/base.html:122 application/templates/logs.html:25
#: application/templates/logs.html:74 application/templates/webmail.html:30
msgid "To"
msgstr "A"

#: application/templates/base.html:123 application/templates/webmail.html:31
msgid "Subject"
msgstr "Oggetto"

#: application/templates/base.html:124 application/templates/logs.html:22
#: application/templates/logs.html:71 application/templates/settings.html:117
#: application/templates/settings.html:118
#: application/templates/webmail.html:32
msgid "Time"
msgstr "Tempo"

#: application/templates/base.html:125 application/templates/logs.html:24
#: application/templates/logs.html:73 application/templates/webmail.html:33
msgid "Size"
msgstr "Dimensione"

#: application/templates/base.html:126
msgid "Date type"
msgstr "Tipo di data"

#: application/templates/base.html:128
msgid "Date"
msgstr "Data"

#: application/templates/base.html:129
msgid "This setting is prioritized."
msgstr "Questa impostazione ha la priorità."

#: application/templates/base.html:130
msgid "Combine multiple values with commas."
msgstr "Combina più valori con le virgole."

#: application/templates/base.html:131
msgid "Put dictionary in dict folder"
msgstr "Metti il dizionario nella cartella dict"

#: application/templates/base.html:156 application/templates/home.html:16
msgid "Logout"
msgstr "Esci"

#: application/templates/base.html:158 application/templates/home.html:21
#: application/templates/login.html:3 application/templates/login.html:22
#: application/templates/login.html:33
msgid "Login"
msgstr "Accedi"

#: application/templates/base.html:160 application/templates/signup.html:3
#: application/templates/signup.html:19 application/templates/signup.html:43
msgid "Signup"
msgstr "Iscriviti"

#: application/templates/base.html:189 application/templates/home.html:15
#: application/templates/my.html:3
msgid "Feeds"
msgstr "Feed"

#: application/templates/base.html:190 application/templates/settings.html:3
msgid "Settings"
msgstr "Opzioni"

#: application/templates/base.html:191 application/templates/logs.html:3
msgid "Logs"
msgstr "Logs"

#: application/templates/base.html:193
msgid "Advanced"
msgstr "Avanzate"

#: application/templates/base.html:194 application/templates/library.html:3
msgid "Shared"
msgstr "Condiviso"

#: application/templates/base.html:195 application/templates/reader.html:6
msgid "Reader"
msgstr "Lettore"

#: application/templates/book_audiolator.html:22
#: application/templates/book_summarizer.html:16
#: application/templates/book_translator.html:19
msgid "State"
msgstr "Stato"

#: application/templates/book_audiolator.html:24
msgid "Send Ebook and Audio"
msgstr "Invia Ebook e Audio"

#: application/templates/book_audiolator.html:25
msgid "Send Audio only"
msgstr "Invia solo audio"

#: application/templates/book_audiolator.html:26
msgid "Disable TTS"
msgstr "Disabilita TTS"

#: application/templates/book_audiolator.html:30
msgid "Send Audio To"
msgstr "Invia audio a"

#: application/templates/book_audiolator.html:31
msgid "Empty to use Kindle_email"
msgstr "Lascia vuoto per usare Kindle_email"

#: application/templates/book_audiolator.html:35
msgid "TTS Engine"
msgstr "Motore TTS"

#: application/templates/book_audiolator.html:41
#: application/templates/book_summarizer.html:35
#: application/templates/book_translator.html:32
msgid "Api Host"
msgstr "Host API"

#: application/templates/book_audiolator.html:42
#: application/templates/book_summarizer.html:36
#: application/templates/book_summarizer.html:75
msgid "Leave empty to use default"
msgstr "Lascia vuoto per usare predefinito"

#: application/templates/book_audiolator.html:46
msgid "Region"
msgstr "Regione"

#: application/templates/book_audiolator.html:53
#: application/templates/book_summarizer.html:39
#: application/templates/book_translator.html:36
msgid "Api Key"
msgstr "Api Key"

#: application/templates/book_audiolator.html:66
msgid "Voice name"
msgstr "Nome voce"

#: application/templates/book_audiolator.html:73
msgid "Voice speed"
msgstr "Velocità voce"

#: application/templates/book_audiolator.html:75
msgid "Extra slow"
msgstr "Molto lento"

#: application/templates/book_audiolator.html:76
msgid "Slow"
msgstr "Lento"

#: application/templates/book_audiolator.html:77
#: application/templates/book_audiolator.html:87
#: application/templates/book_audiolator.html:97
msgid "Medium"
msgstr "Medio"

#: application/templates/book_audiolator.html:78
msgid "Fast"
msgstr "Veloce"

#: application/templates/book_audiolator.html:79
msgid "Extra fast"
msgstr "Molto veloce"

#: application/templates/book_audiolator.html:83
msgid "Voice pitch"
msgstr "Tono voce"

#: application/templates/book_audiolator.html:85
msgid "Extra low"
msgstr "Molto basso"

#: application/templates/book_audiolator.html:86
msgid "Low"
msgstr "Basso"

#: application/templates/book_audiolator.html:88
msgid "High"
msgstr "Alto"

#: application/templates/book_audiolator.html:89
msgid "Extra high"
msgstr "Molto alto"

#: application/templates/book_audiolator.html:93
msgid "Voice volume"
msgstr "Volume voce"

#: application/templates/book_audiolator.html:95
msgid "Extra soft"
msgstr "Molto soft"

#: application/templates/book_audiolator.html:96
msgid "Soft"
msgstr "Soft"

#: application/templates/book_audiolator.html:98
msgid "Loud"
msgstr "Alto"

#: application/templates/book_audiolator.html:99
msgid "Extra loud"
msgstr "Molto alto"

#: application/templates/book_audiolator.html:105
#: application/templates/book_summarizer.html:80
#: application/templates/book_translator.html:72
msgid "Apply to all subscribed recipes"
msgstr "Applica a tutte le ricette sottoscritte"

#: application/templates/book_audiolator.html:110
#: application/templates/book_summarizer.html:85
#: application/templates/book_translator.html:77
msgid ""
"Note: Enabling this feature will significantly increase consumed CPU "
"instance hours."
msgstr ""
"Nota: Abilitando questa funzione si aumenteranno significativamente le ore "
"di utilizzo della CPU."

#: application/templates/book_audiolator.html:119
#: application/templates/book_summarizer.html:94
#: application/templates/book_translator.html:86
msgid "Test (Please save settings firstly)"
msgstr "Test (Salva prima le impostazioni)"

#: application/templates/book_audiolator.html:121
#: application/templates/book_summarizer.html:96
#: application/templates/book_translator.html:88
msgid "Text"
msgstr "Testo"

#: application/templates/book_audiolator.html:127
msgid "Your browser does not support the audio element."
msgstr "Il tuo browser non supporta l'elemento audio."

#: application/templates/book_summarizer.html:29
msgid "Model"
msgstr "Model"

#: application/templates/book_summarizer.html:45
msgid "Auto"
msgstr "Automatico"

#: application/templates/book_summarizer.html:56
msgid "Summary words"
msgstr "Parole riassuntive"

#: application/templates/book_summarizer.html:70
msgid "Summary style"
msgstr "Stile riassunto"

#: application/templates/book_summarizer.html:74
msgid "Placeholders available:"
msgstr "Segnaposti disponibili:"

#: application/templates/book_summarizer.html:74
msgid "Custom prompt"
msgstr "Prompt personalizzato"

#: application/templates/book_summarizer.html:100
msgid "Summary"
msgstr "Riepilogo"

#: application/templates/book_translator.html:37
msgid "One key per line"
msgstr "Una chiave per riga"

#: application/templates/book_translator.html:40
#: application/templates/word_lookup.html:51
msgid "Source language"
msgstr "Lingua di origine"

#: application/templates/book_translator.html:46
msgid "Target language"
msgstr "Lingua di destinazione"

#: application/templates/book_translator.html:52
msgid "Translation Position"
msgstr "Posizione traduzione"

#: application/templates/book_translator.html:54
msgid "Below original"
msgstr "Sotto originale"

#: application/templates/book_translator.html:55
msgid "Above original"
msgstr "Sopra originale"

#: application/templates/book_translator.html:56
msgid "Left to original"
msgstr "A sinistra dell'originale"

#: application/templates/book_translator.html:57
msgid "Right to original"
msgstr "A destra dell'originale"

#: application/templates/book_translator.html:58
msgid "Translated text only"
msgstr "Solo testo tradotto"

#: application/templates/book_translator.html:62
msgid "Original text style"
msgstr "Stile testo originale"

#: application/templates/book_translator.html:66
msgid "Translated text style"
msgstr "Stile testo tradotto"

#: application/templates/book_translator.html:92
msgid "Translation"
msgstr "Traduzione"

#: application/templates/change_password.html:15
msgid "Old password"
msgstr "Vecchia password"

#: application/templates/change_password.html:19
#: application/templates/reset_password.html:31
#: application/templates/reset_password.html:32
msgid "New password"
msgstr "Nuova password"

#: application/templates/change_password.html:23
#: application/templates/reset_password.html:35
#: application/templates/reset_password.html:36
#: application/templates/signup.html:29
#: application/templates/user_account.html:23
msgid "Confirm password"
msgstr "Conferma password"

#: application/templates/change_password.html:31
msgid "Share key"
msgstr "Condividi chiave"

#: application/templates/change_password.html:37
msgid "Confirm Change"
msgstr "Conferma modifica"

#: application/templates/debug_cmd.html:3
msgid "Debug cmd"
msgstr "Comando debug"

#: application/templates/home.html:3
msgid "Home"
msgstr "Home"

#: application/templates/home.html:10 application/templates/login.html:18
msgid "You are in DEMO mode. Logging out will delete all data."
msgstr "Sei in modalità DEMO. Uscendo, verranno eliminati tutti i dati."

#: application/templates/home.html:12
msgid "Sharing Joyful News Every Step of the Way"
msgstr "Condividendo notizie gioiose ad ogni passo"

#: application/templates/home.html:31
msgid "Inherited From Calibre"
msgstr "Ereditato da Calibre"

#: application/templates/home.html:34
#, python-format
msgid ""
"Empowered by %(calibre)s, you can easily create e-books on a Python-"
"supported online platform and seamlessly transfer them to your e-reader or "
"other reading devices."
msgstr ""
"Potenziato da %(calibre)s, puoi facilmente creare e-book su una piattaforma "
"online supportata da Python e trasferirli senza problemi al tuo e-reader o "
"ad altri dispositivi di lettura."

#: application/templates/home.html:42
msgid "Share Your Ideas"
msgstr "Condividi le tue idee"

#: application/templates/home.html:45
#, python-format
msgid ""
"With the open-source %(kindleear)s application, you can set up your own "
"server to deliver daily news feeds to your e-reader and effortlessly share "
"the service with friends."
msgstr ""
"Con l'applicazione open-source %(kindleear)s, puoi configurare il tuo server"
" per inviare notizie quotidiane al tuo e-reader e condividere facilmente il "
"servizio con gli amici."

#: application/templates/library.html:50 application/templates/my.html:61
msgid "Search"
msgstr "Cerca"

#: application/templates/login.html:38 application/view/login.py:197
#: application/view/login.py:204
msgid ""
"The website does not allow registration. You can ask the owner for an "
"account."
msgstr ""
"Il sito non consente la registrazione. Puoi chiedere al proprietario un "
"account."

#: application/templates/logs.html:11
msgid "Only display last 20 logs"
msgstr "Visualizza solo gli ultimi 20 log"

#: application/templates/logs.html:26 application/templates/logs.html:75
msgid "Status"
msgstr "Stato"

#: application/templates/logs.html:58
msgid "Logs of other users"
msgstr "Log di altri utenti"

#: application/templates/my.html:12 application/templates/settings.html:153
msgid "Custom RSS"
msgstr "RSS personalizzato"

#: application/templates/my.html:23
msgid "Content Embedded"
msgstr "Contenuto incorporato"

#: application/templates/my.html:27
msgid "Deliver Separately"
msgstr "Consegna separata"

#: application/templates/my.html:46
msgid "Subscribed"
msgstr "Sottoscritto"

#: application/templates/my.html:51
msgid "Library"
msgstr "Biblioteca"

#: application/templates/my.html:51
msgid "get more recipes"
msgstr "ottieni altre ricette"

#: application/templates/my.html:68
msgid "Subscription to selected recipe successful."
msgstr "Iscrizione alla ricetta selezionata riuscita."

#: application/templates/my.html:71
msgid "Bookmarklet"
msgstr "Bookmarklet"

#: application/templates/my.html:75
msgid "Send to Kindle"
msgstr "Invia a Kindle"

#: application/templates/my.html:79
msgid "Subscribe with KindleEar"
msgstr "Iscriviti con KindleEar"

#: application/templates/my.html:82
msgid "Drag and drop this link to your bookmarks"
msgstr "Trascina e rilascia questo link nei tuoi segnalibri"

#: application/templates/my.html:86
msgid "Chrome extension"
msgstr "Estensione Chrome"

#: application/templates/my.html:87
msgid "Edge extension"
msgstr "Estensione Edge"

#: application/templates/my.html:89
msgid "Browser extensions also available"
msgstr "Estensioni per browser disponibili"

#: application/templates/reader.html:57
msgid "Push current book"
msgstr "Invia libro corrente"

#: application/templates/reader.html:63
msgid "Push current article"
msgstr "Invia articolo corrente"

#: application/templates/reader.html:71
msgid "Delete selected books"
msgstr "Elimina libri selezionati"

#: application/templates/reader.html:77
msgid "Allow click links"
msgstr "Permetti clic sui link"

#: application/templates/reader.html:83
msgid "Top-left dict mode"
msgstr "Modalità dizionario in alto a sinistra"

#: application/templates/reader.html:89
msgid "Dark mode"
msgstr "Modalità scura"

#: application/templates/reader.html:95
msgid "eInk mode"
msgstr "Modalità eInk"

#: application/templates/reader.html:101
msgid "Increase font size"
msgstr "Aumenta la dimensione del carattere"

#: application/templates/reader.html:107
msgid "Decrease font size"
msgstr "Diminuisci la dimensione del carattere"

#: application/templates/reader.html:113
msgid "Visualize Touch Regions"
msgstr "Visualizza le aree di tocco"

#: application/templates/reader.html:119
msgid "Help"
msgstr "Aiuto"

#: application/templates/reader.html:125
#: application/templates/reader_404.html:135
msgid "Menu"
msgstr "Menu"

#: application/templates/reader.html:139
msgid "Collapse all"
msgstr "Riduci tutto"

#: application/templates/reader.html:144
msgid "Expand all"
msgstr "Espandi tutto"

#: application/templates/reader.html:149
#: application/templates/reader_404.html:140
msgid "Prev"
msgstr "Precedente"

#: application/templates/reader.html:154
#: application/templates/reader_404.html:143
msgid "Next page"
msgstr "Pagina successiva"

#: application/templates/reader.html:192
msgid "Pushed successfully."
msgstr "Inviato con successo."

#: application/templates/reader.html:193
msgid "There are currently no books or articles being read."
msgstr "Attualmente non ci sono libri o articoli letti."

#: application/templates/reset_password.html:3
#: application/templates/reset_password.html:41
msgid "Reset password"
msgstr "Reimposta password"

#: application/templates/settings.html:14
msgid ""
"Your account will pause after {0}, please log in again before it expires."
msgstr ""
"Il tuo account si sospenderà dopo {0}, effettua nuovamente l'accesso prima "
"che scada."

#: application/templates/settings.html:23
msgid "Base"
msgstr "Base"

#: application/templates/settings.html:25
msgid "Auto delivery"
msgstr "Consegna automatica"

#: application/templates/settings.html:28
msgid "Recipes and custom RSS"
msgstr "Ricette e RSS personalizzati"

#: application/templates/settings.html:29
msgid "Recipes only"
msgstr "Solo ricette"

#: application/templates/settings.html:30
msgid "Disable all"
msgstr "Disabilita tutto"

#: application/templates/settings.html:34
msgid "Kindle E-mail"
msgstr "E-mail Kindle"

#: application/templates/settings.html:35
msgid "Seperated by comma"
msgstr "Separato da virgola"

#: application/templates/settings.html:39
msgid "Delivery mode"
msgstr "Modalità di consegna"

#: application/templates/settings.html:42
msgid "Email delivery & online reading"
msgstr "Consegna via email e lettura online"

#: application/templates/settings.html:43
msgid "Email delivery"
msgstr "Consegna via email"

#: application/templates/settings.html:44
msgid "Online reading"
msgstr "Lettura online"

#: application/templates/settings.html:48
msgid "Retention days for online books"
msgstr "Giorni di conservazione per i libri online"

#: application/templates/settings.html:48
msgid "Web shelf"
msgstr "Scaffale web"

#: application/templates/settings.html:53
#: application/templates/settings.html:176
msgid "2 Days"
msgstr "2 giorni"

#: application/templates/settings.html:54
#: application/templates/settings.html:177
msgid "3 Days"
msgstr "3 giorni"

#: application/templates/settings.html:55
#: application/templates/settings.html:178
msgid "4 Days"
msgstr "4 giorni"

#: application/templates/settings.html:56
#: application/templates/settings.html:179
msgid "5 Days"
msgstr "5 giorni"

#: application/templates/settings.html:57
#: application/templates/settings.html:180
msgid "6 Days"
msgstr "6 giorni"

#: application/templates/settings.html:64
msgid "Time zone"
msgstr "Fuso orario"

#: application/templates/settings.html:89
msgid "Delivery time"
msgstr "Orario di consegna"

#: application/templates/settings.html:97
msgid "Book type"
msgstr "Tipo di libro"

#: application/templates/settings.html:104
msgid "Determines final file size"
msgstr "Determina la dimensione finale del file"

#: application/templates/settings.html:104
msgid "Device type"
msgstr "Tipo di dispositivo"

#: application/templates/settings.html:113
msgid "Title format"
msgstr "Formato titolo"

#: application/templates/settings.html:116
msgid "Title Only"
msgstr "Solo titolo"

#: application/templates/settings.html:130
msgid "Remove hyperlinks"
msgstr "Rimuovi collegamenti ipertestuali"

#: application/templates/settings.html:133
msgid "Do not remove hyperlinks"
msgstr "Non rimuovere collegamenti ipertestuali"

#: application/templates/settings.html:134
msgid "Remove image links"
msgstr "Rimuovi collegamenti immagine"

#: application/templates/settings.html:135
msgid "Remove text links"
msgstr "Rimuovi collegamenti testo"

#: application/templates/settings.html:136
msgid "Remove all hyperlinks"
msgstr "Rimuovi tutti i collegamenti ipertestuali"

#: application/templates/settings.html:140
msgid "Navbar"
msgstr "Barra di navigazione"

#: application/templates/settings.html:144
msgid "Top Center"
msgstr "Centro alto"

#: application/templates/settings.html:145
msgid "Top Left"
msgstr "Alto a sinistra"

#: application/templates/settings.html:146
msgid "Bottom Center"
msgstr "Centro basso"

#: application/templates/settings.html:147
msgid "Bottom Left"
msgstr "Basso a sinistra"

#: application/templates/settings.html:159
msgid "Sets the lookup dictionary"
msgstr "Imposta il dizionario di ricerca"

#: application/templates/settings.html:171
msgid "Oldest article"
msgstr "Articolo più vecchio"

#: application/templates/settings.html:185
msgid "Time format"
msgstr "Formato orario"

#: application/templates/settings.html:198
msgid "Author format"
msgstr "Formato autore"

#: application/templates/settings.html:215
msgid "Send Mail Service"
msgstr "Servizio invio e-mail"

#: application/templates/settings.html:217
msgid "Service"
msgstr "Servizio"

#: application/templates/settings.html:225
msgid "ApiKey"
msgstr "ApiKey"

#: application/templates/settings.html:229
msgid "SecretKey"
msgstr "SecretKey"

#: application/templates/settings.html:237
msgid "Port"
msgstr "Porta"

#: application/templates/settings.html:249
msgid "Save path"
msgstr "Percorso di salvataggio"

#: application/templates/settings.html:257
#, python-format
msgid ""
"Important: Please activate your kindle firstly, then goto %(personal)s Page "
"and add %(sender)s to 'Approved Personal Document E-mail List'."
msgstr ""
"Importante: Attiva prima il tuo Kindle, poi vai alla pagina %(personal)s e "
"aggiungi %(sender)s alla 'Lista e-mail documenti personali approvati'."

#: application/templates/settings.html:257
msgid "Personal Document Settings"
msgstr "Impostazioni documento personale"

#: application/templates/settings.html:263
#, python-format
msgid ""
"You have not yet set up your email address. Please go to the %(admin)s page "
"to add your email address firstly."
msgstr ""
"Non hai ancora configurato il tuo indirizzo e-mail. Vai alla pagina "
"%(admin)s per aggiungere prima il tuo indirizzo e-mail."

#: application/templates/settings.html:271
msgid "Send Test Email"
msgstr "Invia e-mail di prova"

#: application/templates/signup.html:38
msgid "Invitation code"
msgstr "Codice invito"

#: application/templates/user_account.html:3
msgid "User account"
msgstr "Account utente"

#: application/templates/user_account.html:42
msgid "Never expire"
msgstr "Mai scadere"

#: application/templates/webmail.html:3
msgid "Webmail"
msgstr "Webmail"

#: application/templates/webmail.html:17
msgid "Refresh"
msgstr "Aggiorna"

#: application/templates/webmail.html:18
msgid "Read/Unread"
msgstr "Letto/Non letto"

#: application/templates/webmail.html:19
msgid "Reply"
msgstr "Rispondi"

#: application/templates/webmail.html:20
msgid "Forward"
msgstr "Inoltra"

#: application/templates/webmail.html:21
msgid "Fwd as Attach"
msgstr "Inoltra come allegato"

#: application/templates/word_lookup.html:71
msgid "Word"
msgstr "Parola"

#: application/view/admin.py:48 application/view/adv.py:437
#: application/view/adv.py:528 application/view/settings.py:67
#: application/view/translator.py:88 application/view/translator.py:172
#: application/view/translator.py:254
msgid "Settings Saved!"
msgstr "Impostazioni salvate!"

#: application/view/admin.py:57 application/view/admin.py:64
#: application/view/admin.py:91
msgid "Add account"
msgstr "Aggiungi account"

#: application/view/admin.py:63 application/view/admin.py:103
#: application/view/admin.py:135
msgid "You do not have sufficient privileges."
msgstr "Non hai privilegi sufficienti."

#: application/view/admin.py:79 application/view/login.py:44
#: application/view/login.py:222
msgid "The username includes unsafe chars."
msgstr "Il nome utente include caratteri non sicuri."

#: application/view/admin.py:83 application/view/login.py:224
msgid "Already exist the username."
msgstr "Il nome utente esiste già."

#: application/view/admin.py:88
msgid "The password includes non-ascii chars."
msgstr "La password include caratteri non ASCII."

#: application/view/admin.py:107 application/view/admin.py:132
#: application/view/admin.py:163 application/view/extension.py:32
#: application/view/extension.py:61
msgid "The username '{}' does not exist."
msgstr "Il nome utente '{}' non esiste."

#: application/view/admin.py:123
msgid "The password will not be changed if the fields are empties."
msgstr "La password non verrà modificata se i campi sono vuoti."

#: application/view/admin.py:130 application/view/admin.py:184
msgid "Change"
msgstr "Cambia"

#: application/view/admin.py:181
msgid "Change success."
msgstr "Cambio riuscito."

#: application/view/admin.py:194
msgid "The old password is wrong."
msgstr "La vecchia password è errata."

#: application/view/admin.py:196
msgid "Changes saved successfully."
msgstr "Modifiche salvate con successo."

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108 application/view/adv.py:109
#: application/view/adv.py:110 application/view/adv.py:111
#: application/view/adv.py:112 application/view/adv.py:113
msgid "Append hyperlink '{}' to article"
msgstr "Aggiungi collegamento ipertestuale '{}' all'articolo"

#: application/view/adv.py:104 application/view/adv.py:105
#: application/view/adv.py:106 application/view/adv.py:107
#: application/view/adv.py:108
msgid "Save to {}"
msgstr "Salva in {}"

#: application/view/adv.py:104
msgid "evernote"
msgstr "evernote"

#: application/view/adv.py:105
msgid "wiz"
msgstr "wiz"

#: application/view/adv.py:106
msgid "pocket"
msgstr "pocket"

#: application/view/adv.py:107
msgid "instapaper"
msgstr "instapaper"

#: application/view/adv.py:108
msgid "wallabag"
msgstr "wallabag"

#: application/view/adv.py:109 application/view/adv.py:110
#: application/view/adv.py:111 application/view/adv.py:112
msgid "Share on {}"
msgstr "Condividi su {}"

#: application/view/adv.py:109
msgid "weibo"
msgstr "weibo"

#: application/view/adv.py:110
msgid "facebook"
msgstr "facebook"

#: application/view/adv.py:112
msgid "tumblr"
msgstr "tumblr"

#: application/view/adv.py:113
msgid "Open in browser"
msgstr "Apri nel browser"

#: application/view/adv.py:114
msgid "Append qrcode of url to article"
msgstr "Aggiungi QR code dell'URL all'articolo"

#: application/view/adv.py:381 application/view/share.py:54
#: application/view/subscribe.py:250
msgid "Unknown command: {}"
msgstr "Comando sconosciuto: {}"

#: application/view/adv.py:439 application/view/adv.py:530
msgid "The format is invalid."
msgstr "Il formato non è valido."

#: application/view/adv.py:562
msgid "Authorization Error!<br/>{}"
msgstr "Errore di autorizzazione!<br/>{}"

#: application/view/adv.py:583
msgid "Success authorized by Pocket!"
msgstr "Autorizzazione riuscita tramite Pocket!"

#: application/view/adv.py:589
msgid ""
"Failed to request authorization of Pocket!<hr/>See details "
"below:<br/><br/>{}"
msgstr ""
"Autorizzazione richiesta a Pocket fallita!<hr/>Vedi i dettagli qui "
"sotto:<br/><br/>{}"

#: application/view/adv.py:610
msgid "The Instapaper service encountered an error. Please try again later."
msgstr "Il servizio Instapaper ha riscontrato un errore. Riprova più tardi."

#: application/view/adv.py:623
msgid "Request type [{}] unsupported"
msgstr "Tipo di richiesta [{}] non supportato"

#: application/view/deliver.py:82 application/view/login.py:169
#: application/view/share.py:41
msgid "The username does not exist or the email is empty."
msgstr "Il nome utente non esiste o l'e-mail è vuota."

#: application/view/deliver.py:109
msgid "The following recipes has been added to the push queue."
msgstr "Le seguenti ricette sono state aggiunte alla coda di push."

#: application/view/deliver.py:112
msgid "There are no recipes to deliver."
msgstr "Non ci sono ricette da consegnare."

#: application/view/extension.py:69
msgid "The rules parameter is invalid."
msgstr "Il parametro delle regole non è valido."

#: application/view/library.py:32
msgid "Cannot fetch data from {}, status: {}"
msgstr "Impossibile recuperare i dati da {}, stato: {}"

#: application/view/library.py:48 application/view/subscribe.py:238
#: application/view/subscribe.py:380 application/view/subscribe.py:409
#: application/view/subscribe.py:416 application/view/translator.py:30
msgid "The recipe does not exist."
msgstr "La ricetta non esiste."

#: application/view/login.py:27 application/view/login.py:76
msgid "Please use {}/{} to login at first time."
msgstr "Usa {}/{} per effettuare il primo accesso."

#: application/view/login.py:40
msgid "Username is empty."
msgstr "Il nome utente è vuoto."

#: application/view/login.py:42
msgid "The len of username reached the limit of 25 chars."
msgstr "La lunghezza del nome utente ha raggiunto il limite di 25 caratteri."

#: application/view/login.py:80
msgid "Forgot password?"
msgstr "Hai dimenticato la password?"

#: application/view/login.py:148 application/view/login.py:275
msgid "The token is wrong or expired."
msgstr "Il token è errato o scaduto."

#: application/view/login.py:151
msgid "Please input the correct username and email to reset password."
msgstr ""
"Inserisci il nome utente e l'e-mail corretti per reimpostare la password."

#: application/view/login.py:153
msgid "The email of account '{name}' is {email}."
msgstr "L'email dell'account '{name}' è {email}."

#: application/view/login.py:174
msgid "Reset password success, Please close this page and login again."
msgstr ""
"Reimpostazione della password riuscita, chiudi questa pagina e accedi di "
"nuovo."

#: application/view/login.py:177
msgid "The email you input is not associated with this account."
msgstr "L'email inserita non è associata a questo account."

#: application/view/login.py:186
msgid "The link to reset your password has been sent to your email."
msgstr ""
"Il link per reimpostare la tua password è stato inviato alla tua email."

#: application/view/login.py:187
msgid "Please check your email inbox within 24 hours."
msgstr "Controlla la tua casella di posta entro 24 ore."

#: application/view/login.py:218
msgid "The invitation code is invalid."
msgstr "Il codice di invito non è valido."

#: application/view/login.py:226
msgid ""
"Failed to create an account. Please contact the administrator for "
"assistance."
msgstr ""
"Impossibile creare un account. Contatta l'amministratore per assistenza."

#: application/view/login.py:236
msgid "Successfully created account."
msgstr "Account creato con successo."

#: application/view/login.py:247
msgid "Reset KindleEar password"
msgstr "Reimposta la password di KindleEar"

#: application/view/login.py:248
msgid "This is an automated email. Please do not reply to it."
msgstr "Questa è una email automatica. Si prega di non rispondere."

#: application/view/login.py:249
msgid "You can click the following link to reset your KindleEar password."
msgstr ""
"Puoi cliccare sul seguente link per reimpostare la tua password di "
"KindleEar."

#: application/view/reader.py:88
msgid "The article is missing?"
msgstr "L'articolo manca?"

#: application/view/reader.py:190 application/view/translator.py:121
#: application/view/translator.py:205 application/view/translator.py:287
msgid "The text is empty."
msgstr "Il testo è vuoto."

#: application/view/reader.py:239
msgid "No definitions found for '{}'."
msgstr "Nessuna definizione trovata per '{}'."

#: application/view/reader.py:240
msgid "Did you mean?"
msgstr "Intendevi?"

#: application/view/reader.py:324 application/view/reader.py:331
msgid "Failed to push: {}"
msgstr "Impossibile inviare: {}"

#: application/view/reader.py:379
msgid "Failed to create ebook."
msgstr "Impossibile creare l'ebook."

#: application/view/settings.py:131
msgid ""
"You have not yet set up your email address. Please go to the 'Account' page "
"to add your email address firstly."
msgstr ""
"Non hai ancora configurato il tuo indirizzo email. Vai alla pagina 'Account'"
" per aggiungere prima il tuo indirizzo email."

#: application/view/settings.py:215
msgid "English"
msgstr "Inglese"

#: application/view/settings.py:216
msgid "Simplified Chinese"
msgstr "Cinese Semplificato"

#: application/view/settings.py:217
msgid "Traditional Chinese"
msgstr "Cinese Tradizionale"

#: application/view/settings.py:218
msgid "French"
msgstr "Francese"

#: application/view/settings.py:219
msgid "Spanish"
msgstr "Spagnolo"

#: application/view/settings.py:220
msgid "Portuguese"
msgstr "Portoghese"

#: application/view/settings.py:221
msgid "German"
msgstr "Tedesco"

#: application/view/settings.py:222
msgid "Italian"
msgstr "Italiano"

#: application/view/settings.py:223
msgid "Japanese"
msgstr "Giapponese"

#: application/view/settings.py:224
msgid "Russian"
msgstr "Russo"

#: application/view/settings.py:225
msgid "Turkish"
msgstr "Turco"

#: application/view/settings.py:226
msgid "Korean"
msgstr "Coreano"

#: application/view/settings.py:227
msgid "Arabic"
msgstr "Arabo"

#: application/view/settings.py:228
msgid "Czech"
msgstr "Ceco"

#: application/view/settings.py:229
msgid "Dutch"
msgstr "Olandese"

#: application/view/settings.py:230
msgid "Greek"
msgstr "Greco"

#: application/view/settings.py:231
msgid "Hindi"
msgstr "Hindi"

#: application/view/settings.py:232
msgid "Malaysian"
msgstr "Malesiano"

#: application/view/settings.py:233
msgid "Bengali"
msgstr "Bengalese"

#: application/view/settings.py:234
msgid "Persian"
msgstr "Persiano"

#: application/view/settings.py:235
msgid "Urdu"
msgstr "Urdu"

#: application/view/settings.py:236
msgid "Swahili"
msgstr "Swahili"

#: application/view/settings.py:237
msgid "Vietnamese"
msgstr "Vietnamita"

#: application/view/settings.py:238
msgid "Punjabi"
msgstr "Punjabi"

#: application/view/settings.py:239
msgid "Javanese"
msgstr "Giapponese"

#: application/view/settings.py:240
msgid "Tagalog"
msgstr "Tagalog"

#: application/view/settings.py:241
msgid "Hausa"
msgstr "Hausa"

#: application/view/settings.py:242
msgid "Thai"
msgstr "Thailandese"

#: application/view/settings.py:243
msgid "Polish"
msgstr "Polacco"

#: application/view/settings.py:244
msgid "Romanian"
msgstr "Rumeno"

#: application/view/settings.py:245
msgid "Hungarian"
msgstr "Ungherese"

#: application/view/settings.py:246
msgid "Swedish"
msgstr "Svedese"

#: application/view/settings.py:247
msgid "Hebrew"
msgstr "Ebraico"

#: application/view/settings.py:248
msgid "Norwegian"
msgstr "Norvegese"

#: application/view/settings.py:249
msgid "Finnish"
msgstr "Finlandese"

#: application/view/settings.py:250
msgid "Danish"
msgstr "Danese"

#: application/view/settings.py:251
msgid "Ukrainian"
msgstr "Ucraino"

#: application/view/settings.py:252
msgid "Tamil"
msgstr "Tamil"

#: application/view/settings.py:253
msgid "Marathi"
msgstr "Marathi"

#: application/view/settings.py:254
msgid "Burmese"
msgstr "Birmano"

#: application/view/settings.py:255
msgid "Amharic"
msgstr "Amarico"

#: application/view/settings.py:256
msgid "Azerbaijani"
msgstr "Azerbaigiano"

#: application/view/settings.py:257
msgid "Kazakh"
msgstr "Kazako"

#: application/view/settings.py:258
msgid "Serbian"
msgstr "Serbo"

#: application/view/settings.py:259
msgid "Croatian"
msgstr "Croato"

#: application/view/settings.py:260
msgid "Slovak"
msgstr "Slovacco"

#: application/view/settings.py:261
msgid "Bulgarian"
msgstr "Bulgaro"

#: application/view/settings.py:262
msgid "Icelandic"
msgstr "Islandese"

#: application/view/settings.py:263
msgid "Lithuanian"
msgstr "Lituano"

#: application/view/settings.py:264
msgid "Latvian"
msgstr "Lettone"

#: application/view/settings.py:265
msgid "Estonian"
msgstr "Estone"

#: application/view/settings.py:266
msgid "Macedonian"
msgstr "Macedone"

#: application/view/settings.py:267
msgid "Albanian"
msgstr "Albanese"

#: application/view/settings.py:268
msgid "Galician"
msgstr "Galiziano"

#: application/view/settings.py:269
msgid "Welsh"
msgstr "Gallese"

#: application/view/settings.py:270
msgid "Basque"
msgstr "Basco"

#: application/view/settings.py:271
msgid "Nepali"
msgstr "Nepalese"

#: application/view/share.py:60
msgid "There is no {} email yet."
msgstr "Non c'è ancora nessun'email {}."

#: application/view/share.py:108 application/view/share.py:133
#: application/view/share.py:155 application/view/share.py:177
msgid "Saved to your {} account."
msgstr "Salvato nel tuo account {}."

#: application/view/share.py:111 application/view/share.py:129
#: application/view/share.py:158 application/view/share.py:179
msgid "Failed save to {}."
msgstr "Impossibile salvare su {}."

#: application/view/share.py:112 application/view/share.py:130
#: application/view/share.py:159 application/view/share.py:180
msgid "Reason :"
msgstr "Motivo:"

#: application/view/share.py:121
msgid "Unauthorized {} account!"
msgstr "Account {} non autorizzato!"

#: application/view/share.py:134
msgid "See details below:"
msgstr "Vedi i dettagli di seguito:"

#: application/view/share.py:157
msgid "Unknown: {}"
msgstr "Sconosciuto: {}"

#: application/view/subscribe.py:81 application/view/subscribe.py:167
msgid "Duplicated subscription!"
msgstr "Abbonamento duplicato!"

#: application/view/subscribe.py:126
msgid "The Title or Url is empty."
msgstr "Il Titolo o l'URL è vuoto."

#: application/view/subscribe.py:139
msgid "Failed to fetch the recipe."
msgstr "Impossibile recuperare la ricetta."

#: application/view/subscribe.py:153 application/view/subscribe.py:331
msgid "Failed to save the recipe. Error:"
msgstr "Impossibile salvare la ricetta. Errore:"

#: application/view/subscribe.py:195
msgid "The Rss does not exist."
msgstr "Il feed RSS non esiste."

#: application/view/subscribe.py:278
msgid "You can only delete the uploaded recipe."
msgstr "Puoi eliminare solo la ricetta caricata."

#: application/view/subscribe.py:283
msgid "The recipe have been subscribed, please unsubscribe it before delete."
msgstr ""
"La ricetta è già stata sottoscritta, disiscriviti prima di eliminarla."

#: application/view/subscribe.py:304 application/view/translator.py:51
#: application/view/translator.py:104 application/view/translator.py:117
#: application/view/translator.py:140 application/view/translator.py:188
#: application/view/translator.py:201 application/view/translator.py:230
#: application/view/translator.py:270 application/view/translator.py:283
msgid "This recipe has not been subscribed to yet."
msgstr "Questa ricetta non è ancora stata sottoscritta."

#: application/view/subscribe.py:318
msgid "Can not read uploaded file, Error:"
msgstr "Impossibile leggere il file caricato, Errore:"

#: application/view/subscribe.py:326
msgid ""
"Failed to decode the recipe. Please ensure that your recipe is saved in "
"utf-8 encoding."
msgstr ""
"Impossibile decodificare la ricetta. Assicurati che la ricetta sia salvata "
"con la codifica utf-8."

#: application/view/subscribe.py:349
msgid "Cannot find any subclass of BasicNewsRecipe."
msgstr "Impossibile trovare una sottoclasse di BasicNewsRecipe."

#: application/view/subscribe.py:354
msgid "The recipe is already in the library."
msgstr "La ricetta è già nella libreria."

#: application/view/subscribe.py:387
msgid "The login information for this recipe has been cleared."
msgstr "Le informazioni di accesso per questa ricetta sono state cancellate."

#: application/view/subscribe.py:391
msgid "The login information for this recipe has been saved."
msgstr "Le informazioni di accesso per questa ricetta sono state salvate."

#: application/view/translator.py:81 application/view/translator.py:165
msgid "The api key is required."
msgstr "La chiave API è richiesta."
