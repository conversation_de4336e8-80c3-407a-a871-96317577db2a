
services:
  kindleear:
    container_name: kindleear
    image: kindleear/kindleear
    restart: always
    volumes:
      - ./data/:/data/
    expose:
      - "8000"
    networks:
      - web_network
    environment:
       APP_ID: kindleear
       #DOMAIN with http or https prefix
       APP_DOMAIN: http://example.com
       LOG_LEVEL: warning
       TZ: Etc/GMT+0
       USE_DOCKER_LOGS: ${USE_DOCKER_LOGS:-}
       
  caddy:
    container_name: caddy
    image: caddy:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    environment:
      #DOMAIN without http and https prefix
      DOMAIN: example.com
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - ./caddy/:/data/caddy/
    depends_on:
      - kindleear
    networks:
      - web_network

  mailfix:
   container_name: mailfix
   image: kindleear/mailfix
   restart: unless-stopped
   ports:
     - "25:25"
   depends_on:
     - kindleear
     - caddy
   environment:
     #change DOMAIN to your email domain, without http and https prefix
     DOMAIN: example.com
     URL: http://kindleear:8000/mail
   networks:
     - web_network

networks:
  web_network:
    driver: bridge
