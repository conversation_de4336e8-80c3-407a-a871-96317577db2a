{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Reset password") }} - KindleEar</title>
{% endblock -%}

{% block header_loginfo -%}
{% endblock %}

{% block menubar -%}
{% endblock -%}

{% block content -%}
<div class="main">
  {% if tips -%}
  <div class="notice-box">{{tips|safe}}</div>
  {% endif -%}
  <form action="" method="POST" class="pure-form pure-form-aligned">
    <div class="pure-control-group">
      <label for="username">{{_("Username")}}</label>
      <input name="username" type="text" placeholder="{{_('Username')}}" class="pure-u-1 pure-u-sm-1-2" 
      {% if userName %} value="{{userName}}" {% endif %} 
      {% if firstStep %} autofocus {% else %} readonly {% endif %} />
    </div>
    {% if firstStep -%}
    <div class="pure-control-group">
      <label for="email">{{_("Email")}}</label>
      <input name="email" type="email" class="pure-u-1 pure-u-sm-1-2" />
    </div>
    {% else -%}
    <div class="pure-control-group">
      <label for="new_p1">{{ _("New password") }}</label>
      <input name="new_p1" type="password" placeholder="{{ _('New password') }}" class="pure-u-1 pure-u-sm-1-2" autofocus />
    </div>
    <div class="pure-control-group">
      <label for="new_p2">{{ _("Confirm password") }}</label>
      <input name="new_p2" type="password" placeholder="{{ _('Confirm password') }}" class="pure-u-1 pure-u-sm-1-2" />
    </div>
    <input name="token" type="hidden" value="{{token}}" />
    {% endif -%}
    <div class="pure-control-group" style="text-align:center;">
      <button type="submit" class="pure-button pure-button-primary pure-input-rounded">{{_("Reset password")}}</button>
    </div>
  </form>
</div>
{% endblock -%}