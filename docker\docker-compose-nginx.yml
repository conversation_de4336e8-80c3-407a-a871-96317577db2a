
services:
  kindleear:
    container_name: kindleear
    image: kindleear/kindleear
    restart: always
    volumes:
      - ./data/:/data/
    expose:
      - "8000"
    networks:
      - web_network
    environment:
       APP_ID: kindleear
       APP_DOMAIN: http://example.com
       LOG_LEVEL: warning
       TZ: Etc/GMT+0
       USE_DOCKER_LOGS: ${USE_DOCKER_LOGS:-}
       
  nginx:
    container_name: nginx
    image: nginx:stable-alpine3.17-slim
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./data/:/var/log/nginx/
      # uncomment this two lines if https is need
      #- ./data/fullchain.pem:/etc/nginx/ssl/fullchain.pem:ro
      #- ./data/privkey.pem:/etc/nginx/ssl/privkey.pem:ro
    depends_on:
      - kindleear
    networks:
      - web_network

  mailfix:
   container_name: mailfix
   image: kindleear/mailfix
   restart: unless-stopped
   ports:
     - "25:25"
   depends_on:
     - kindleear
     - nginx
   environment:
     #change DOMAIN to your email domain, without http and https prefix
     DOMAIN: example.com
     URL: http://kindleear:8000/mail
   networks:
     - web_network

networks:
  web_network:
    driver: bridge
