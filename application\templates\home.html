{% extends "base.html" %}
{% block titleTag -%}
<title>{{ _("Home") }} - KindleEar</title>
{% endblock -%}

{% block content -%}
<div class="banner">
  <div class="banner-head">
    {% if demoMode -%}
    <h2>{{_("You are in DEMO mode. Logging out will delete all data.")}}</h2>
    {% else -%}
    <h1>{{_("Sharing Joyful News Every Step of the Way")}}</h1>
    {% endif -%}
      {% if session.get('userName') -%}
      <a class="pure-button pure-button-primary" href="/my" style="margin-bottom: 10px;">{{_("Feeds")}}</a>
      <a class="pure-button pure-button-primary" href="/logout" style="margin-left:.6em; margin-bottom: 10px;">{{_('Logout')}}</a>
      {% else -%}
    <form class="pure-form" action="/login" method="POST">
      <input type="text" name="username" placeholder="{{_('Username')}}" class="pure-input-rounded pure-u-1 pure-u-sm-1-4 pure-u-lg-1-6" autofocus />
      <input type="password" name="password" placeholder="{{_('Password')}}" class="pure-input-rounded pure-u-1 pure-u-sm-1-4 pure-u-lg-1-6"/>
      <button type="submit" class="pure-button pure-button-primary pure-input-rounded pure-u-1 pure-u-sm-1-6 pure-u-lg-1-12">{{_('Login')}}</button>
    </form>
    {% endif -%}
  </div>
</div>

<div class="main l-content">
  <div class="information pure-g">
    <div class="pure-u-1 pure-u-md-1-2">
      <div class="l-box">
        <h3 class="information-head">{{_("Inherited From Calibre")}}</h3>
        <p>
        {% autoescape off %}
          {{_("Empowered by %(calibre)s, you can easily create e-books on a Python-supported online platform and seamlessly transfer them to your e-reader or other reading devices.", calibre='<a href="https://calibre-ebook.com" target="_blank">Calibre</a>')|safe}}
        {% endautoescape %}
        </p>
      </div>
    </div>

    <div class="pure-u-1 pure-u-md-1-2">
      <div class="l-box">
        <h3 class="information-head">{{_("Share Your Ideas")}}</h3>
        <p>
        {% autoescape off %}
          {{_("With the open-source %(kindleear)s application, you can set up your own server to deliver daily news feeds to your e-reader and effortlessly share the service with friends.", kindleear='<a href="https://github.com/cdhigh/kindleear" target="_blank">KindleEar</a>')|safe}}
        {% endautoescape %}
        </p>
      </div>
    </div>
  </div>
</div>
{% endblock -%}